# Thinkertags Web App

Frontend for thinkertags.com built with React, Vite, and Tailwind CSS.

## Setup

```bash
npm install
npm run dev
```

### Deployment
Automatically deployed via GitHub Actions when pushing to main branch. Pipeline:

1. Builds application
2. Uploads to S3
3. Invalidates CloudFront cache

### Environment Variables
```bash
VITE_API_URL=https://api.thinkertags.com
VITE_COGNITO_USER_POOL_ID=xxx
VITE_COGNITO_CLIENT_ID=xxx
```


## Copyright and License
©  Thinkertags and Carl-Gustaf <PERSON> 2025 All rights reserved. 
This codebase is proprietary and confidential. No part of this codebase may be reproduced, distributed, or transmitted in any form or by any means  without the prior written permission of its owner.
For usage rights, please contact the owner <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>.