# Thinkertags Article Technical Guide

This guide provides technical instructions for formatting, structuring, and publishing research and insights articles (.md files) for Thinkertags. Follow these guidelines to maintain technical consistency across all published content. For content principles and writing guidance, refer to the companion document `article-writing-guide-content.md`.

## File Structure and Naming

- Store blog posts as markdown files in the `public/blog/[post-name]/[post-name].md` format
- Place media files in the same folder as the markdown file
- Store author images directly under `/blog/` with `carl-gustaf-ydstrom.jpeg` as the default

## Markdown Formatting

### Basic Structure

```markdown
# Article Title

*Published: Month Day, Year*

**Author: Author Name**

![author-image](/images/blog/author-image.jpeg)

![banner-image](/images/blog/banner-image.jpeg)

## Introduction

[dropcap]Your introduction paragraph goes here. This will display with a drop cap for the first letter.

## Main Section Title

Regular paragraph text goes here.

**Bold Subsection:**
- Bullet point 1
- Bullet point 2

## Another Main Section

[dropcap]If you want another paragraph to have a drop cap, use the [dropcap] tag again.

***

*Footer text or series information goes here.*
```

### Special Formatting Elements

#### Drop Caps

Use the `[dropcap]` tag at the beginning of paragraphs where you want the first letter to appear as a drop cap (larger first letter that spans two lines of text). This is typically used for:

- The first paragraph of the introduction
- The first paragraph after major section breaks
- Paragraphs that begin new important thoughts

Example:
```markdown
[dropcap]Managing physical assets at scale isn't just complex—it directly impacts your bottom line, customer satisfaction, and operational efficiency.
```

#### Banner Images

- Banner images appear at the top of posts without rounded corners or background shadows
- Format: `![banner-image](/images/blog/image-name.jpeg)`
- **Recommended resolution**: 1600x900 pixels (minimum 1200x675 pixels)
- **Aspect ratio**: 16:9 widescreen format
- Images should be high quality (at least 72 DPI) and optimized for web (typically JPEG format)
- No fallback logic exists - if no banner image is specified, none will be displayed
- Ensure proper spacing with top margin on smaller screens to prevent navbar overlap

#### Lists and Formatting

- Use bullet points (`-`) for unordered lists
- Use numbers (`1.`) for ordered lists
- Use `**text**` for bold text, especially for subsection titles
- Use `*text*` for italic text, typically for dates and footer information
- Use `***` for horizontal rule separators

#### Source Attribution

When citing sources, incorporate the attribution directly into the text rather than using numbered references.

Example:
```markdown
According to a 2023 study by Smith and Johnson, proper asset tagging can reduce search time by up to 35% and improve inventory accuracy by over 25%.
```

Or:
```markdown
The 2024 Thinkertags Research Report found that organizations implementing digital tracking solutions see an average 27% improvement in service response times.
```

## Content Guidelines

### Introduction

- Begin with a compelling hook that addresses a pain point or opportunity
- Use the `[dropcap]` tag for the first paragraph
- Keep the introduction concise (2-3 paragraphs)
- Don't display the "## Introduction" heading in the final rendering

### Main Content

- Organize content with clear section headings (## Level 2 headings)
- Use subsections with bold text rather than ### Level 3 headings when possible
- Include practical applications or examples for each main point
- Use bullet points for actionable items or lists
- Support claims with specific data or case studies when available

### Conclusion

- Summarize key points
- Include a call to action
- Reference Thinkertags solutions where relevant
- Consider using the `[dropcap]` tag for the first paragraph of the conclusion

### Voice and Tone

- Write in a professional but conversational tone
- Use second-person ("you") to directly address the reader
- Balance technical accuracy with accessibility
- Focus on practical insights rather than theoretical concepts
- Maintain left-aligned text throughout

### Cross-Referencing Articles

- When creating a new article, review and reference existing articles when relevant
- Use the format `[Article Title](https://thinkertags.com/research-and-insights/{md-name})` for internal links
- Create a cohesive narrative across articles by building on previously established concepts
- Consider adding a "Related Articles" section at the end of posts that cover similar topics
- When referencing case studies or examples mentioned in other articles, link to the original source

## Technical Specifications

- Use Playfair Display (serif) font for titles
- Ensure text is left-aligned
- Respect newlines in markdown content to maintain proper spacing
- Include appropriate metadata for audio playback if applicable

## Example Paragraph with Drop Cap

```markdown
[dropcap]Managing physical assets at scale isn't just complex—it directly impacts your bottom line, customer satisfaction, and operational efficiency. Yet many organizations continue struggling with outdated systems that create unnecessary friction. After years working with service companies across industries, we've identified three foundational principles that separate exceptional asset management operations from mediocre ones. These principles aren't trendy tactics—they're timeless approaches that work regardless of your industry or asset type.
```

## Example "Related Articles" Section

```markdown
## Related Articles

- [Top 3 Timeless Principles for Better Asset Management](https://thinkertags.com/research-and-insights/top-3-timeless-principles)
- [How QR Codes Are Transforming Field Service](https://thinkertags.com/research-and-insights/qr-codes-field-service)
- [The ROI of Digital Asset Management](https://thinkertags.com/research-and-insights/roi-digital-asset-management)
```

## Example Article Structure with Source Attribution

```markdown
# The Impact of Digital Asset Tracking on Service Efficiency

*Published: June 15, 2025*

**Author: Gustaf Ydström**

![author-image](/images/blog/carl-gustaf-ydstrom.jpeg)

![banner-image](/images/blog/digital-asset-tracking-banner.jpeg)

## Introduction

[dropcap]Digital asset tracking technologies have revolutionized how service organizations manage their equipment and inventory. According to the 2025 Thinkertags Research Report, organizations implementing comprehensive digital tracking solutions see an average 27% improvement in service response times and a 32% reduction in lost or misplaced equipment.

## Key Benefits of Digital Asset Tracking

Service organizations implementing digital asset tracking systems report several measurable benefits:

1. **Reduced Search Time**: The Technical Service Council's 2024 Annual Technician Productivity Report found that technicians spend 35% less time locating equipment when assets have digital identifiers with precise location data.

2. **Improved Inventory Accuracy**: A 2023 study by Zhang and colleagues published in Operations Research Quarterly showed that digital tracking improves inventory accuracy from an industry average of 63% to over 95%.

3. **Enhanced Maintenance Compliance**: Johnson and Smith's 2024 research in the Journal of Service Management revealed that organizations report 42% better compliance with preventive maintenance schedules when using digital tracking systems with automated alerts.

## Implementation Considerations

While the benefits are clear, successful implementation requires careful planning. According to the 2025 Field Service Digital Transformation Survey, organizations that take a phased approach to implementation are 3.2 times more likely to report successful outcomes than those attempting full-scale deployment at once.

## Related Articles

- [Top 3 Timeless Principles for Better Asset Management](https://thinkertags.com/research-and-insights/top-3-timeless-principles)
- [QR Codes vs RFID: Choosing the Right Technology](https://thinkertags.com/research-and-insights/qr-vs-rfid)
```

This guide will evolve over time as we refine our content strategy and technical implementation. Additional prompt artifacts for different purposes may be added in the future.
