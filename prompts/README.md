# Thinkertags Prompt Instructions

This directory contains prompt instructions and artifacts that help maintain consistency across various content creation and development processes at Thinkertags. These documents serve as guides for both human writers and AI assistants to ensure alignment with Thinkertags' standards, voice, and technical requirements.

## Available Guides

### Article Writing

- [Article Technical Guide](article-writing-guide-technical.md) - Technical instructions for formatting, structuring, and publishing research and insights articles
- [Article Content Principles](article-writing-guide-content.md) - Core writing principles, voice guidelines, and content concepts for creating compelling articles

## Purpose

These prompt artifacts are designed to:

1. **Ensure Consistency** - Maintain a unified approach across all content
2. **Preserve Knowledge** - Document best practices and requirements
3. **Streamline Creation** - Provide clear guidelines for efficient content development
4. **Enable Collaboration** - Allow multiple contributors to align with Thinkertags standards

## Evolution

These guides will evolve over time as we refine our content strategy, technical implementation, and brand voice. Future prompt artifacts may be added for different purposes, potentially organized into subdirectories by category.

## Usage

When creating new content or developing features, reference the appropriate guide to ensure alignment with established standards. These documents serve as living references rather than rigid rules, and feedback for improvement is always welcome.
