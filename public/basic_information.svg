<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Interactive ambient background glow with φ (phi) ratio precision -->
  <rect x="1" y="1" width="14" height="14" rx="7" fill="url(#ambienceGradient)" opacity="0.15" filter="url(#softGlow)">
    <animate attributeName="opacity" values="0.15;0.18;0.15" dur="4s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
  </rect>
  
  <!-- Ultra-lux crystalline backdrop with subtle breathing animation -->
  <g opacity="0.04" filter="url(#crystallineEffect)">
    <path d="M8 2.5L3 6.5L8 13.5L13 6.5L8 2.5Z" fill="url(#crystalGradient)">
      <animate attributeName="opacity" values="1;0.85;1" dur="7s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
    </path>
  </g>
  
  <!-- Dynamic constellation pattern with golden ratio positioning -->
  <g opacity="0.07" filter="url(#subtleStar)">
    <path d="M4 4l0.5 2L2 6.5 4.5 8 2 9.5 4.5 10l-0.5 2 2-1.5 2 1.5-0.5-2 2-0.5-2.5-1.5 2.5-1.5-2-0.5 0.5-2-2 1.5z" fill="url(#starGradient)">
      <animate attributeName="opacity" values="0.8;1;0.8" dur="8s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
      <animateTransform attributeName="transform" type="rotate" from="0 4 6" to="1.5 4 6" dur="15s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1" additive="sum"/>
    </path>
    <path d="M12 3l0.3 1.2-1.5 0.3 1.5 0.9-1.5 0.9 1.5 0.3L12 8l1.2-0.9 1.2 0.9-0.3-1.2 1.5-0.3-1.5-0.9 1.5-0.9-1.5-0.3L14.4 3l-1.2 0.9z" fill="url(#starGradient)">
      <animate attributeName="opacity" values="0.6;0.9;0.6" dur="6s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
      <animateTransform attributeName="transform" type="rotate" from="0 12 4.5" to="-1.2 12 4.5" dur="12s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1" additive="sum"/>
    </path>
  </g>

  <!-- Premium resonance rings with ultra-subtle parallax effect -->
  <g opacity="0.05">
    <circle cx="8" cy="8" r="6.85" stroke="url(#resonanceGradient)" stroke-width="0.01" stroke-dasharray="0.1 0.3">
      <animateTransform attributeName="transform" type="rotate" from="0 8 8" to="360 8 8" dur="120s" repeatCount="indefinite" additive="sum"/>
      <animate attributeName="opacity" values="0.4;0.7;0.4" dur="15s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
    </circle>
    <circle cx="8" cy="8" r="6.65" stroke="url(#resonanceGradient)" stroke-width="0.01" stroke-dasharray="0.15 0.35">
      <animateTransform attributeName="transform" type="rotate" from="0 8 8" to="-360 8 8" dur="90s" repeatCount="indefinite" additive="sum"/>
      <animate attributeName="opacity" values="0.5;0.8;0.5" dur="18s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
    </circle>
  </g>

  <!-- Premium luxury base with floating effect and precision φ ratio -->
  <g filter="url(#luxuryFloatingEffect)">
    <!-- Luxury base circle with sophisticated platinum gradient and advanced breathing animation -->
    <circle cx="8" cy="8" r="6" fill="url(#platinumBaseGradient)" filter="url(#subtleShadow)">
      <animate attributeName="r" values="6;6.03;6" dur="3s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
      <animate attributeName="filter" values="url(#subtleShadow);url(#subtleShadowIntense);url(#subtleShadow)" dur="3s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
    </circle>
    <circle cx="8" cy="8" r="6" stroke="url(#outerRimGradient)" stroke-width="0.4" stroke-opacity="0.8">
      <animate attributeName="stroke-opacity" values="0.8;0.9;0.8" dur="3s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
    </circle>
  </g>
  
  <!-- Ultra-premium inner circle with advanced polished effect and micro-texture -->
  <circle cx="8" cy="8" r="5.5" fill="url(#innerCircleGradient)" filter="url(#innerPolish)"/>
  <circle cx="8" cy="8" r="5.5" stroke="url(#innerRimGradient)" stroke-width="0.3"/>
  
  <!-- Precision micro pattern overlay with dynamic texture behavior -->
  <circle cx="8" cy="8" r="5.2" fill="url(#microPatternGradient)" opacity="0.03" filter="url(#dynamicNoiseTexture)">
    <animate attributeName="opacity" values="0.03;0.05;0.03" dur="5s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
  </circle>
  
  <!-- Designer's marks at precise golden section points -->
  <g>
    <path d="M10.5 5.115a0.1 0.1 0 1 0 0 0.2 0.1 0.1 0 1 0 0-0.2z" fill="#E0C9A6" opacity="0.5"/>
    <path d="M5.382 10.618a0.06 0.06 0 1 0 0 0.12 0.06 0.06 0 1 0 0-0.12z" fill="#E0C9A6" opacity="0.4"/>
    <path d="M10.927 10.309a0.04 0.04 0 1 0 0 0.08 0.04 0.04 0 1 0 0-0.08z" fill="#E0C9A6" opacity="0.4">
      <animate attributeName="opacity" values="0.4;0;0.4" dur="7s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
    </path>
  </g>
  
  <!-- Elegant information symbol with ultra-luxury gold finish and interactive pulse -->
  <g>
    <!-- Dimensional gold base with dynamic light movement -->
    <circle cx="8" cy="5.5" r="1.15" fill="url(#goldBaseGradient)" filter="url(#goldDepth)"/>
    
    <!-- Main gold dot with premium light behavior -->
    <circle cx="8" cy="5.5" r="1.1" fill="url(#goldDotGradient)" filter="url(#goldGlow)">
      <animate attributeName="r" values="1.1;1.14;1.1" dur="2s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
      <animate attributeName="filter" values="url(#goldGlow);url(#goldGlowIntense);url(#goldGlow)" dur="2s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
    </circle>
    
    <!-- Ultra-fine gold highlight for dimensional appearance -->
    <path d="M7.65 5.2C7.65 5.2 7.8 5 8.05 5C8.3 5 8.45 5.2 8.45 5.2" stroke="white" stroke-width="0.08" stroke-linecap="round" opacity="0.6">
      <animate attributeName="opacity" values="0.6;0.8;0.6" dur="2s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
    </path>
  </g>
  
  <!-- Dynamic stem with sophisticated interaction -->
  <g>
    <!-- Gold stem shadow for dimensional depth -->
    <path d="M8 7.25V11.55" stroke="url(#goldStemShadowGradient)" stroke-width="1.4" stroke-linecap="round" filter="url(#goldStemShadow)" opacity="0.7"/>
    
    <!-- Main premium gold stem -->
    <path d="M8 7.2V11.5" stroke="url(#goldStemGradient)" stroke-width="1.2" stroke-linecap="round" filter="url(#goldShadow)">
      <animate attributeName="stroke-width" values="1.2;1.25;1.2" dur="3s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
    </path>
    
    <!-- Subtle pulse highlight for ultra-luxury movement -->
    <path d="M8 7.2V11.5" stroke="white" stroke-width="0.15" stroke-linecap="round" opacity="0.3" filter="url(#pulseShadow)">
      <animate attributeName="opacity" values="0.3;0.5;0.3" dur="1.5s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
      <animate attributeName="stroke-width" values="0.15;0.18;0.15" dur="1.5s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
    </path>
    
    <!-- Micro-highlights for dimensional appearance -->
    <path d="M8 7.5L8 11.2" stroke="white" stroke-width="0.05" stroke-linecap="round" opacity="0.5" stroke-dasharray="0.1 0.4">
      <animate attributeName="stroke-dashoffset" values="0;0.5" dur="1.2s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1"/>
    </path>
  </g>
  
  <!-- Advanced light reflections with interactive animations -->
  <g>
    <!-- Primary dimensional highlight with light movement -->
    <ellipse cx="5.8" cy="5.8" rx="0.5" ry="1.2" transform="rotate(-45 5.8 5.8)" fill="url(#lightReflectionGradient)" fill-opacity="0.4">
      <animateTransform attributeName="transform" type="rotate" from="-45 5.8 5.8" to="-48 5.8 5.8" dur="4s" repeatCount="indefinite" additive="sum" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
      <animate attributeName="fill-opacity" values="0.4;0.6;0.4" dur="4s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
      <animate attributeName="ry" values="1.2;1.3;1.2" dur="4s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
    </ellipse>
    
    <!-- Secondary highlight with independent movement -->
    <ellipse cx="10.2" cy="10.2" rx="0.4" ry="1" transform="rotate(-45 10.2 10.2)" fill="url(#lightReflectionGradient)" fill-opacity="0.3">
      <animateTransform attributeName="transform" type="rotate" from="-45 10.2 10.2" to="-41 10.2 10.2" dur="5s" repeatCount="indefinite" additive="sum" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
      <animate attributeName="fill-opacity" values="0.3;0.5;0.3" dur="5s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
      <animate attributeName="rx" values="0.4;0.45;0.4" dur="5s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
    </ellipse>
    
    <!-- Tertiary curved light path reflection -->
    <path d="M8 2.5C8 2.5 9.5 3 11.5 3.5" stroke="url(#curvedLightGradient)" stroke-width="0.2" stroke-opacity="0.5" stroke-linecap="round">
      <animate attributeName="stroke-opacity" values="0.5;0.7;0.5" dur="3s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
      <animate attributeName="stroke-width" values="0.2;0.25;0.2" dur="3s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
      <animateTransform attributeName="transform" type="rotate" from="0 8 8" to="3 8 8" dur="9s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1" additive="sum"/>
    </path>
    
    <!-- Additional dynamic light flares -->
    <path d="M4 8.5C4 8.5 5 9.2 6.5 9.6" stroke="url(#curvedLightGradient)" stroke-width="0.15" stroke-opacity="0.3" stroke-linecap="round">
      <animate attributeName="stroke-opacity" values="0.3;0.5;0.3" dur="4s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
      <animate attributeName="d" values="M4 8.5C4 8.5 5 9.2 6.5 9.6;M4 8.7C4 8.7 5.2 9.5 6.7 9.8;M4 8.5C4 8.5 5 9.2 6.5 9.6" dur="7s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
    </path>
  </g>
  
  <!-- Ultra-premium dynamic sparkling effect with precision timing -->
  <g filter="url(#advancedSparkleFilter)">
    <!-- Primary sparkle with variable opacity -->
    <circle cx="9.5" cy="4.2" r="0.055" fill="white" opacity="0.8">
      <animate attributeName="opacity" values="0.8;0;0.8" dur="2.5s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
      <animate attributeName="r" values="0.055;0.035;0.055" dur="2.5s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
    </circle>
    
    <!-- Secondary sparkle with delay -->
    <circle cx="6" cy="10.8" r="0.045" fill="white" opacity="0.6">
      <animate attributeName="opacity" values="0.6;0;0.6" dur="3.7s" repeatCount="indefinite" begin="1s" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
      <animate attributeName="r" values="0.045;0.025;0.045" dur="3.7s" repeatCount="indefinite" begin="1s" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
    </circle>
    
    <!-- Tertiary sparkle with alternate timing -->
    <circle cx="10.7" cy="8.3" r="0.04" fill="white" opacity="0.7">
      <animate attributeName="opacity" values="0.7;0;0.7" dur="3s" repeatCount="indefinite" begin="0.5s" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
      <animate attributeName="r" values="0.04;0.02;0.04" dur="3s" repeatCount="indefinite" begin="0.5s" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
    </circle>
    
    <!-- Micro sparkles for enhanced luxury -->
    <circle cx="7.3" cy="3.9" r="0.02" fill="white" opacity="0.6">
      <animate attributeName="opacity" values="0.6;0;0.6" dur="5s" repeatCount="indefinite" begin="2.5s" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
    </circle>
    <circle cx="4.8" cy="7.2" r="0.015" fill="white" opacity="0.5">
      <animate attributeName="opacity" values="0.5;0;0.5" dur="4.2s" repeatCount="indefinite" begin="1.2s" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
    </circle>
    <circle cx="11.2" cy="11.8" r="0.018" fill="white" opacity="0.4">
      <animate attributeName="opacity" values="0.4;0;0.4" dur="6.5s" repeatCount="indefinite" begin="3s" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
    </circle>
  </g>
  
  <!-- Premium accent rings with sophisticated rotation -->
  <g>
    <!-- Primary accent ring with precise timing -->
    <circle cx="8" cy="8" r="5.8" stroke="url(#accentRingGradient)" stroke-width="0.1" stroke-opacity="0.7">
      <animateTransform attributeName="transform" type="rotate" from="0 8 8" to="360 8 8" dur="60s" repeatCount="indefinite" additive="sum"/>
      <animate attributeName="stroke-opacity" values="0.7;0.8;0.7" dur="30s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
    </circle>
    
    <!-- Secondary accent ring with counter-rotation -->
    <circle cx="8" cy="8" r="6.2" stroke="url(#outerAccentGradient)" stroke-width="0.1" stroke-opacity="0.5">
      <animateTransform attributeName="transform" type="rotate" from="0 8 8" to="-360 8 8" dur="80s" repeatCount="indefinite" additive="sum"/>
      <animate attributeName="stroke-opacity" values="0.5;0.6;0.5" dur="40s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
    </circle>
    
    <!-- Additional precision accent details with φ ratio positions -->
    <circle cx="8" cy="8" r="5.5" stroke="url(#innerAccentGradient)" stroke-width="0.03" stroke-dasharray="0.3 0.5" stroke-opacity="0.25">
      <animateTransform attributeName="transform" type="rotate" from="0 8 8" to="360 8 8" dur="30s" repeatCount="indefinite"/>
      <animate attributeName="stroke-opacity" values="0.25;0.35;0.25" dur="8s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
      <animate attributeName="stroke-dasharray" values="0.3 0.5;0.4 0.6;0.3 0.5" dur="20s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
    </circle>
    
    <!-- Outermost accent ring with variable dash pattern -->
    <circle cx="8" cy="8" r="6.5" stroke="url(#outerAccentGradient)" stroke-width="0.02" stroke-dasharray="0.2 0.6" stroke-opacity="0.2">
      <animateTransform attributeName="transform" type="rotate" from="0 8 8" to="-360 8 8" dur="40s" repeatCount="indefinite"/>
      <animate attributeName="stroke-dasharray" values="0.2 0.6;0.1 0.7;0.2 0.6" dur="25s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
    </circle>
  </g>
  
  <!-- Luxury flare effect with precise movement -->
  <g opacity="0.3" filter="url(#flareFilter)">
    <path d="M3.5 8C3.5 8 5 9 8 9C11 9 12.5 8 12.5 8" stroke="url(#flareGradient)" stroke-width="0.2" stroke-linecap="round" opacity="0">
      <animate attributeName="opacity" values="0;0.7;0" dur="7s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
      <animateTransform attributeName="transform" type="rotate" from="-20 8 8" to="20 8 8" dur="7s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"/>
    </path>
  </g>
  
  <!-- Premium definitions with luxury effects -->
  <defs>
    <!-- Enhanced ambience gradient for sophisticated presence -->
    <radialGradient id="ambienceGradient" cx="8" cy="8" r="10" gradientUnits="userSpaceOnUse">
      <stop offset="0.2" stop-color="#F5E1C5"/>
      <stop offset="0.45" stop-color="#F0D9B6"/>
      <stop offset="0.65" stop-color="#D0B996"/>
      <stop offset="0.8" stop-color="#A78D67"/>
      <stop offset="1" stop-color="#7A654C"/>
    </radialGradient>
    
    <!-- Crystal backdrop gradient -->
    <linearGradient id="crystalGradient" x1="8" y1="2.5" x2="8" y2="13.5" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="white" stop-opacity="0.9"/>
      <stop offset="0.3" stop-color="white" stop-opacity="0.6"/>
      <stop offset="0.6" stop-color="white" stop-opacity="0.4"/>
      <stop offset="1" stop-color="white" stop-opacity="0.1"/>
    </linearGradient>
    
    <!-- Star pattern gradient with enhanced colors -->
    <linearGradient id="starGradient" x1="8" y1="3" x2="8" y2="13" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFF6E5"/>
      <stop offset="0.35" stop-color="#F0D9B6"/>
      <stop offset="0.7" stop-color="#D0B996"/>
      <stop offset="1" stop-color="#A78D67"/>
    </linearGradient>
    
    <!-- Advanced resonance gradient -->
    <linearGradient id="resonanceGradient" x1="1.5" y1="1.5" x2="14.5" y2="14.5" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFFFF" stop-opacity="0.8"/>
      <stop offset="0.5" stop-color="#E0C9A6" stop-opacity="0.8"/>
      <stop offset="1" stop-color="#A78D67" stop-opacity="0.8"/>
    </linearGradient>
    
    <!-- Ultra-premium platinum base gradient with precision φ-ratio color stops -->
    <linearGradient id="platinumBaseGradient" x1="2" y1="2" x2="14" y2="14" gradientUnits="userSpaceOnUse" gradientTransform="rotate(15, 8, 8)">
      <stop offset="0" stop-color="#FFFFFF"/>
      <stop offset="0.125" stop-color="#FAFAFE"/> <!-- 1/8 -->
      <stop offset="0.236" stop-color="#F5F5F9"/> <!-- φ-2 -->
      <stop offset="0.382" stop-color="#EDEDF2"/> <!-- (φ-1)/φ -->
      <stop offset="0.5" stop-color="#E2E2E8"/> <!-- 1/2 -->
      <stop offset="0.618" stop-color="#D8D8DE"/> <!-- 1-φ-1 -->
      <stop offset="0.764" stop-color="#CCCCD4"/> <!-- 1-(φ-2) -->
      <stop offset="0.875" stop-color="#C0C0C8"/> <!-- 7/8 -->
      <stop offset="1" stop-color="#B0B0B8"/>
    </linearGradient>
    
    <!-- Enhanced outer rim gradient for ultra-metallic appearance -->
    <linearGradient id="outerRimGradient" x1="2" y1="2" x2="14" y2="14" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFFFF"/>
      <stop offset="0.2" stop-color="#F5F5F7"/>
      <stop offset="0.4" stop-color="#DEDEE6"/>
      <stop offset="0.618" stop-color="#C8C8D0"/> <!-- φ-1 -->
      <stop offset="0.764" stop-color="#A8A8B0"/> <!-- φ-2 -->
      <stop offset="0.85" stop-color="#909098"/>
      <stop offset="1" stop-color="#707078"/>
    </linearGradient>
    
    <!-- Enhanced inner circle gradient with premium sheen and precision ratios -->
    <radialGradient id="innerCircleGradient" cx="6.5" cy="6.5" r="6.5" gradientUnits="userSpaceOnUse" gradientTransform="rotate(-5, 6.5, 6.5)">
      <stop offset="0" stop-color="#FFFFFF"/>
      <stop offset="0.236" stop-color="#FCFCFE"/> <!-- φ-2 -->
      <stop offset="0.5" stop-color="#F9F9FC"/>
      <stop offset="0.618" stop-color="#F5F5FA"/> <!-- 1-φ-1 -->
      <stop offset="0.85" stop-color="#F0F0F5"/>
      <stop offset="1" stop-color="#E8E8F0"/>
    </radialGradient>
    
    <!-- Enhanced inner rim gradient with mathematical precision -->
    <linearGradient id="innerRimGradient" x1="2.5" y1="2.5" x2="13.5" y2="13.5" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#D0D0D8"/>
      <stop offset="0.236" stop-color="#C8C8D0"/> <!-- φ-2 -->
      <stop offset="0.382" stop-color="#C0C0C8"/> <!-- (φ-1)/φ -->
      <stop offset="0.5" stop-color="#D8D8E0"/>
      <stop offset="0.618" stop-color="#C8C8D0"/> <!-- 1-φ-1 -->
      <stop offset="0.764" stop-color="#C0C0C8"/> <!-- 1-(φ-2) -->
      <stop offset="1" stop-color="#B0B0B8"/>
    </linearGradient>
    
    <!-- Micro pattern gradient for enhanced texture -->
    <radialGradient id="microPatternGradient" cx="8" cy="8" r="6" gradientUnits="userSpaceOnUse" gradientTransform="rotate(10, 8, 8)">
      <stop offset="0" stop-color="#FFFFFF"/>
      <stop offset="0.5" stop-color="#F8F8FD"/>
      <stop offset="0.75" stop-color="#F4F4F9"/>
      <stop offset="1" stop-color="#F0F0F5"/>
    </radialGradient>
    
    <!-- Golden base gradient for depth effect -->
    <radialGradient id="goldBaseGradient" cx="7.8" cy="5.4" r="1.3" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#D4A429"/>
      <stop offset="0.5" stop-color="#B3891E"/>
      <stop offset="1" stop-color="#8E6B18"/>
    </radialGradient>
    
    <!-- Ultra-luxurious gold dot gradient with precision color science -->
    <radialGradient id="goldDotGradient" cx="7.8" cy="5.3" r="1.2" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFBEB"/>
      <stop offset="0.09" stop-color="#FFF8E0"/> <!-- φ^-4 -->
      <stop offset="0.146" stop-color="#FFF3D6"/> <!-- φ^-3 -->
      <stop offset="0.236" stop-color="#FFDF8B"/> <!-- φ^-2 -->
      <stop offset="0.382" stop-color="#F9D56A"/> <!-- φ^-1 -->
      <stop offset="0.5" stop-color="#F5CA52"/>
      <stop offset="0.618" stop-color="#EBBF45"/> <!-- 1-φ^-1 -->
      <stop offset="0.764" stop-color="#DEB039"/> <!-- 1-φ^-2 -->
      <stop offset="0.854" stop-color="#D4A429"/> <!-- 1-φ^-3 -->
      <stop offset="0.91" stop-color="#C79B24"/> <!-- 1-φ^-4 -->
      <stop offset="1" stop-color="#B3891E"/>
    </radialGradient>
    
    <!-- Gold stem shadow gradient -->
    <linearGradient id="goldStemShadowGradient" x1="8" y1="7.25" x2="8" y2="11.55" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#B3891E"/>
      <stop offset="0.5" stop-color="#8E6B18"/>
      <stop offset="1" stop-color="#6D5012"/>
    </linearGradient>
    
    <!-- Enhanced gold stem gradient with precision color theory -->
    <linearGradient id="goldStemGradient" x1="8" y1="7" x2="8" y2="11.7" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFBEB"/>
      <stop offset="0.05" stop-color="#FFF8E0"/>
      <stop offset="0.1" stop-color="#FFF3D6"/>
      <stop offset="0.2" stop-color="#FFDF8B"/>
      <stop offset="0.3" stop-color="#F9D56A"/>
      <stop offset="0.5" stop-color="#F5CA52"/>
      <stop offset="0.7" stop-color="#EBBF45"/>
      <stop offset="0.85" stop-color="#DEB039"/>
      <stop offset="0.95" stop-color="#D4A429"/>
      <stop offset="1" stop-color="#B3891E"/>
    </linearGradient>
    
    <!-- Light reflection gradient -->
    <linearGradient id="lightReflectionGradient" gradientUnits="userSpaceOnUse" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0" stop-color="white" stop-opacity="1"/>
      <stop offset="0.5" stop-color="white" stop-opacity="0.8"/>
      <stop offset="1" stop-color="white" stop-opacity="0.5"/>
    </linearGradient>
    
    <!-- Curved light path gradient -->
    <linearGradient id="curvedLightGradient" gradientUnits="userSpaceOnUse" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0" stop-color="white" stop-opacity="0.9"/>
      <stop offset="0.5" stop-color="white" stop-opacity="0.7"/>
      <stop offset="1" stop-color="white" stop-opacity="0.5"/>
    </linearGradient>
    
    <!-- Enhanced accent ring gradient with golden ratio color stops -->
    <linearGradient id="accentRingGradient" x1="2.2" y1="2.2" x2="13.8" y2="13.8" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFBEB"/>
      <stop offset="0.236" stop-color="#F0D9B6"/> <!-- φ^-2 -->
      <stop offset="0.382" stop-color="#E0C9A6"/> <!-- φ^-1 -->
      <stop offset="0.618" stop-color="#CFB58E"/> <!-- 1-φ^-1 -->
      <stop offset="0.764" stop-color="#BFA27D"/> <!-- 1-φ^-2 -->
      <stop offset="1" stop-color="#A78D67"/>
    </linearGradient>
    
    <!-- Enhanced outer accent gradient -->
    <linearGradient id="outerAccentGradient" x1="1.8" y1="1.8" x2="14.2" y2="14.2" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#F0D9B6"/>
      <stop offset="0.382" stop-color="#E0C9A6"/> <!-- φ^-1 -->
      <stop offset="0.618" stop-color="#CFB58E"/> <!-- 1-φ^-1 -->
      <stop offset="1" stop-color="#A78D67"/>
    </linearGradient>
    
    <!-- Inner accent gradient -->
    <linearGradient id="innerAccentGradient" x1="3" y1="3" x2="13" y2="13" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#F0D9B6"/>
      <stop offset="0.382" stop-color="#E5CEAD"/> <!-- φ^-1 -->
      <stop offset="0.618" stop-color="#E0C9A6"/> <!-- 1-φ^-1 -->
      <stop offset="1" stop-color="#CFB58E"/>
    </linearGradient>
    
    <!-- Luxury flare gradient -->
    <linearGradient id="flareGradient" x1="3.5" y1="8" x2="12.5" y2="8" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="white" stop-opacity="0"/>
      <stop offset="0.5" stop-color="white" stop-opacity="1"/>
      <stop offset="1" stop-color="white" stop-opacity="0"/>
    </linearGradient>
    
    <!-- Advanced premium effects -->
    <filter id="softGlow" x="0" y="0" width="16" height="16" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.8"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.95 0 0 0 0 0.88 0 0 0 0 0.75 0 0 0 0.5 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="crystallineEffect" x="0" y="0" width="16" height="16" filterUnits="userSpaceOnUse">
      <feGaussianBlur stdDeviation="0.15" result="blur"/>
      <feBlend in="SourceGraphic" in2="blur" mode="normal"/>
    </filter>
    
    <filter id="subtleStar" x="0" y="0" width="16" height="16" filterUnits="userSpaceOnUse">
      <feGaussianBlur stdDeviation="0.2" result="blur"/>
      <feBlend in="SourceGraphic" in2="blur" mode="normal"/>
    </filter>
    
    <filter id="subtleShadow" x="1.5" y="1.5" width="13" height="13" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset dy="0.05"/>
      <feGaussianBlur stdDeviation="0.25"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.4 0 0 0 0 0.4 0 0 0 0 0.4 0 0 0 0.3 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="0.5"/>
      <feGaussianBlur stdDeviation="0.5"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
      <feBlend mode="normal" in2="shape" result="effect2_innerShadow"/>
    </filter>
    
    <filter id="subtleShadowIntense" x="1.5" y="1.5" width="13" height="13" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset dy="0.07"/>
      <feGaussianBlur stdDeviation="0.28"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.4 0 0 0 0 0.4 0 0 0 0 0.4 0 0 0 0.35 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="0.55"/>
      <feGaussianBlur stdDeviation="0.55"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.55 0"/>
      <feBlend mode="normal" in2="shape" result="effect2_innerShadow"/>
    </filter>
    
    <filter id="innerPolish" x="2.25" y="2.25" width="11.5" height="11.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="0.25"/>
      <feGaussianBlur stdDeviation="0.25"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
      <feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
    </filter>
    
    <filter id="dynamicNoiseTexture" x="0" y="0" width="16" height="16" filterUnits="userSpaceOnUse">
      <feTurbulence type="fractalNoise" baseFrequency="0.65" numOctaves="3" seed="3" stitchTiles="stitch" result="turbulence"/>
      <feComponentTransfer in="turbulence" result="adjusted">
        <feFuncA type="linear" slope="0.05" intercept="0"/>
      </feComponentTransfer>
      <feComposite in="adjusted" in2="SourceGraphic" operator="in" result="composite"/>
    </filter>
    
    <filter id="goldDepth" x="6.3" y="3.8" width="3.4" height="3.4" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset dy="0.1"/>
      <feGaussianBlur stdDeviation="0.1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.7 0 0 0 0 0.5 0 0 0 0 0.2 0 0 0 0.4 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="goldGlow" x="6.4" y="3.9" width="3.2" height="3.2" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.25"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.85 0 0 0 0 0.4 0 0 0 0.8 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dx="-0.15" dy="-0.15"/>
      <feGaussianBlur stdDeviation="0.1"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
      <feBlend mode="normal" in2="shape" result="effect2_innerShadow"/>
    </filter>
    
    <filter id="goldGlowIntense" x="6.4" y="3.9" width="3.2" height="3.2" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.4"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.85 0 0 0 0 0.4 0 0 0 0.9 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dx="-0.15" dy="-0.15"/>
      <feGaussianBlur stdDeviation="0.1"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.7 0"/>
      <feBlend mode="normal" in2="shape" result="effect2_innerShadow"/>
    </filter>
    
    <filter id="goldStemShadow" x="6.7" y="6.95" width="2.6" height="5.2" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset dy="0.1"/>
      <feGaussianBlur stdDeviation="0.1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.7 0 0 0 0 0.5 0 0 0 0 0.2 0 0 0 0.3 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="goldShadow" x="6.8" y="6.6" width="2.4" height="5.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.15"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.92 0 0 0 0 0.74 0 0 0 0 0.31 0 0 0 0.6 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="pulseShadow" x="6.8" y="6.6" width="2.4" height="5.5" filterUnits="userSpaceOnUse">
      <feGaussianBlur stdDeviation="0.1"/>
    </filter>
    
    <filter id="advancedSparkleFilter" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse">
      <feGaussianBlur stdDeviation="0.05"/>
      <feColorMatrix type="matrix" values="1 0 0 0 0.2 0 1 0 0 0.2 0 0 1 0 0.2 0 0 0 1 0"/>
      <feComponentTransfer>
        <feFuncR type="gamma" amplitude="1.2" exponent="1.2"/>
        <feFuncG type="gamma" amplitude="1.2" exponent="1.2"/>
        <feFuncB type="gamma" amplitude="1.2" exponent="1.2"/>
      </feComponentTransfer>
    </filter>
    
    <filter id="flareFilter" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse">
      <feGaussianBlur stdDeviation="0.2"/>
      <feComponentTransfer>
        <feFuncR type="gamma" amplitude="1.5" exponent="1.2"/>
        <feFuncG type="gamma" amplitude="1.5" exponent="1.2"/>
        <feFuncB type="gamma" amplitude="1.5" exponent="1.2"/>
      </feComponentTransfer>
    </filter>
    
    <filter id="luxuryFloatingEffect" x="1.5" y="1.5" width="13" height="13" filterUnits="userSpaceOnUse">
      <feGaussianBlur in="SourceAlpha" stdDeviation="0.12" result="blur"/>
      <feOffset in="blur" dx="0" dy="0.05" result="offsetBlur"/>
      <feComponentTransfer in="offsetBlur" result="brightBlur">
        <feFuncA type="linear" slope="0.6"/>
      </feComponentTransfer>
      <feComposite in="brightBlur" in2="SourceAlpha" operator="in" result="selectiveBlur"/>
      <feBlend in="SourceGraphic" in2="selectiveBlur" mode="normal"/>
    </filter>
  </defs>
</svg>