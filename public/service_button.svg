<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- ULTRA LUXURY BACKDROP SYSTEM -->
  <circle cx="8" cy="8" r="7.6" filter="url(#galaxyOuterEffect)" fill="url(#cosmicBackgroundGradient)" opacity="0.98"/>
  <circle cx="8" cy="8" r="7.3" filter="url(#nebulaCoreEffect)" fill="url(#deepSpaceGradient)" opacity="0.99"/>
  
  <!-- ENHANCED PREMIUM ORBITAL RINGS -->
  <g opacity="0.85" filter="url(#orbitalRingGlow)">
    <circle cx="8" cy="8" r="7" fill="none" stroke="url(#azureRingGradient)" stroke-width="0.15" stroke-dasharray="1.5 0.8">
      <animateTransform attributeName="transform" type="rotate" from="0 8 8" to="360 8 8" dur="120s" repeatCount="indefinite"/>
    </circle>
    <circle cx="8" cy="8" r="6.6" fill="none" stroke="url(#cobaltRingGradient)" stroke-width="0.12" stroke-dasharray="2 0.8" stroke-dashoffset="1.5">
      <animateTransform attributeName="transform" type="rotate" from="360 8 8" to="0 8 8" dur="90s" repeatCount="indefinite"/>
    </circle>
    <circle cx="8" cy="8" r="6.2" fill="none" stroke="url(#celestialRingGradient)" stroke-width="0.1" stroke-dasharray="0.8 1.2" stroke-dashoffset="0.6">
      <animateTransform attributeName="transform" type="rotate" from="0 8 8" to="360 8 8" dur="60s" repeatCount="indefinite"/>
    </circle>
    <circle cx="8" cy="8" r="5.8" fill="none" stroke="url(#diamondRingGradient)" stroke-width="0.08" stroke-dasharray="0.6 1.4" stroke-dashoffset="0.3">
      <animateTransform attributeName="transform" type="rotate" from="180 8 8" to="-180 8 8" dur="45s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- ENHANCED CENTRAL SUPERNOVA GLOW -->
  <circle cx="8" cy="8" r="5.8" fill="url(#supernovaGlowGradient)" opacity="0.35" filter="url(#supernovaEffect)">
    <animate attributeName="r" values="5.8;5.9;5.8" dur="4s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
  </circle>
  
  <!-- EXPANDED STARFIELD BACKGROUND -->
  <g filter="url(#starfieldEffect)">
    <circle cx="5.2" cy="4.4" r="0.07" fill="#88EEFF" opacity="0.8">
      <animate attributeName="opacity" values="0.8;1;0.8" dur="2.3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="10.8" cy="5.3" r="0.06" fill="#A0DDFF" opacity="0.7">
      <animate attributeName="opacity" values="0.7;0.9;0.7" dur="3.1s" repeatCount="indefinite"/>
    </circle>
    <circle cx="4.3" cy="9.7" r="0.08" fill="#7AFFFF" opacity="0.75">
      <animate attributeName="opacity" values="0.75;0.95;0.75" dur="2.7s" repeatCount="indefinite"/>
    </circle>
    <circle cx="11.5" cy="10.2" r="0.05" fill="#B6EEFF" opacity="0.65">
      <animate attributeName="opacity" values="0.65;0.85;0.65" dur="3.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="6.7" cy="3.6" r="0.06" fill="#D0F2FF" opacity="0.7">
      <animate attributeName="opacity" values="0.7;0.9;0.7" dur="2.9s" repeatCount="indefinite"/>
    </circle>
    <circle cx="9.3" cy="11.4" r="0.07" fill="#60E8FF" opacity="0.75">
      <animate attributeName="opacity" values="0.75;0.95;0.75" dur="3.3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="3.5" cy="7.2" r="0.04" fill="#C0F0FF" opacity="0.8">
      <animate attributeName="opacity" values="0.8;1;0.8" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="12.4" cy="7.8" r="0.05" fill="#50D0FF" opacity="0.7">
      <animate attributeName="opacity" values="0.7;0.9;0.7" dur="3.2s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- ENHANCED 3D ENVELOPE PLATFORM WITH DEPTH -->
  <g filter="url(#platformEffect)" opacity="0.94">
    <path d="M4 9.8l4 1.4 4-1.4v-0.3l-4-1.4-4 1.4z" fill="url(#platformBaseGradient)"/>
    <path d="M4 9.8v0.3l4 1.4 4-1.4v-0.3" stroke="url(#platformSideGradient)" stroke-width="0.08" stroke-linecap="round"/>
    <path d="M4 9.5l4-1.4 4 1.4" stroke="url(#platformEdgeGradient)" stroke-width="0.15" stroke-linecap="round"/>
  </g>
  
  <!-- PREMIUM HOLOGRAPHIC EMAIL PROJECTION -->
  <g filter="url(#hologramEffect)" opacity="0.98">
    <path d="M4.8 6.8h6.4c0.4 0 0.7 0.3 0.7 0.7v3.2c0 0.4-0.3 0.7-0.7 0.7H4.8c-0.4 0-0.7-0.3-0.7-0.7V7.5c0-0.4 0.3-0.7 0.7-0.7z" 
          fill="url(#envelopeBodyGradient)" 
          stroke="url(#goldTrimGradient)" 
          stroke-width="0.25"/>
    
    <path d="M11.9 7.5L8 9.8 4.1 7.5" 
          stroke="url(#flapEdgeGradient)" 
          stroke-width="0.35" 
          stroke-linecap="round" 
          fill="url(#envelopeFlapGradient)">
      <animate attributeName="d" 
               values="M11.9 7.5L8 9.8 4.1 7.5;M11.9 7.6L8 9.9 4.1 7.6;M11.9 7.5L8 9.8 4.1 7.5" 
               dur="3.5s" repeatCount="indefinite" calcMode="spline" 
               keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
    </path>
    
    <path d="M5 11.2l3 1.05 3-1.05" stroke="url(#envelopeShadowGradient)" stroke-width="0.08" stroke-linecap="round" opacity="0.4"/>
    

  </g>
  
  <!-- ENHANCED ENERGY FLOW LINES -->
  <g filter="url(#energyFlowEffect)" opacity="0.8">
    <path d="M3.5 5.5c3-2.8 6-2.8 9 0" stroke="url(#energyFlowGradient1)" stroke-width="0.1" stroke-linecap="round">
      <animate attributeName="d" values="M3.5 5.5c3-2.8 6-2.8 9 0;M3.5 5.3c3-2.6 6-2.6 9 0;M3.5 5.5c3-2.8 6-2.8 9 0" dur="8s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
    </path>
    <path d="M3.5 10.5c3 2.8 6 2.8 9 0" stroke="url(#energyFlowGradient2)" stroke-width="0.1" stroke-linecap="round">
      <animate attributeName="d" values="M3.5 10.5c3 2.8 6 2.8 9 0;M3.5 10.7c3 2.6 6 2.6 9 0;M3.5 10.5c3 2.8 6 2.8 9 0" dur="8s" begin="2s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
    </path>
    <path d="M3.8 4.5c2.8-1.6 5.6-1.6 8.4 0" stroke="url(#energyFlowGradient3)" stroke-width="0.08" stroke-linecap="round" opacity="0.7">
      <animate attributeName="d" values="M3.8 4.5c2.8-1.6 5.6-1.6 8.4 0;M3.8 4.3c2.8-1.5 5.6-1.5 8.4 0;M3.8 4.5c2.8-1.6 5.6-1.6 8.4 0" dur="7s" begin="1s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
    </path>
    <path d="M3.8 11.5c2.8 1.6 5.6 1.6 8.4 0" stroke="url(#energyFlowGradient4)" stroke-width="0.08" stroke-linecap="round" opacity="0.7">
      <animate attributeName="d" values="M3.8 11.5c2.8 1.6 5.6 1.6 8.4 0;M3.8 11.7c2.8 1.5 5.6 1.5 8.4 0;M3.8 11.5c2.8 1.6 5.6 1.6 8.4 0" dur="7s" begin="3s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
    </path>
  </g>
  
  <!-- ENHANCED ANIMATED DATA TRAILS WITH MORE PARTICLES -->
  <g filter="url(#dataTrailEffect)" opacity="0.7">
    <circle cx="5" cy="5" r="0.12" fill="#88DDFF">
      <animate attributeName="cx" values="3;5;8;8.5" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="cy" values="3;5;7;7" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="r" values="0.08;0.12;0.15;0" dur="2s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="12" cy="4" r="0.1" fill="#66FFEE">
      <animate attributeName="cx" values="13;12;9;8.2" dur="2.5s" begin="0.7s" repeatCount="indefinite"/>
      <animate attributeName="cy" values="4;4;6;6.8" dur="2.5s" begin="0.7s" repeatCount="indefinite"/>
      <animate attributeName="r" values="0.06;0.1;0.14;0" dur="2.5s" begin="0.7s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="3" cy="8" r="0.09" fill="#A0EEFF">
      <animate attributeName="cx" values="2;3;6;7.8" dur="2.8s" begin="1.2s" repeatCount="indefinite"/>
      <animate attributeName="cy" values="8;8;7.5;7" dur="2.8s" begin="1.2s" repeatCount="indefinite"/>
      <animate attributeName="r" values="0.05;0.09;0.12;0" dur="2.8s" begin="1.2s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="14" cy="9" r="0.08" fill="#70F0FF">
      <animate attributeName="cx" values="14;13;10;8.2" dur="3s" begin="1.8s" repeatCount="indefinite"/>
      <animate attributeName="cy" values="9;8.5;8;7.5" dur="3s" begin="1.8s" repeatCount="indefinite"/>
      <animate attributeName="r" values="0.04;0.08;0.11;0" dur="3s" begin="1.8s" repeatCount="indefinite"/>
    </circle>
    
    <path d="M3 3l0.1 0.1" stroke="#88EEFF" stroke-width="0.06" stroke-linecap="round" opacity="0.6">
      <animate attributeName="d" values="M3 3l0.1 0.1;M5 5l0.1 0.1;M7 6.5l0.1 0.1;M8 7l0.1 0.1" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;0.8;0.4;0" dur="2s" repeatCount="indefinite"/>
    </path>
    <path d="M13 4l-0.1 0.1" stroke="#66FFEE" stroke-width="0.05" stroke-linecap="round" opacity="0.6">
      <animate attributeName="d" values="M13 4l-0.1 0.1;M11 4l-0.1 0.1;M9 5.5l-0.1 0.1;M8.2 6.8l-0.1 0.1" dur="2.5s" begin="0.7s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;0.8;0.4;0" dur="2.5s" begin="0.7s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- ENHANCED PREMIUM AMBIENT EFFECTS -->
  <circle cx="8" cy="8" r="7.2" fill="none" stroke="url(#ambianceGradient)" stroke-width="0.12" stroke-opacity="0.5">
    <animate attributeName="r" values="7.2;7.25;7.2" dur="5s" repeatCount="indefinite"/>
    <animate attributeName="stroke-opacity" values="0.5;0.7;0.5" dur="5s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Subtle pulsing glow ring -->
  <circle cx="8" cy="8" r="7" fill="none" stroke="url(#pulsingGlowGradient)" stroke-width="0.06" stroke-opacity="0.3">
    <animate attributeName="r" values="7;7.1;7" dur="4s" begin="2s" repeatCount="indefinite"/>
    <animate attributeName="stroke-opacity" values="0.3;0.5;0.3" dur="4s" begin="2s" repeatCount="indefinite"/>
  </circle>
  
  <!-- ENHANCED LUXURY CORNER ACCENTS -->
  <path d="M2.5 6L3.5 5" stroke="url(#accentGradient)" stroke-width="0.18" stroke-linecap="round" opacity="0.85"/>
  <path d="M13.5 6L12.5 5" stroke="url(#accentGradient)" stroke-width="0.18" stroke-linecap="round" opacity="0.85"/>
  <path d="M2.5 10L3.5 11" stroke="url(#accentGradient)" stroke-width="0.18" stroke-linecap="round" opacity="0.85"/>
  <path d="M13.5 10L12.5 11" stroke="url(#accentGradient)" stroke-width="0.18" stroke-linecap="round" opacity="0.85"/>
  
  <!-- Premium diamond corner accents -->
  <path d="M3 4.5L3.5 4" stroke="url(#diamondAccentGradient)" stroke-width="0.15" stroke-linecap="round" opacity="0.9"/>
  <path d="M13 4.5L12.5 4" stroke="url(#diamondAccentGradient)" stroke-width="0.15" stroke-linecap="round" opacity="0.9"/>
  <path d="M3 11.5L3.5 12" stroke="url(#diamondAccentGradient)" stroke-width="0.15" stroke-linecap="round" opacity="0.9"/>
  <path d="M13 11.5L12.5 12" stroke="url(#diamondAccentGradient)" stroke-width="0.15" stroke-linecap="round" opacity="0.9"/>
  
  <!-- DEFINITIONS -->
  <defs>
    <!-- ENHANCED LUXURY COLOR GRADIENTS -->
    <radialGradient id="cosmicBackgroundGradient" cx="8" cy="8" r="7.6" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#2260FF"/>
      <stop offset="0.4" stop-color="#1A50E0"/>
      <stop offset="0.7" stop-color="#1340C0"/>
      <stop offset="0.9" stop-color="#0A2F90"/>
      <stop offset="1" stop-color="#051C60"/>
    </radialGradient>
    
    <linearGradient id="deepSpaceGradient" x1="1" y1="1" x2="15" y2="15" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#4070FF"/>
      <stop offset="0.25" stop-color="#2260F0"/>
      <stop offset="0.5" stop-color="#1A50E0"/>
      <stop offset="0.75" stop-color="#1340C0"/>
      <stop offset="0.9" stop-color="#0A2F90"/>
      <stop offset="1" stop-color="#051C60"/>
    </linearGradient>
    
    <linearGradient id="azureRingGradient" x1="1" y1="1" x2="15" y2="15" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#70D8FF"/>
      <stop offset="0.5" stop-color="#40B0FF"/>
      <stop offset="1" stop-color="#1090FF"/>
    </linearGradient>
    
    <linearGradient id="cobaltRingGradient" x1="15" y1="1" x2="1" y2="15" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#4080FF"/>
      <stop offset="0.5" stop-color="#3870E0"/>
      <stop offset="1" stop-color="#3060C0"/>
    </linearGradient>
    
    <linearGradient id="celestialRingGradient" x1="8" y1="1" x2="8" y2="15" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#60FFFF"/>
      <stop offset="0.5" stop-color="#50C0FA"/>
      <stop offset="1" stop-color="#4080F5"/>
    </linearGradient>
    
    <linearGradient id="diamondRingGradient" x1="2" y1="8" x2="14" y2="8" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#90F0FF"/>
      <stop offset="0.3" stop-color="#70C8FF"/>
      <stop offset="0.7" stop-color="#50A0FF"/>
      <stop offset="1" stop-color="#3080FF"/>
    </linearGradient>
    
    <radialGradient id="supernovaGlowGradient" cx="8" cy="8" r="5.8" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#80E8FF"/>
      <stop offset="0.3" stop-color="#50B8FF" stop-opacity="0.8"/>
      <stop offset="0.6" stop-color="#4090FF" stop-opacity="0.5"/>
      <stop offset="1" stop-color="#1060FF" stop-opacity="0"/>
    </radialGradient>
    
    <linearGradient id="platformBaseGradient" x1="8" y1="8.1" x2="8" y2="11.2" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#3070F0"/>
      <stop offset="1" stop-color="#1040A0"/>
    </linearGradient>
    
    <linearGradient id="platformSideGradient" x1="8" y1="9.5" x2="8" y2="11.5" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#2060E0"/>
      <stop offset="1" stop-color="#0830A0"/>
    </linearGradient>
    
    <linearGradient id="platformEdgeGradient" x1="4" y1="8.8" x2="12" y2="8.8" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#50B8FF"/>
      <stop offset="0.5" stop-color="#70D8FF"/>
      <stop offset="1" stop-color="#50B8FF"/>
    </linearGradient>
    
    <linearGradient id="envelopeBodyGradient" x1="8" y1="6.8" x2="8" y2="11.4" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#C0F0FF"/>
      <stop offset="1" stop-color="#90E0FF"/>
    </linearGradient>
    
    <linearGradient id="goldTrimGradient" x1="8" y1="6.8" x2="8" y2="11.4" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFE24D"/>
      <stop offset="1" stop-color="#FFBF24"/>
    </linearGradient>
    
    <linearGradient id="envelopeFlapGradient" x1="8" y1="7.5" x2="8" y2="9.8" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#B0F0FF"/>
      <stop offset="1" stop-color="#70D0FF"/>
    </linearGradient>
    
    <linearGradient id="flapEdgeGradient" x1="8" y1="7.5" x2="8" y2="9.8" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFE24D"/>
      <stop offset="1" stop-color="#FFA024"/>
    </linearGradient>
    
    <linearGradient id="envelopeShadowGradient" x1="8" y1="11.2" x2="8" y2="12.25" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#3070E0"/>
      <stop offset="1" stop-color="#1040A0" stop-opacity="0"/>
    </linearGradient>
    
    <linearGradient id="sapphireLineGradient" x1="5.6" y1="9" x2="10.4" y2="9" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#1080FF"/>
      <stop offset="0.5" stop-color="#20A0FF"/>
      <stop offset="1" stop-color="#1080FF"/>
    </linearGradient>
    
    
    <!-- ENHANCED PREMIUM FILTER EFFECTS -->
    <filter id="galaxyOuterEffect" x="-1" y="-1" width="18" height="18" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset dy="0.5"/>
      <feGaussianBlur stdDeviation="0.95"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.15 0 0 0 0 0.6 0 0 0 0.7 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="nebulaCoreEffect" x="-0.5" y="-0.5" width="17" height="17" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.1 0 0 0 0 0.4 0 0 0 0 1 0 0 0 0.8 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="orbitalRingGlow" x="0.5" y="0.5" width="15" height="15" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.3"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.25 0 0 0 0 0.6 0 0 0 0 1 0 0 0 0.85 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="supernovaEffect" x="1.7" y="1.7" width="12.6" height="12.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.6"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.2 0 0 0 0 0.7 0 0 0 0 1 0 0 0 0.7 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="starfieldEffect" x="3" y="3" width="10" height="9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.15"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.5 0 0 0 0 0.9 0 0 0 0 1 0 0 0 0.95 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="platformEffect" x="3.5" y="7.7" width="9" height="4.1" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset dy="0.25"/>
      <feGaussianBlur stdDeviation="0.2"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.4 0 0 0 0 0.9 0 0 0 0.6 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="hologramEffect" x="3.6" y="6.3" width="8.8" height="6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.3"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.4 0 0 0 0 0.8 0 0 0 0 1 0 0 0 0.8 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
  </defs>
</svg>