# The Conundrums of Better Quality: Why We're Thinking About Asset Management All Wrong

*Published: May 12, 2025*

**Author: <PERSON><PERSON><PERSON>**

![author-image](/images/blog/carl-gustaf-ydstrom.jpeg)

![banner-image](/images/blog/samuel-arkwright-MoiQCBPc9CY-unsplash.jpeg)

![link-preview-image](/images/blog/samuel-arkwright-MoiQCBPc9CY-unsplash-preview.jpg) 

## Introduction

[dropcap]The modern approach to quality in asset management is fundamentally broken. Not because organizations don't care about quality—quite the opposite. They care too much about the wrong things. After years working with enterprises managing thousands of critical assets, I've come to a controversial conclusion: our obsession with measuring, standardizing, and inspecting quality has created precisely the opposite of what we intended. The harder we chase quality through conventional means, the more it slips through our fingers.

This isn't just a theoretical problem. The consequences appear in every equipment maintenance budget, every service level agreement, and every frustrated technician forced to follow procedures that don't match reality. Yet we keep doing the same things while expecting different results—the classic definition of insanity, played out across industries worldwide.

## The Measurement Mirage

[dropcap]The first illusion in quality management is that we can improve what we measure. This sounds logical, even obvious. But in complex environments like enterprise asset management, measurement creates a mirage—an apparently clear vision that disappears the closer you get to it.

Consider what happens when an organization decides to improve first-time fix rates. They begin tracking this metric obsessively. Soon, managers are discussing it in every meeting. Goals and incentives get attached to it. And slowly but surely, a transformation happens—not of the actual service quality, but of the behaviors surrounding the measurement itself.

Technicians begin to cherry-pick easier jobs. They over-provision parts "just in case." They classify visits as successful even when they've applied temporary fixes. The metric improves dramatically while the underlying reality remains unchanged or even deteriorates.

This isn't because people are dishonest. It's because human beings naturally optimize for what's measured, regardless of whether that measurement accurately reflects the desired outcome. In the words of management guru Peter Drucker, "What gets measured gets managed"—but the inverse is equally true: what doesn't get measured gets ignored.

The solution isn't to stop measuring. It's to recognize that measurement creates reality rather than merely observing it. Smart organizations understand this and apply three principles that turn measurement from an enemy into an ally:

1. They measure outcomes, not activities
2. They limit metrics to what they can actually act upon
3. They constantly rotate their measurement focus to prevent gaming

Most importantly, they recognize that measurements are simply proxies for reality—useful tools that become dangerous when mistaken for the truth itself.

## The Standardization Trap

[dropcap]The second quality conundrum emerges from our industrial-age belief that standardization equals quality. This made perfect sense when we were manufacturing identical widgets, but it breaks down when applied to complex service operations where context is king.

The standardization trap works like this: an organization identifies its "best practices" for asset maintenance. These get documented in exhaustive procedures that cover every imaginable scenario. Technicians are trained to follow these procedures precisely. Quality audits confirm compliance. Everything looks perfect on paper.

Yet on the ground, the reality is very different. Technicians face environments their procedures never anticipated. They encounter hybrid equipment setups, unusual environmental conditions, and unique customer requirements. The procedures become a hindrance rather than a help. The technicians now face an impossible choice: follow procedures they know are suboptimal or deliver actual quality by breaking the rules.

The most telling sign of this trap? Shadow systems. When technicians create unofficial workarounds, maintain their own knowledge bases, or develop hand-drawn schematics, they're not being rebellious—they're desperately trying to deliver real quality despite, not because of, the standardized approach.

This isn't a call for chaos. Rather, it's a recognition that in complex environments, standardization should focus on principles, not procedures. The best organizations understand this distinction and build what I call "principled adaptability":

1. They standardize the thinking process, not just the actions
2. They create systems that adapt to context rather than ignoring it
3. They trust front-line decision-makers while providing them with the right information

In other words, they recognize that humans aren't robots to be programmed but intelligent agents to be equipped.

## The Inspection Illusion

[dropcap]Perhaps the most persistent delusion in quality management is that we can inspect quality into a system. Despite decades of evidence to the contrary, organizations continue to invest millions in quality assurance functions that attempt to catch defects after they've already occurred.

This approach fundamentally misunderstands how quality actually works. Quality isn't something added through inspection; it's either built into the process or absent from it. By the time an inspector identifies a problem, the damage is already done—resources wasted, time lost, opportunities missed.

The inspection illusion perpetuates itself through a particularly destructive cycle: poor quality leads to more inspection, which diverts resources from actual improvement, which leads to continued poor quality. Organizations trapped in this cycle often have the largest quality departments and the worst quality outcomes.

Breaking free requires a fundamental shift in thinking:

1. Recognize that quality comes from decisions, not inspections
2. Invest in capabilities that improve decision quality at the point of service
3. Create feedback loops that connect outcomes directly to decisions

In asset management, this means equipping technicians with contextual intelligence about equipment history, environmental factors, and performance patterns. It means creating systems that support human judgment rather than replacing it.

## The Path Forward: Connected Intelligence

[dropcap]These conundrums—measurement, standardization, and inspection—all stem from the same root cause: treating quality as a mechanical problem rather than an intelligence problem. The path forward isn't more rigorous application of industrial-age thinking but a fundamental shift toward connected intelligence.

What does this look like in practice? Imagine a technician approaching an asset that requires service. In a traditional setup, they carry standardized procedures and checklists. In a connected intelligence environment, they have something far more valuable: context.

They know not just the maintenance history but the patterns within that history. They understand how this specific asset performs compared to similar equipment in different environments. They can see how recent changes in one component might affect seemingly unrelated systems.

This isn't just a technological shift. It's a philosophical one—moving from controlling actions to enhancing decisions. The organizations that embrace this shift gain a profound competitive advantage: they can deliver consistent quality across diverse environments while adapting to conditions their competitors never anticipated.

At Thinkertags, we've built our platform around this philosophy. We don't replace existing systems or dictate rigid procedures. Instead, we create an orchestration layer that connects physical assets with digital intelligence, providing the right information at the right moment to support better decisions.

The quality conundrums I've described aren't going away. But organizations that understand them can transform these challenges into opportunities—delivering higher quality at lower cost while creating a more engaging experience for their teams and customers.

The choice is simple: continue chasing quality through increasingly rigid controls, or embrace connected intelligence and make quality an emergent property of better decisions. The organizations that choose the latter won't just solve today's quality problems—they'll be positioned to handle challenges their competitors haven't even imagined.

***

*This article is part of our Operational Excellence series, exploring the principles that separate exceptional asset management from average approaches.*

## Related Articles

- [3 Principles for Better Asset Management](https://thinkertags.com/research-and-insights/top-3-timeless-principles)
- [The Future of Asset Tracking with Thinkertags](https://thinkertags.com/research-and-insights/the-future-of-asset-tracking-with-thinkertags)
- [The Conundrums of Better Quality: Why We're Thinking About Asset Management All Wrong](https://thinkertags.com/research-and-insights/the-conundrums-of-better-quality)
