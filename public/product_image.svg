<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Premium outer frame with enhanced 3D effect -->
  <rect x="1.5" y="2" width="13" height="12" rx="2" filter="url(#luxuryShadow)" fill="url(#premiumGoldGradient)"/>
  
  <!-- Elegant beveled edge effect -->
  <path d="M3.5 2h9c1.105 0 2 0.895 2 2v0.5H1.5V4c0-1.105 0.895-2 2-2z" fill="url(#topBevelGradient)" opacity="0.9"/>
  <path d="M3 13.5h9c0.5 0 1.2-0.3 1.5-0.7H1.5c0.3 0.4 1 0.7 1.5 0.7z" fill="#000000" fill-opacity="0.2"/>
  
  <!-- Luxury image area with refined inset and subtle pattern -->
  <rect x="2.5" y="3" width="11" height="10" rx="1.5" fill="url(#imageAreaGradient)" stroke="url(#luxuryBorderGradient)" stroke-width="0.6" filter="url(#subtleInset)"/>
  <rect x="2.5" y="3" width="11" height="10" rx="1.5" fill="url(#patternTexture)" fill-opacity="0.03"/>
  
  <!-- Enhanced elegant landscape -->
  <path d="M2.5 9.8L4.2 7.3C4.3 7.15 4.5 7.1 4.7 7.2C4.8 7.25 4.85 7.35 4.9 7.45L6.3 9.2C6.35 9.3 6.45 9.35 6.6 9.3C6.7 9.25 6.75 9.18 6.8 9.1L8.9 5.8C9 5.65 9.15 5.6 9.3 5.7C9.4 5.75 9.45 5.8 9.5 5.9L13.5 10.7V11.6C13.5 12.05 13.15 12.5 12.5 12.5H3.5C2.95 12.5 2.5 12.05 2.5 11.6V9.8Z" fill="url(#enhancedLandscapeGradient)"/>
  
  <!-- Reflective water surface -->
  <path d="M2.5 10L4.2 9.3L6.4 10L8.9 9.2L13.5 10V11.5C13.5 12 13.05 12.5 12.5 12.5H3.5C2.95 12.5 2.5 12 2.5 11.5V10Z" fill="url(#waterGradient)" opacity="0.6"/>
  
  <!-- Enhanced golden sun with premium glow -->
  <circle cx="10.5" cy="5" r="1.5" fill="url(#luxurySunGradient)" filter="url(#enhancedSunGlow)"/>
  <circle cx="10.3" cy="4.8" r="0.4" fill="white" fill-opacity="0.85"/>
  
  <!-- Luxury decorative corners -->
  <path d="M3.2 3.2L2.8 4.2L3.8 3.8L3.2 3.2Z" fill="url(#cornerDetailGradient)"/>
  <path d="M12.8 3.2L13.2 4.2L12.2 3.8L12.8 3.2Z" fill="url(#cornerDetailGradient)"/>
  <path d="M3.2 12.8L2.8 11.8L3.8 12.2L3.2 12.8Z" fill="url(#cornerDetailGradient)"/>
  <path d="M12.8 12.8L13.2 11.8L12.2 12.2L12.8 12.8Z" fill="url(#cornerDetailGradient)"/>
  
  <!-- Premium corner detail with diamond effect -->
  <path d="M11.5 3.5L13 5V3.5H11.5Z" fill="url(#diamondGradient)"/>
  <path d="M11.5 3.5L13 5" stroke="#D4AF37" stroke-width="0.3"/>
  
  <!-- Light glints for that premium polished look -->
  <circle cx="4.5" cy="4.5" r="0.15" fill="white" fill-opacity="0.9"/>
  <circle cx="11.5" cy="4.5" r="0.1" fill="white" fill-opacity="0.9"/>
  <circle cx="4.5" cy="11.5" r="0.1" fill="white" fill-opacity="0.7"/>
  <circle cx="11.5" cy="11.5" r="0.15" fill="white" fill-opacity="0.7"/>
  
  <!-- Premium definitions -->
  <defs>
    <!-- Enhanced gold gradient with realistic luxury metal effect -->
    <linearGradient id="premiumGoldGradient" x1="8" y1="1.5" x2="8" y2="14.5" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#F7E7B4"/>
      <stop offset="0.1" stop-color="#F5D67B"/>
      <stop offset="0.25" stop-color="#E4C368"/>
      <stop offset="0.4" stop-color="#D4AF37"/>
      <stop offset="0.6" stop-color="#C19B26"/>
      <stop offset="0.8" stop-color="#AE8C14"/>
      <stop offset="0.9" stop-color="#9E7C10"/>
      <stop offset="1" stop-color="#8A6E0A"/>
    </linearGradient>

    <!-- Top bevel gradient for 3D effect -->
    <linearGradient id="topBevelGradient" x1="8" y1="2" x2="8" y2="4.5" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFFFF"/>
      <stop offset="1" stop-color="#F5D67B" stop-opacity="0"/>
    </linearGradient>
    
    <!-- Enhanced image area gradient -->
    <linearGradient id="imageAreaGradient" x1="8" y1="2.8" x2="8" y2="13.2" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFFFF"/>
      <stop offset="0.97" stop-color="#F8F8F8"/>
      <stop offset="1" stop-color="#EFEFEF"/>
    </linearGradient>
    
    <!-- Luxury border gradient -->
    <linearGradient id="luxuryBorderGradient" x1="8" y1="2.8" x2="8" y2="13.2" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#F7E7B4"/>
      <stop offset="0.5" stop-color="#D4AF37"/>
      <stop offset="1" stop-color="#9E7C10"/>
    </linearGradient>
    
    <!-- Enhanced landscape gradient -->
    <linearGradient id="enhancedLandscapeGradient" x1="8" y1="5.6" x2="8" y2="12.5" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#64B5F6"/>
      <stop offset="0.4" stop-color="#2196F3"/>
      <stop offset="0.7" stop-color="#1976D2"/>
      <stop offset="1" stop-color="#0D47A1"/>
    </linearGradient>
    
    <!-- Water reflection gradient -->
    <linearGradient id="waterGradient" x1="8" y1="9.2" x2="8" y2="12.5" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#29B6F6" stop-opacity="0.7"/>
      <stop offset="1" stop-color="#0D47A1" stop-opacity="0.8"/>
    </linearGradient>
    
    <!-- Enhanced sun gradient -->
    <radialGradient id="luxurySunGradient" cx="10.5" cy="4.8" r="1.5" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFDE7"/>
      <stop offset="0.4" stop-color="#FFEB3B"/>
      <stop offset="1" stop-color="#FFC107"/>
    </radialGradient>
    
    <!-- Corner detail gradient -->
    <linearGradient id="cornerDetailGradient" x1="8" y1="2" x2="8" y2="14" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#F7E7B4"/>
      <stop offset="1" stop-color="#D4AF37"/>
    </linearGradient>
    
    <!-- Diamond effect gradient -->
    <linearGradient id="diamondGradient" x1="11.5" y1="3.5" x2="13" y2="5" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFDE7"/>
      <stop offset="0.5" stop-color="#FFF9C4"/>
      <stop offset="1" stop-color="#F7E7B4"/>
    </linearGradient>
    
    <!-- Pattern texture for luxury feel -->
    <pattern id="patternTexture" width="4" height="4" patternUnits="userSpaceOnUse">
      <path d="M0 0h4v4H0z" fill="none"/>
      <path d="M0 0h1v1H0z M2 2h1v1H2z" fill="#000000" opacity="0.03"/>
      <path d="M1 1h1v1H1z M3 3h1v1H3z" fill="#FFFFFF" opacity="0.05"/>
    </pattern>
    
    <!-- Enhanced luxury shadow effect -->
    <filter id="luxuryShadow" x="0.5" y="1" width="15" height="14" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset dy="0.7"/>
      <feGaussianBlur stdDeviation="0.6"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.384 0 0 0 0 0.306 0 0 0 0 0.047 0 0 0 0.4 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="0.5"/>
      <feGaussianBlur stdDeviation="0.25"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="shape" result="effect2_innerShadow"/>
    </filter>
    
    <!-- Subtle inset effect for image area -->
    <filter id="subtleInset" x="2.3" y="2.8" width="11.4" height="10.4" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="0.2"/>
      <feGaussianBlur stdDeviation="0.15"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_innerShadow"/>
    </filter>
    
    <!-- Enhanced sun glow -->
    <filter id="enhancedSunGlow" x="7.5" y="2" width="6" height="6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.75"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.921 0 0 0 0 0.231 0 0 0 0.8 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
  </defs>
</svg>