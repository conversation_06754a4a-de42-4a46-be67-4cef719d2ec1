<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Ultimate ambient glow with enhanced luminosity -->
  <ellipse cx="8" cy="8" r="7.2" fill="url(#ultraAmbienceGradient)" opacity="0.18" filter="url(#enhancedSoftGlow)"/>
  
  <!-- Ultra-premium shadow effect with improved depth -->
  <path d="M13.2 1.8H3.8C3.25 1.8 2.8 2.25 2.8 2.8V14.2C2.8 14.75 3.25 15.2 3.8 15.2H13.2C13.75 15.2 14.2 14.75 14.2 14.2V2.8C14.2 2.25 13.75 1.8 13.2 1.8Z" 
        fill="black" opacity="0.22" filter="url(#enhancedShadowEffect)"/>
  
  <!-- Luxury cotton-fiber paper base with sophisticated gradient -->
  <path d="M13 1H3C2.45 1 2 1.45 2 2V14C2 14.55 2.45 15 3 15H13C13.55 15 14 14.55 14 14V2C14 1.45 13.55 1 13 1Z" 
        fill="url(#ultraPremiumPaperGradient)" filter="url(#enhancedPaperTexture)"/>
  
  <!-- Subtle luxury cotton fiber pattern overlay -->
  <path d="M13 1H3C2.45 1 2 1.45 2 2V14C2 14.55 2.45 15 3 15H13C13.55 15 14 14.55 14 14V2C14 1.45 13.55 1 13 1Z" 
        fill="url(#cottonFiberPattern)" fill-opacity="0.03"/>
  
  <!-- Ultra-premium edge detailing with platinum look -->
  <path d="M13 1H3C2.45 1 2 1.45 2 2V14C2 14.55 2.45 15 3 15H13C13.55 15 14 14.55 14 14V2C14 1.45 13.55 1 13 1Z" 
        stroke="url(#platinumBorderGradient)" stroke-width="0.18" stroke-opacity="0.85"/>
  
  <!-- Premium embossed watermark -->
  <path d="M8.2 7.5C8.2 7.5 8.6 7 9 7.3C9.4 7.6 9.5 8 9.5 8C9.5 8 9 8.5 8.6 8.2C8.2 7.9 8.2 7.5 8.2 7.5Z" 
        fill="url(#watermarkGradient)" opacity="0.06" filter="url(#embossingEffect)"/>
  <path d="M6.8 7.5C6.8 7.5 6.4 7 6 7.3C5.6 7.6 5.5 8 5.5 8C5.5 8 6 8.5 6.4 8.2C6.8 7.9 6.8 7.5 6.8 7.5Z" 
        fill="url(#watermarkGradient)" opacity="0.06" filter="url(#embossingEffect)"/>
  
  <!-- Enhanced folded corner with ultra-premium depth effect -->
  <path d="M11 1.1V3C11 3.55 11.45 4 12 4H13.9L11 1.1Z" 
        fill="url(#enhancedFoldedCornerGradient)" filter="url(#enhancedFoldedCornerShadow)"/>
  <path d="M11 1V3C11 3.55 11.45 4 12 4H14L11 1Z" 
        stroke="url(#platinumCornerEdgeGradient)" stroke-width="0.22" stroke-opacity="0.9"/>
  
  <!-- Enhanced corner fold line detail with dynamic lighting -->
  <path d="M11.2 2.8L13.2 4.8" stroke="url(#enhancedFoldLineGradient)" stroke-width="0.18" stroke-opacity="0.6"/>
  
  <!-- Ultra-premium text lines with metallic ink effect -->
  <path d="M4 6H12" stroke="url(#premiumInkGradient1)" stroke-width="0.85" stroke-linecap="round" filter="url(#enhancedTextLineGlow)"/>
  <path d="M4 8.5H10" stroke="url(#premiumInkGradient2)" stroke-width="0.85" stroke-linecap="round" filter="url(#enhancedTextLineGlow)"/>
  <path d="M4 11H8" stroke="url(#premiumInkGradient3)" stroke-width="0.85" stroke-linecap="round" filter="url(#enhancedTextLineGlow)"/>
  
  <!-- Ultra-luxury letterhead detail -->
  <path d="M4 3.8C4 3.8 4.5 3.5 5.5 3.5C6.5 3.5 7 4 8 4C9 4 9.5 3.5 10.5 3.5C11.5 3.5 12 3.8 12 3.8" 
        stroke="url(#letterheadGradient)" stroke-width="0.15" stroke-opacity="0.4" stroke-linecap="round"/>
  
  <!-- Luxury accent element - enhanced binding with gold trim -->
  <path d="M3 2.5C3 2.5 3.2 2.7 3.5 2.7C3.8 2.7 4 2.5 4 2.5C4 2.5 4.2 2.7 4.5 2.7C4.8 2.7 5 2.5 5 2.5" 
        stroke="url(#goldAccentGradient)" stroke-width="0.28" stroke-opacity="0.7" stroke-linecap="round" filter="url(#goldAccentGlow)"/>
  
  <!-- Enhanced highlight effects with dynamic light play -->
  <path d="M3 13C3 13 5 13.5 8 13.5C11 13.5 13 13 13 13" 
        stroke="white" stroke-width="0.22" stroke-opacity="0.55" stroke-linecap="round"/>
  <path d="M3 3C3 3 5 3.5 8 3.5C11 3.5 13 3 13 3" 
        stroke="white" stroke-width="0.22" stroke-opacity="0.55" stroke-linecap="round"/>
  
  <!-- Detailed premium light reflections -->
  <ellipse cx="4.5" cy="5" rx="0.12" ry="0.17" fill="white" opacity="0.75"/>
  <ellipse cx="10" cy="7.5" rx="0.15" ry="0.1" fill="white" opacity="0.65"/>
  <ellipse cx="6.5" cy="10" rx="0.1" ry="0.15" fill="white" opacity="0.55"/>
  <ellipse cx="12" cy="3.5" rx="0.08" ry="0.12" fill="white" opacity="0.7"/>
  <ellipse cx="7.8" cy="12.5" rx="0.15" ry="0.07" fill="white" opacity="0.45"/>
  
  <!-- Ultra-premium definitions -->
  <defs>
    <!-- Enhanced ambient background glow -->
    <radialGradient id="ultraAmbienceGradient" cx="8" cy="8" r="7.2" gradientUnits="userSpaceOnUse">
      <stop offset="0.3" stop-color="#74B8FF"/>
      <stop offset="0.7" stop-color="#3478F6"/>
      <stop offset="1" stop-color="#1D59D1"/>
    </radialGradient>
    
    <!-- Ultra-premium cotton paper gradient -->
    <linearGradient id="ultraPremiumPaperGradient" x1="2" y1="1" x2="14" y2="15" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFFFF"/>
      <stop offset="0.4" stop-color="#FAFCFF"/>
      <stop offset="0.8" stop-color="#F8FAFF"/>
      <stop offset="1" stop-color="#F0F6FF"/>
    </linearGradient>
    
    <!-- Platinum border gradient -->
    <linearGradient id="platinumBorderGradient" x1="2" y1="1" x2="14" y2="15" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#ACD1FF"/>
      <stop offset="0.4" stop-color="#64A8FF"/>
      <stop offset="0.7" stop-color="#3478F6"/>
      <stop offset="1" stop-color="#1D59D1"/>
    </linearGradient>
    
    <!-- Enhanced folded corner gradients -->
    <linearGradient id="enhancedFoldedCornerGradient" x1="11" y1="1" x2="14" y2="4" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#D9E9FF"/>
      <stop offset="0.3" stop-color="#C2DCFF"/>
      <stop offset="0.6" stop-color="#9CC1FF"/>
      <stop offset="1" stop-color="#74B8FF"/>
    </linearGradient>
    
    <linearGradient id="platinumCornerEdgeGradient" x1="11" y1="1" x2="14" y2="4" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#74B8FF"/>
      <stop offset="0.4" stop-color="#3478F6"/>
      <stop offset="0.8" stop-color="#1D59D1"/>
      <stop offset="1" stop-color="#0D47A1"/>
    </linearGradient>
    
    <linearGradient id="enhancedFoldLineGradient" x1="11.2" y1="2.8" x2="13.2" y2="4.8" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#3478F6" stop-opacity="0.35"/>
      <stop offset="0.5" stop-color="#3478F6" stop-opacity="0.65"/>
      <stop offset="1" stop-color="#3478F6" stop-opacity="0.35"/>
    </linearGradient>
    
    <!-- Premium metallic ink gradients -->
    <linearGradient id="premiumInkGradient1" x1="4" y1="6" x2="12" y2="6" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#74B8FF"/>
      <stop offset="0.2" stop-color="#64A8FF"/>
      <stop offset="0.5" stop-color="#3478F6"/>
      <stop offset="0.8" stop-color="#64A8FF"/>
      <stop offset="1" stop-color="#74B8FF"/>
    </linearGradient>
    
    <linearGradient id="premiumInkGradient2" x1="4" y1="8.5" x2="10" y2="8.5" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#74B8FF"/>
      <stop offset="0.2" stop-color="#64A8FF"/>
      <stop offset="0.5" stop-color="#3478F6"/>
      <stop offset="0.8" stop-color="#64A8FF"/>
      <stop offset="1" stop-color="#74B8FF"/>
    </linearGradient>
    
    <linearGradient id="premiumInkGradient3" x1="4" y1="11" x2="8" y2="11" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#74B8FF"/>
      <stop offset="0.2" stop-color="#64A8FF"/>
      <stop offset="0.5" stop-color="#3478F6"/>
      <stop offset="0.8" stop-color="#64A8FF"/>
      <stop offset="1" stop-color="#74B8FF"/>
    </linearGradient>
    
    <!-- Letterhead gradient -->
    <linearGradient id="letterheadGradient" x1="4" y1="3.8" x2="12" y2="3.8" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#ACD1FF"/>
      <stop offset="0.5" stop-color="#64A8FF"/>
      <stop offset="1" stop-color="#ACD1FF"/>
    </linearGradient>
    
    <!-- Gold accent gradient -->
    <linearGradient id="goldAccentGradient" x1="3" y1="2.5" x2="5" y2="2.5" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFE38C"/>
      <stop offset="0.3" stop-color="#FFDC73"/>
      <stop offset="0.7" stop-color="#FFC400"/>
      <stop offset="1" stop-color="#FFAD0D"/>
    </linearGradient>
    
    <!-- Watermark gradient -->
    <linearGradient id="watermarkGradient" x1="6" y1="7" x2="10" y2="9" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#000000"/>
      <stop offset="1" stop-color="#3478F6"/>
    </linearGradient>
    
    <!-- Cotton fiber pattern -->
    <pattern id="cottonFiberPattern" width="100" height="100" patternUnits="userSpaceOnUse">
      <path d="M0 0h100v100H0z" fill="none"/>
      <path fill="#FFFFFF" opacity="0.4" d="M30 10h2v2h-2z M70 30h2v2h-2z M10 70h2v2h-2z M50 50h2v2h-2z M90 90h2v2h-2z"/>
      <path fill="#000000" opacity="0.1" d="M10 10h1v1h-1z M40 40h1v1h-1z M70 70h1v1h-1z M20 80h1v1h-1z M80 20h1v1h-1z"/>
      <path fill="#3478F6" opacity="0.05" d="M25 25h1v1h-1z M65 65h1v1h-1z M85 45h1v1h-1z M45 85h1v1h-1z"/>
    </pattern>
    
    <!-- Enhanced premium effect filters -->
    <filter id="enhancedSoftGlow" x="0" y="0" width="16" height="16" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.6"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.2 0 0 0 0 0.47 0 0 0 0 0.97 0 0 0 0.55 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="enhancedShadowEffect" x="2.3" y="1.3" width="12.4" height="14.4" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset dy="0.25"/>
      <feGaussianBlur stdDeviation="0.18"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.35 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="enhancedPaperTexture" x="1.5" y="0.5" width="13" height="15" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="-0.2"/>
      <feGaussianBlur stdDeviation="0.12"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
      <feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
      <feTurbulence baseFrequency="0.7" numOctaves="3" result="turbulence" seed="2" type="fractalNoise"/>
      <feDisplacementMap in="SourceGraphic" in2="turbulence" scale="0.35" xChannelSelector="R" yChannelSelector="G"/>
      <feColorMatrix type="matrix" values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 0.06 0" result="texture"/>
    </filter>
    
    <filter id="embossingEffect" x="5.3" y="6.8" width="4.5" height="1.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="0.2"/>
      <feGaussianBlur stdDeviation="0.1"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.4 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_innerShadow"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="-0.2"/>
      <feGaussianBlur stdDeviation="0.1"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
      <feBlend mode="normal" in2="effect1_innerShadow" result="effect2_innerShadow"/>
    </filter>
    
    <filter id="enhancedFoldedCornerShadow" x="10.7" y="0.7" width="3.6" height="3.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset dy="0.12"/>
      <feGaussianBlur stdDeviation="0.12"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.2 0 0 0 0 0.47 0 0 0 0 0.97 0 0 0 0.35 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="-0.12"/>
      <feGaussianBlur stdDeviation="0.12"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.18 0"/>
      <feBlend mode="normal" in2="shape" result="effect2_innerShadow"/>
    </filter>
    
    <filter id="enhancedTextLineGlow" x="3.6" y="5.6" width="8.8" height="5.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.12"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.2 0 0 0 0 0.47 0 0 0 0 0.97 0 0 0 0.45 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="goldAccentGlow" x="2.7" y="2.2" width="2.6" height="0.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.8 0 0 0 0 0.2 0 0 0 0.6 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
  </defs>
</svg>