<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Enhanced ambient background glow for ultra-premium presence -->
  <circle cx="8" cy="8" r="7.8" fill="url(#enhancedAmbienceGradient)" opacity="0.2" filter="url(#enhancedSoftGlow)"/>
  
  <!-- Luxury platinum outer rim -->
  <circle cx="8" cy="8" r="7.2" fill="url(#platinumRimGradient)" filter="url(#platinumRimShadow)"/>
  <circle cx="8" cy="8" r="7.2" stroke="url(#platinumBorderGradient)" stroke-width="0.2"/>
  
  <!-- Premium outer bezel with enhanced metallic effect -->
  <circle cx="8" cy="8" r="7" fill="url(#enhancedOuterBezelGradient)" filter="url(#enhancedOuterShadow)"/>
  <circle cx="8" cy="8" r="7" stroke="url(#enhancedOuterRimGradient)" stroke-width="0.3"/>
  
  <!-- Luxurious detail markers -->
  <path d="M8 1.2L8.2 1.8H7.8L8 1.2Z" fill="url(#markerGradient)" filter="url(#markerGlow)"/>
  <path d="M8 14.8L8.2 14.2H7.8L8 14.8Z" fill="url(#markerGradient)" filter="url(#markerGlow)"/>
  <path d="M1.2 8L1.8 7.8V8.2L1.2 8Z" fill="url(#markerGradient)" filter="url(#markerGlow)"/>
  <path d="M14.8 8L14.2 7.8V8.2L14.8 8Z" fill="url(#markerGradient)" filter="url(#markerGlow)"/>
  
  <!-- Premium inner bezel with sapphire effect -->
  <circle cx="8" cy="8" r="6.2" fill="url(#sapphireBezelGradient)" opacity="0.8" filter="url(#sapphireGlow)"/>
  <circle cx="8" cy="8" r="6.2" stroke="url(#sapphireBorderGradient)" stroke-width="0.3" stroke-opacity="0.8"/>
  
  <!-- Premium inner dial with enhanced polished effect -->
  <circle cx="8" cy="8" r="5.8" fill="url(#enhancedInnerDialGradient)" filter="url(#enhancedInnerShadow)"/>
  <circle cx="8" cy="8" r="5.8" stroke="url(#enhancedInnerRimGradient)" stroke-width="0.2"/>
  
  <!-- Subtle luxury texture overlay -->
  <circle cx="8" cy="8" r="5.8" fill="url(#luxuryTexturePattern)" fill-opacity="0.03"/>
  
  <!-- Enhanced luxury progress track with inset effect -->
  <circle cx="8" cy="8" r="4.8" fill="none" stroke="url(#enhancedTrackGradient)" stroke-width="0.6" stroke-opacity="0.4" filter="url(#enhancedTrackShadow)"/>
  
  <!-- Luxury time markers -->
  <path d="M8 3.4L8.15 3.7H7.85L8 3.4Z" fill="url(#timeMarkerGradient)" opacity="0.8"/>
  <path d="M8 12.6L8.15 12.3H7.85L8 12.6Z" fill="url(#timeMarkerGradient)" opacity="0.8"/>
  <path d="M3.4 8L3.7 7.85V8.15L3.4 8Z" fill="url(#timeMarkerGradient)" opacity="0.8"/>
  <path d="M12.6 8L12.3 7.85V8.15L12.6 8Z" fill="url(#timeMarkerGradient)" opacity="0.8"/>
  
  <!-- Premium progress indicator with elegant enhanced caps -->
  <path d="M8 3.2A4.8 4.8 0 0 1 12.8 8 4.8 4.8 0 0 1 8 12.8 4.8 4.8 0 0 1 5 11.7" 
        stroke="url(#enhancedProgressGradient)" stroke-width="0.8" stroke-linecap="round" filter="url(#enhancedProgressGlow)"/>
  
  <!-- Enhanced luxury accent elements -->
  <path d="M8 4.5L8.3 4.8 8 5.1 7.7 4.8 8 4.5Z" fill="url(#enhancedAccentGradient)" filter="url(#enhancedAccentGlow)"/>
  <path d="M8 10.9L8.3 11.2 8 11.5 7.7 11.2 8 10.9Z" fill="url(#enhancedAccentGradient)" filter="url(#enhancedAccentGlow)"/>
  <path d="M4.5 8L4.8 8.3 4.5 8.6 4.2 8.3 4.5 8Z" fill="url(#enhancedAccentGradient)" filter="url(#enhancedAccentGlow)"/>
  <path d="M11.5 8L11.8 8.3 11.5 8.6 11.2 8.3 11.5 8Z" fill="url(#enhancedAccentGradient)" filter="url(#enhancedAccentGlow)"/>
  
  <!-- Exquisite center medallion with enhanced depth -->
  <circle cx="8" cy="8" r="3" fill="url(#enhancedCenterBaseGradient)" filter="url(#centerBaseShadow)"/>
  <circle cx="8" cy="8" r="2.8" fill="url(#enhancedCenterGradient)" filter="url(#enhancedCenterShadow)"/>
  <circle cx="8" cy="8" r="2.8" stroke="url(#enhancedCenterRimGradient)" stroke-width="0.2"/>
  
  <!-- Premium center texture -->
  <circle cx="8" cy="8" r="2.8" fill="url(#centerTexturePattern)" fill-opacity="0.05"/>
  
  <!-- Enhanced elegant check symbol -->
  <path d="M6.8 8L7.6 9 9.2 7" stroke="url(#enhancedCheckGradient)" stroke-width="0.7" stroke-linecap="round" stroke-linejoin="round" filter="url(#enhancedCheckGlow)"/>
  
  <!-- Ultra-refined highlights for polished appearance -->
  <path d="M6.2 5.2C6.2 5.2 6.9 5.8 8 5.8C9.1 5.8 9.8 5.2 9.8 5.2" stroke="white" stroke-width="0.2" stroke-opacity="0.5" stroke-linecap="round"/>
  <path d="M5.5 7C5.5 7 6.2 7.5 8 7.5C9.8 7.5 10.5 7 10.5 7" stroke="white" stroke-width="0.15" stroke-opacity="0.3" stroke-linecap="round"/>
  
  <!-- Premium light reflections -->
  <circle cx="7" cy="7" r="0.15" fill="white" fill-opacity="0.8"/>
  <circle cx="7.8" cy="6.5" r="0.1" fill="white" fill-opacity="0.7"/>
  <ellipse cx="9.5" cy="9.5" rx="0.1" ry="0.15" transform="rotate(-45 9.5 9.5)" fill="white" fill-opacity="0.6"/>
  <ellipse cx="6.8" cy="9" rx="0.08" ry="0.12" transform="rotate(30 6.8 9)" fill="white" fill-opacity="0.5"/>
  
  <!-- Ultra-premium definitions -->
  <defs>
    <!-- Enhanced sophisticated gradient definitions -->
    <radialGradient id="enhancedAmbienceGradient" cx="8" cy="8" r="7.8" gradientUnits="userSpaceOnUse">
      <stop offset="0.3" stop-color="#7AAFFF"/>
      <stop offset="0.7" stop-color="#5A90F0"/>
      <stop offset="1" stop-color="#3069D4"/>
    </radialGradient>
    
    <linearGradient id="platinumRimGradient" x1="3" y1="1" x2="13" y2="15" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFFFF"/>
      <stop offset="0.3" stop-color="#F0F0F0"/>
      <stop offset="0.6" stop-color="#E0E0E0"/>
      <stop offset="1" stop-color="#C0C0C0"/>
    </linearGradient>
    
    <linearGradient id="platinumBorderGradient" x1="1" y1="1" x2="15" y2="15" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFFFF"/>
      <stop offset="0.5" stop-color="#A0A0A0"/>
      <stop offset="1" stop-color="#808080"/>
    </linearGradient>
    
    <linearGradient id="enhancedOuterBezelGradient" x1="4" y1="2" x2="12" y2="14" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#F8FAFF"/>
      <stop offset="0.3" stop-color="#E8F0FF"/>
      <stop offset="0.7" stop-color="#D8E8FF"/>
      <stop offset="1" stop-color="#C8E0FF"/>
    </linearGradient>
    
    <linearGradient id="enhancedOuterRimGradient" x1="2" y1="2" x2="14" y2="14" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#ACD1FF"/>
      <stop offset="0.5" stop-color="#74B8FF"/>
      <stop offset="1" stop-color="#4488F6"/>
    </linearGradient>
    
    <linearGradient id="markerGradient" x1="8" y1="1.2" x2="8" y2="1.8" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#3478F6"/>
      <stop offset="1" stop-color="#1D59D1"/>
    </linearGradient>
    
    <radialGradient id="sapphireBezelGradient" cx="8" cy="7.5" r="6.2" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#F0F8FF"/>
      <stop offset="0.7" stop-color="#E0F0FF"/>
      <stop offset="1" stop-color="#D0E8FF"/>
    </radialGradient>
    
    <linearGradient id="sapphireBorderGradient" x1="3" y1="3" x2="13" y2="13" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#9CC1FF"/>
      <stop offset="1" stop-color="#3478F6"/>
    </linearGradient>
    
    <linearGradient id="enhancedInnerDialGradient" x1="4" y1="4" x2="12" y2="12" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFFFF"/>
      <stop offset="0.8" stop-color="#F4F8FF"/>
      <stop offset="1" stop-color="#E8F0FF"/>
    </linearGradient>
    
    <linearGradient id="enhancedInnerRimGradient" x1="4" y1="4" x2="12" y2="12" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#74B8FF"/>
      <stop offset="1" stop-color="#4488F6"/>
    </linearGradient>
    
    <linearGradient id="enhancedTrackGradient" x1="4" y1="4" x2="12" y2="12" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#C8E0FF"/>
      <stop offset="1" stop-color="#5A90F0"/>
    </linearGradient>
    
    <linearGradient id="timeMarkerGradient" x1="8" y1="3.4" x2="8" y2="3.7" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#3478F6"/>
      <stop offset="1" stop-color="#1D59D1"/>
    </linearGradient>
    
    <linearGradient id="enhancedProgressGradient" x1="5" y1="3.2" x2="12.8" y2="11" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#ACD1FF"/>
      <stop offset="0.5" stop-color="#4488F6"/>
      <stop offset="1" stop-color="#2D69E1"/>
    </linearGradient>
    
    <radialGradient id="enhancedAccentGradient" cx="8" cy="8" r="0.3" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFFFF"/>
      <stop offset="0.7" stop-color="#74B8FF"/>
      <stop offset="1" stop-color="#4488F6"/>
    </radialGradient>
    
    <radialGradient id="enhancedCenterBaseGradient" cx="8" cy="8" r="3" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#5A90F0"/>
      <stop offset="1" stop-color="#2D69E1"/>
    </radialGradient>
    
    <radialGradient id="enhancedCenterGradient" cx="7.5" cy="7.5" r="3" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#74B8FF"/>
      <stop offset="0.7" stop-color="#4488F6"/>
      <stop offset="1" stop-color="#2D69E1"/>
    </radialGradient>
    
    <linearGradient id="enhancedCenterRimGradient" x1="5.5" y1="5.5" x2="10.5" y2="10.5" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#ACD1FF"/>
      <stop offset="1" stop-color="#2D69E1"/>
    </linearGradient>
    
    <linearGradient id="enhancedCheckGradient" x1="6.8" y1="8" x2="9.2" y2="8" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFFFF"/>
      <stop offset="1" stop-color="#F4F8FF"/>
    </linearGradient>
    
    <!-- Premium texture patterns -->
    <pattern id="luxuryTexturePattern" width="12" height="12" patternUnits="userSpaceOnUse" patternTransform="rotate(45)">
      <path d="M0 0h12v12H0z" fill="none"/>
      <path d="M0 0h1v1H0z M4 4h1v1H4z M8 8h1v1H8z" fill="#3478F6" opacity="0.05"/>
      <path d="M2 2h1v1H2z M6 6h1v1H6z M10 10h1v1H10z" fill="#FFFFFF" opacity="0.1"/>
    </pattern>
    
    <pattern id="centerTexturePattern" width="8" height="8" patternUnits="userSpaceOnUse" patternTransform="rotate(30)">
      <path d="M0 0h8v8H0z" fill="none"/>
      <path d="M0 0h1v1H0z M4 4h1v1H4z" fill="#FFFFFF" opacity="0.1"/>
      <path d="M2 2h1v1H2z M6 6h1v1H6z" fill="#000000" opacity="0.05"/>
    </pattern>
    
    <!-- Enhanced premium effect filters -->
    <filter id="enhancedSoftGlow" x="0" y="0" width="16" height="16" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.6"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.267 0 0 0 0 0.522 0 0 0 0 0.973 0 0 0 0.6 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="platinumRimShadow" x="0.5" y="0.5" width="15" height="15" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.15"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.6 0 0 0 0 0.6 0 0 0 0 0.6 0 0 0 0.3 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="enhancedOuterShadow" x="0.7" y="0.7" width="14.6" height="14.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.18"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.267 0 0 0 0 0.522 0 0 0 0 0.973 0 0 0 0.35 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="0.25"/>
      <feGaussianBlur stdDeviation="0.25"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
      <feBlend mode="normal" in2="shape" result="effect2_innerShadow"/>
    </filter>
    
    <filter id="markerGlow" x="7.5" y="0.7" width="1" height="1.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.15"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.267 0 0 0 0 0.522 0 0 0 0 0.973 0 0 0 0.8 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="sapphireGlow" x="1.6" y="1.6" width="12.8" height="12.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.267 0 0 0 0 0.522 0 0 0 0 0.973 0 0 0 0.2 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="enhancedInnerShadow" x="2" y="2" width="12" height="12" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="0.25"/>
      <feGaussianBlur stdDeviation="0.15"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.267 0 0 0 0 0.522 0 0 0 0 0.973 0 0 0 0.25 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_innerShadow"/>
    </filter>
    
    <filter id="enhancedTrackShadow" x="2.8" y="2.8" width="10.4" height="10.4" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="0.25"/>
      <feGaussianBlur stdDeviation="0.12"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.22 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_innerShadow"/>
    </filter>
    
    <filter id="enhancedProgressGlow" x="4.6" y="2.8" width="8.6" height="9.3" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.18"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.267 0 0 0 0 0.522 0 0 0 0 0.973 0 0 0 0.85 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="enhancedAccentGlow" x="7.5" y="4.3" width="1" height="1" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.12"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.9 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="centerBaseShadow" x="4.7" y="4.7" width="6.6" height="6.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.15"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.114 0 0 0 0 0.349 0 0 0 0 0.82 0 0 0 0.3 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="enhancedCenterShadow" x="4.9" y="4.9" width="6.2" height="6.2" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.18"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.114 0 0 0 0 0.349 0 0 0 0 0.82 0 0 0 0.6 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="enhancedCheckGlow" x="6.3" y="6.5" width="3.4" height="3" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.18"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.95 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
  </defs>
</svg>