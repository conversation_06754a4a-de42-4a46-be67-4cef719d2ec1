<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Ultra-Premium Ambient Background -->
  <ellipse cx="6" cy="7" rx="6" ry="6" fill="url(#ambientGlow)" opacity="0.15" filter="url(#ambientFilter)"/>
  
  <!-- Luxury Asset Base with Premium Gold Finish -->
  <path d="M3 4.5L6 3L9 4.5L6 6L3 4.5Z" fill="url(#ultraPremiumTopGradient)" filter="url(#enhancedAssetGlow)" stroke="url(#assetTopEdgeGradient)" stroke-width="0.15" stroke-opacity="0.8"/>
  <path d="M3 4.5V9.5L6 11V6L3 4.5Z" fill="url(#ultraPremiumSideGradient)" filter="url(#enhancedAssetSideShadow)" stroke="url(#assetSideEdgeGradient)" stroke-width="0.15" stroke-opacity="0.5"/>
  <path d="M6 6V11L9 9.5V4.5L6 6Z" fill="url(#ultraPremiumFrontGradient)" filter="url(#enhancedAssetFrontShadow)" stroke="url(#assetFrontEdgeGradient)" stroke-width="0.15" stroke-opacity="0.6"/>
  
  <!-- Ultra-Premium Surface Detail -->
  <path d="M4.2 5.2L6 4.3L7.8 5.2L6 6.1L4.2 5.2Z" fill="url(#surfaceDetailGradient)" opacity="0.7"/>
  <path d="M4.6 7L5.4 7.4M4.6 8L5.4 8.4" stroke="url(#engravedDetailGradient)" stroke-width="0.15" stroke-linecap="round" stroke-opacity="0.6"/>
  
  <!-- Exquisite First QR Tag with Diamond Finish -->
  <path d="M10.5 3.5L14.5 2.5V6.5L10.5 7.5V3.5Z" fill="url(#tagPremiumGradient)" filter="url(#enhancedTagShadow)"/>
  <path d="M10.5 3.5L14.5 2.5V6.5L10.5 7.5V3.5Z" stroke="url(#tagPremiumBorderGradient)" stroke-width="0.25" stroke-opacity="0.9"/>
  
  <!-- Precision QR Code Elements on First Tag -->
  <path d="M11.5 4H12.2V4.7H11.5V4ZM12.8 4H13.5V4.7H12.8V4ZM11.5 5.3H12.2V6H11.5V5.3ZM12.8 5.3H13.5V6H12.8V5.3Z" fill="white" opacity="0.95" filter="url(#qrCodeGlow)"/>
  
  <!-- Luxury Second QR Tag with Sapphire Finish -->
  <path d="M12 9.5L15 8V12L12 13.5V9.5Z" fill="url(#tag2PremiumGradient)" filter="url(#enhancedTag2Shadow)"/>
  <path d="M12 9.5L15 8V12L12 13.5V9.5Z" stroke="url(#tag2PremiumBorderGradient)" stroke-width="0.25" stroke-opacity="0.9"/>
  
  <!-- Precision QR Code Elements on Second Tag -->
  <path d="M13 10H13.7V10.7H13V10ZM13 11.3H13.7V12H13V11.3Z" fill="white" opacity="0.95" filter="url(#qrCodeGlow)"/>
  
  <!-- Premium Connection Elements with Platinum Finish -->
  <path d="M9 5L10.5 4.5" stroke="url(#platinumConnectionGradient)" stroke-width="0.45" stroke-linecap="round" filter="url(#enhancedConnectionGlow)"/>
  <path d="M9 9L12 10.5" stroke="url(#platinumConnectionGradient)" stroke-width="0.45" stroke-linecap="round" filter="url(#enhancedConnectionGlow)"/>
  
  <!-- Ultra-Premium Tag Attachment Points with Diamond Effect -->
  <circle cx="10.5" cy="4.5" r="0.55" fill="url(#diamondAttachPointGradient)" filter="url(#enhancedAttachPointGlow)"/>
  <circle cx="12" cy="10.5" r="0.55" fill="url(#diamondAttachPointGradient)" filter="url(#enhancedAttachPointGlow)"/>
  <circle cx="10.5" cy="4.5" r="0.25" fill="white" opacity="0.7"/>
  <circle cx="12" cy="10.5" r="0.25" fill="white" opacity="0.7"/>
  
  <!-- Exquisite Light Reflections -->
  <path d="M5.5 3.8C5.5 3.8 6.2 4.2 7 4.2" stroke="white" stroke-width="0.25" stroke-opacity="0.85" stroke-linecap="round"/>
  <path d="M4 5.2C4 5.2 4.7 5.6 5.5 5.6" stroke="white" stroke-width="0.2" stroke-opacity="0.6" stroke-linecap="round"/>
  <path d="M12 3.2C12 3.2 12.8 3.5 13.8 3.5" stroke="white" stroke-width="0.25" stroke-opacity="0.85" stroke-linecap="round"/>
  <path d="M13 9.2C13 9.2 13.8 9.5 14.4 9.5" stroke="white" stroke-width="0.25" stroke-opacity="0.85" stroke-linecap="round"/>
  <ellipse cx="7.2" cy="4.8" rx="0.15" ry="0.08" transform="rotate(-15 7.2 4.8)" fill="white" opacity="0.95"/>
  <ellipse cx="4.5" cy="5.5" rx="0.1" ry="0.15" fill="white" opacity="0.7"/>
  <ellipse cx="11.3" cy="4.2" rx="0.12" ry="0.08" fill="white" opacity="0.9"/>
  <ellipse cx="13.3" cy="10.2" rx="0.12" ry="0.08" fill="white" opacity="0.9"/>
  
  <!-- Ultra-Premium Definitions for Luxury Gradients and Effects -->
  <defs>
    <!-- Ambient Background Glow -->
    <radialGradient id="ambientGlow" cx="6" cy="7" r="6" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFDF80" stop-opacity="0.4"/>
      <stop offset="0.6" stop-color="#FFDF80" stop-opacity="0.1"/>
      <stop offset="1" stop-color="#FFDF80" stop-opacity="0"/>
    </radialGradient>
    
    <!-- Ultra Premium Asset Gradients -->
    <linearGradient id="ultraPremiumTopGradient" x1="3" y1="4.5" x2="9" y2="4.5" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFC400"/>
      <stop offset="0.2" stop-color="#FFDC73"/>
      <stop offset="0.4" stop-color="#FFF0C2"/>
      <stop offset="0.6" stop-color="#FFDC73"/>
      <stop offset="0.8" stop-color="#FFC400"/>
      <stop offset="1" stop-color="#FFAD0D"/>
    </linearGradient>
    
    <linearGradient id="ultraPremiumSideGradient" x1="3" y1="7" x2="6" y2="7" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#D6A000"/>
      <stop offset="0.3" stop-color="#FFB800"/>
      <stop offset="0.7" stop-color="#FFCB45"/>
      <stop offset="1" stop-color="#FFD466"/>
    </linearGradient>
    
    <linearGradient id="ultraPremiumFrontGradient" x1="6" y1="7" x2="9" y2="7" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFCB45"/>
      <stop offset="0.3" stop-color="#FFB800"/>
      <stop offset="0.7" stop-color="#FFAA00"/>
      <stop offset="1" stop-color="#D69500"/>
    </linearGradient>
    
    <linearGradient id="assetTopEdgeGradient" x1="3" y1="4.5" x2="9" y2="4.5" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFFFF"/>
      <stop offset="0.5" stop-color="#FFE38C"/>
      <stop offset="1" stop-color="#B98400"/>
    </linearGradient>
    
    <linearGradient id="assetSideEdgeGradient" x1="3" y1="7" x2="6" y2="7" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#B98400"/>
      <stop offset="1" stop-color="#FFE38C"/>
    </linearGradient>
    
    <linearGradient id="assetFrontEdgeGradient" x1="6" y1="7" x2="9" y2="7" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFE38C"/>
      <stop offset="1" stop-color="#B98400"/>
    </linearGradient>
    
    <linearGradient id="surfaceDetailGradient" x1="4.2" y1="5.2" x2="7.8" y2="5.2" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFFFF" stop-opacity="0.1"/>
      <stop offset="0.5" stop-color="#FFFFFF" stop-opacity="0.8"/>
      <stop offset="1" stop-color="#FFFFFF" stop-opacity="0.1"/>
    </linearGradient>
    
    <linearGradient id="engravedDetailGradient" x1="4.6" y1="7" x2="5.4" y2="7" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#B98400" stop-opacity="0.8"/>
      <stop offset="1" stop-color="#FFE38C" stop-opacity="0.6"/>
    </linearGradient>
    
    <!-- Ultra Premium Tag Gradients -->
    <linearGradient id="tagPremiumGradient" x1="10.5" y1="3.5" x2="14.5" y2="6.5" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#C2DCFF"/>
      <stop offset="0.2" stop-color="#9CC1FF"/>
      <stop offset="0.5" stop-color="#64A8FF"/>
      <stop offset="0.8" stop-color="#3478F6"/>
      <stop offset="1" stop-color="#1D59D1"/>
    </linearGradient>
    
    <linearGradient id="tagPremiumBorderGradient" x1="10.5" y1="3.5" x2="14.5" y2="6.5" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFFFF"/>
      <stop offset="0.3" stop-color="#C2DCFF"/>
      <stop offset="0.7" stop-color="#3478F6"/>
      <stop offset="1" stop-color="#1D59D1"/>
    </linearGradient>
    
    <linearGradient id="tag2PremiumGradient" x1="12" y1="9.5" x2="15" y2="12" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#64A8FF"/>
      <stop offset="0.3" stop-color="#3478F6"/>
      <stop offset="0.7" stop-color="#1D59D1"/>
      <stop offset="1" stop-color="#0D47A1"/>
    </linearGradient>
    
    <linearGradient id="tag2PremiumBorderGradient" x1="12" y1="9.5" x2="15" y2="12" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#9CC1FF"/>
      <stop offset="0.3" stop-color="#64A8FF"/>
      <stop offset="0.7" stop-color="#1D59D1"/>
      <stop offset="1" stop-color="#0D47A1"/>
    </linearGradient>
    
    <!-- Platinum Connection Gradient -->
    <linearGradient id="platinumConnectionGradient" x1="9" y1="5" x2="12" y2="10.5" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFFFF"/>
      <stop offset="0.2" stop-color="#F0F0F0"/>
      <stop offset="0.4" stop-color="#E0E0E0"/>
      <stop offset="0.6" stop-color="#D0D0D0"/>
      <stop offset="0.8" stop-color="#C0C0C0"/>
      <stop offset="1" stop-color="#A0A0A0"/>
    </linearGradient>
    
    <!-- Diamond Attachment Point Gradient -->
    <radialGradient id="diamondAttachPointGradient" cx="10.5" cy="4.5" r="0.55" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFFFF"/>
      <stop offset="0.3" stop-color="#F0F0F0"/>
      <stop offset="0.7" stop-color="#E0E0E0"/>
      <stop offset="1" stop-color="#C0C0C0"/>
    </radialGradient>
    
    <!-- Enhanced Filter Effects -->
    <filter id="ambientFilter" x="0" y="1" width="12" height="12" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feGaussianBlur stdDeviation="0.5" result="effect1_foregroundBlur"/>
    </filter>
    
    <filter id="enhancedAssetGlow" x="2.5" y="2.5" width="7" height="4" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.3"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.8 0 0 0 0 0.2 0 0 0 0.5 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="enhancedAssetSideShadow" x="2.7" y="4.2" width="3.6" height="7.1" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.2"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.6 0 0 0 0 0.4 0 0 0 0 0 0 0 0 0.4 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="enhancedAssetFrontShadow" x="5.7" y="4.2" width="3.6" height="7.1" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.2"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.8 0 0 0 0 0.6 0 0 0 0 0.2 0 0 0 0.4 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="enhancedTagShadow" x="10.2" y="2.2" width="4.6" height="5.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.18"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.2 0 0 0 0 0.47 0 0 0 0 0.97 0 0 0 0.45 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="-0.2"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
      <feBlend mode="normal" in2="shape" result="effect2_innerShadow"/>
    </filter>
    
    <filter id="enhancedTag2Shadow" x="11.7" y="7.7" width="3.6" height="6.1" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.18"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.114 0 0 0 0 0.349 0 0 0 0 0.82 0 0 0 0.45 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="-0.2"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
      <feBlend mode="normal" in2="shape" result="effect2_innerShadow"/>
    </filter>
    
    <filter id="qrCodeGlow" x="11.3" y="3.8" width="2.4" height="2.4" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="enhancedConnectionGlow" x="8.75" y="4.25" width="3.5" height="6.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.12"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.9 0 0 0 0 0.9 0 0 0 0 0.9 0 0 0 0.7 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="enhancedAttachPointGlow" x="9.7" y="3.7" width="1.6" height="1.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="0.18"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.9 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
  </defs>
</svg>