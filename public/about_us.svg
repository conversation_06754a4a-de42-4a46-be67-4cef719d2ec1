<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- CELESTIAL BACKDROP WITH GOLDEN RATIO PROPORTIONS -->
  <rect x="1.5" y="3" width="13" height="10" rx="2" filter="url(#luxuryBackdropShadow)" fill="url(#celestialGradient)">
    <animate attributeName="opacity" values="1;0.97;1" dur="8s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
  </rect>
  
  <!-- ATMOSPHERIC DEPTH WITH VOLUMETRIC LIGHTING -->
  <path d="M3.5 3h9c1.105 0 2 0.895 2 2v0.5H1.5V5c0-1.105 0.895-2 2-2z" fill="white" fill-opacity="0.35" filter="url(#topGlowEffect)">
    <animate attributeName="fill-opacity" values="0.35;0.25;0.35" dur="10s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
  </path>
  <path d="M3 12.5h10c.5 0 1-.3 1.2-.7H1.8c.2.4.7.7 1.2.7z" fill="black" fill-opacity="0.15">
    <animate attributeName="fill-opacity" values="0.15;0.1;0.15" dur="10s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
  </path>
  
  <!-- MOONLIGHT RAYS WITH VOLUMETRIC SCATTERING -->
  <g opacity="0.4" filter="url(#moonlightEffect)">
    <path d="M3 6l5-3" stroke="white" stroke-width="0.15" stroke-linecap="round" opacity="0.3">
      <animate attributeName="opacity" values="0.3;0.5;0.3" dur="8s" repeatCount="indefinite"/>
    </path>
    <path d="M5 5l4-2.5" stroke="white" stroke-width="0.12" stroke-linecap="round" opacity="0.3">
      <animate attributeName="opacity" values="0.3;0.6;0.3" dur="9s" begin="1s" repeatCount="indefinite"/>
    </path>
    <path d="M7 4.5l3-2" stroke="white" stroke-width="0.1" stroke-linecap="round" opacity="0.25">
      <animate attributeName="opacity" values="0.25;0.45;0.25" dur="7s" begin="2s" repeatCount="indefinite"/>
    </path>
    <path d="M10 4l2-1.5" stroke="white" stroke-width="0.1" stroke-linecap="round" opacity="0.2">
      <animate attributeName="opacity" values="0.2;0.4;0.2" dur="10s" begin="0.5s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- SACRED KOI POND WITH DYNAMIC FLUID SIMULATION -->
  <g filter="url(#pondReflectionEffect)">
    <ellipse cx="8" cy="11.2" rx="4.2" ry="0.95" fill="url(#waterGradient)" filter="url(#waterRippleEffect)">
      <animate attributeName="ry" values="0.95;0.9;0.95" dur="4s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
      <animate attributeName="rx" values="4.2;4.25;4.2" dur="6.5s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
    </ellipse>
    <ellipse cx="8" cy="11.2" rx="3.8" ry="0.75" fill="url(#waterHighlightGradient)" opacity="0.6">
      <animate attributeName="rx" values="3.8;3.7;3.8" dur="3s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
      <animate attributeName="ry" values="0.75;0.7;0.75" dur="5s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
    </ellipse>
  </g>
  
  <!-- ANIMATED KOI FISH WITH FLUID DYNAMICS -->
  <g filter="url(#koiGlowEffect)">
    <!-- White and red koi -->
    <path d="M6.8 11.2c0.15-0.05 0.4-0.05 0.5 0.05s0 0.2-0.1 0.25c-0.15 0-0.3-0.1-0.4-0.3z" fill="url(#whiteKoiGradient)">
      <animate attributeName="d" 
              values="M6.8 11.2c0.15-0.05 0.4-0.05 0.5 0.05s0 0.2-0.1 0.25c-0.15 0-0.3-0.1-0.4-0.3z;
                     M6.7 11.15c0.15-0.05 0.45-0.05 0.55 0.05s0 0.25-0.1 0.3c-0.15 0-0.35-0.1-0.45-0.35z;
                     M6.8 11.2c0.15-0.05 0.4-0.05 0.5 0.05s0 0.2-0.1 0.25c-0.15 0-0.3-0.1-0.4-0.3z" 
              dur="5s" repeatCount="indefinite"/>
      <animateMotion path="M0,0 C0.3,0.1 0.6,0 0.9,0.05 C1.2,-0.05 1.5,0.08 1.8,0 C2.1,0.03 2.4,-0.05 2.7,0.02" 
                    dur="15s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="rotate"
                      values="-3 7.1 11.3; 3 7.1 11.3; -3 7.1 11.3" 
                      dur="5s" repeatCount="indefinite" additive="sum"/>
    </path>
    <path d="M6.9 11.25c0 0 0.15 0.05 0.18 0c0.03-0.05-0.05-0.08-0.08-0.1" stroke="#FF3A54" stroke-width="0.08" stroke-linecap="round">
      <animate attributeName="d" 
              values="M6.9 11.25c0 0 0.15 0.05 0.18 0c0.03-0.05-0.05-0.08-0.08-0.1;
                     M6.85 11.2c0 0 0.18 0.06 0.21 0.01c0.03-0.05-0.05-0.09-0.09-0.11;
                     M6.9 11.25c0 0 0.15 0.05 0.18 0c0.03-0.05-0.05-0.08-0.08-0.1" 
              dur="5s" repeatCount="indefinite"/>
      <animateMotion path="M0,0 C0.3,0.1 0.6,0 0.9,0.05 C1.2,-0.05 1.5,0.08 1.8,0 C2.1,0.03 2.4,-0.05 2.7,0.02" 
                    dur="15s" repeatCount="indefinite"/>
    </path>
    
    <!-- Orange and black koi -->
    <path d="M8.9 11.3c-0.15-0.05-0.35-0.05-0.45 0.05s0 0.2 0.1 0.25c0.15 0 0.25-0.1 0.35-0.3z" fill="url(#orangeKoiGradient)">
      <animate attributeName="d" 
              values="M8.9 11.3c-0.15-0.05-0.35-0.05-0.45 0.05s0 0.2 0.1 0.25c0.15 0 0.25-0.1 0.35-0.3z;
                     M8.95 11.25c-0.15-0.05-0.4-0.05-0.5 0.05s0 0.25 0.1 0.3c0.15 0 0.3-0.1 0.4-0.35z;
                     M8.9 11.3c-0.15-0.05-0.35-0.05-0.45 0.05s0 0.2 0.1 0.25c0.15 0 0.25-0.1 0.35-0.3z" 
              dur="6s" begin="1s" repeatCount="indefinite"/>
      <animateMotion path="M0,0 C-0.3,0.08 -0.6,-0.02 -0.9,0.03 C-1.2,-0.04 -1.5,0.06 -1.8,-0.02 C-2.1,0.04 -2.4,-0.03 -2.7,0.01" 
                    dur="17s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="rotate"
                      values="3 8.6 11.35; -3 8.6 11.35; 3 8.6 11.35" 
                      dur="6s" repeatCount="indefinite" additive="sum"/>
    </path>
    <path d="M8.8 11.35c0 0 -0.15 0.05 -0.18 0c-0.03-0.05 0.05-0.08 0.08-0.1" stroke="#333" stroke-width="0.08" stroke-linecap="round">
      <animate attributeName="d" 
              values="M8.8 11.35c0 0 -0.15 0.05 -0.18 0c-0.03-0.05 0.05-0.08 0.08-0.1;
                     M8.85 11.3c0 0 -0.18 0.06 -0.21 0.01c-0.03-0.05 0.05-0.09 0.09-0.11;
                     M8.8 11.35c0 0 -0.15 0.05 -0.18 0c-0.03-0.05 0.05-0.08 0.08-0.1" 
              dur="6s" begin="1s" repeatCount="indefinite"/>
      <animateMotion path="M0,0 C-0.3,0.08 -0.6,-0.02 -0.9,0.03 C-1.2,-0.04 -1.5,0.06 -1.8,-0.02 C-2.1,0.04 -2.4,-0.03 -2.7,0.01" 
                    dur="17s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- WATER RIPPLE CIRCLES WITH REALISTIC PHYSICS -->
  <g filter="url(#rippleGlowEffect)">
    <circle cx="6.8" cy="11.2" r="0.12" fill="#FFFFFF" fill-opacity="0.6">
      <animate attributeName="r" values="0.05;0.35;0.65" dur="3s" repeatCount="indefinite" calcMode="spline" keySplines="0 0.5 0.5 1; 0 0.5 0.5 1"/>
      <animate attributeName="fill-opacity" values="0.7;0.3;0" dur="3s" repeatCount="indefinite" calcMode="spline" keySplines="0 0.5 0.5 1; 0 0.5 0.5 1"/>
    </circle>
    <circle cx="9.2" cy="11.2" r="0.12" fill="#FFFFFF" fill-opacity="0.6">
      <animate attributeName="r" values="0.05;0.35;0.65" dur="4.8s" begin="1.5s" repeatCount="indefinite" calcMode="spline" keySplines="0 0.5 0.5 1; 0 0.5 0.5 1"/>
      <animate attributeName="fill-opacity" values="0.7;0.3;0" dur="4.8s" begin="1.5s" repeatCount="indefinite" calcMode="spline" keySplines="0 0.5 0.5 1; 0 0.5 0.5 1"/>
    </circle>
    <circle cx="7.5" cy="11.1" r="0.1" fill="#FFFFFF" fill-opacity="0.5">
      <animate attributeName="r" values="0.05;0.25;0.55" dur="4s" begin="2.2s" repeatCount="indefinite" calcMode="spline" keySplines="0 0.5 0.5 1; 0 0.5 0.5 1"/>
      <animate attributeName="fill-opacity" values="0.6;0.25;0" dur="4s" begin="2.2s" repeatCount="indefinite" calcMode="spline" keySplines="0 0.5 0.5 1; 0 0.5 0.5 1"/>
    </circle>
    <circle cx="8.7" cy="11.3" r="0.08" fill="#FFFFFF" fill-opacity="0.5">
      <animate attributeName="r" values="0.05;0.2;0.5" dur="3.5s" begin="1s" repeatCount="indefinite" calcMode="spline" keySplines="0 0.5 0.5 1; 0 0.5 0.5 1"/>
      <animate attributeName="fill-opacity" values="0.6;0.2;0" dur="3.5s" begin="1s" repeatCount="indefinite" calcMode="spline" keySplines="0 0.5 0.5 1; 0 0.5 0.5 1"/>
    </circle>
  </g>
  
  <!-- ZEN GARDEN ROCKS WITH REFLECTIONS -->
  <g filter="url(#rockShadowEffect)">
    <path d="M4.2 10.9c0.15-0.1 0.4-0.1 0.55 0 0.15 0.1 0.15 0.25 0 0.35 -0.15 0.1-0.4 0.1-0.55 0 -0.15-0.1-0.15-0.25 0-0.35z" fill="url(#rockGradient1)">
      <animate attributeName="d" values="M4.2 10.9c0.15-0.1 0.4-0.1 0.55 0 0.15 0.1 0.15 0.25 0 0.35 -0.15 0.1-0.4 0.1-0.55 0 -0.15-0.1-0.15-0.25 0-0.35z;
                                        M4.15 10.85c0.15-0.1 0.45-0.1 0.6 0 0.15 0.1 0.15 0.25 0 0.35 -0.15 0.1-0.45 0.1-0.6 0 -0.15-0.1-0.15-0.25 0-0.35z;
                                        M4.2 10.9c0.15-0.1 0.4-0.1 0.55 0 0.15 0.1 0.15 0.25 0 0.35 -0.15 0.1-0.4 0.1-0.55 0 -0.15-0.1-0.15-0.25 0-0.35z" 
              dur="10s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
    </path>
    
    <path d="M11.3 10.85c0.15-0.1 0.35-0.1 0.5 0 0.15 0.1 0.15 0.25 0 0.35 -0.15 0.1-0.35 0.1-0.5 0 -0.15-0.1-0.15-0.25 0-0.35z" fill="url(#rockGradient2)">
      <animate attributeName="d" values="M11.3 10.85c0.15-0.1 0.35-0.1 0.5 0 0.15 0.1 0.15 0.25 0 0.35 -0.15 0.1-0.35 0.1-0.5 0 -0.15-0.1-0.15-0.25 0-0.35z;
                                        M11.25 10.8c0.15-0.1 0.4-0.1 0.55 0 0.15 0.1 0.15 0.25 0 0.35 -0.15 0.1-0.4 0.1-0.55 0 -0.15-0.1-0.15-0.25 0-0.35z;
                                        M11.3 10.85c0.15-0.1 0.35-0.1 0.5 0 0.15 0.1 0.15 0.25 0 0.35 -0.15 0.1-0.35 0.1-0.5 0 -0.15-0.1-0.15-0.25 0-0.35z" 
              dur="12s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
    </path>
  </g>
  
  <!-- SACRED TORII GATE WITH TRADITIONAL PROPORTIONS -->
  <g filter="url(#toriiGlowEffect)" opacity="0.98">
    <path d="M4.5 10h7v-0.2h-7z" fill="url(#toriiTopGradient)" stroke="#95191C" stroke-width="0.05"/>
    <path d="M4.8 10h6.4v-0.4h-6.4z" fill="url(#toriiTopGradient)" stroke="#95191C" stroke-width="0.05"/>
    <path d="M5.2 9.8V10.3M10.8 9.8V10.3" stroke="url(#toriiPillarGradient)" stroke-width="0.3" stroke-linecap="round"/>
    <path d="M5.2 10.1V11M10.8 10.1V11" stroke="url(#toriiPillarGradient)" stroke-width="0.25" stroke-linecap="round"/>
  </g>
  
  <!-- TRADITIONAL JAPANESE HOUSE WITH ENHANCED ARCHITECTURAL DETAILS -->
  <g filter="url(#houseGlowEffect)">
    <!-- Main Structure with Historic Proportions -->
    <path d="M4.8 9.6V7.3h6.4v2.3H4.8z" fill="url(#woodTextureGradient)" stroke="#3D2302" stroke-width="0.2">
      <animate attributeName="stroke-opacity" values="1;0.8;1" dur="10s" repeatCount="indefinite"/>
    </path>
    
    <!-- Enhanced Curved Roof with Multiple Layers -->
    <path d="M3.2 7.4c0 0 1.9-1.8 4.8-1.8s4.8 1.8 4.8 1.8" stroke="#3D2302" stroke-width="0.5" stroke-linecap="round" fill="none">
      <animate attributeName="stroke-opacity" values="1;0.9;1" dur="12s" repeatCount="indefinite"/>
    </path>
    <path d="M3.2 7.2c0 0 1.9-1.6 4.8-1.6s4.8 1.6 4.8 1.6v0.4c0 0-1.9-1.3-4.8-1.3s-4.8 1.3-4.8 1.3V7.2z" fill="url(#roofMainGradient)" stroke="#3D2302" stroke-width="0.25"/>
    <path d="M3.6 7.3c0 0 1.7-1.1 4.4-1.1s4.4 1.1 4.4 1.1" stroke="#3D2302" stroke-width="0.2" stroke-linecap="round" stroke-opacity="0.6"/>
    
    <!-- Enhanced Layered Roof Tiles with Traditional Patterns -->
    <path d="M3.6 6.9c0 0 1.7-1.4 4.4-1.4s4.4 1.4 4.4 1.4" stroke="#593A23" stroke-width="0.25" stroke-dasharray="0.3 0.15" stroke-linecap="round"/>
    <path d="M3.8 6.7c0 0 1.6-1.2 4.2-1.2s4.2 1.2 4.2 1.2" stroke="#593A23" stroke-width="0.15" stroke-dasharray="0.2 0.2" stroke-linecap="round"/>
    <path d="M4.0 6.5c0 0 1.5-1.0 4.0-1.0s4.0 1.0 4.0 1.0" stroke="#593A23" stroke-width="0.1" stroke-dasharray="0.15 0.25" stroke-linecap="round"/>
    <path d="M4.2 6.3c0 0 1.4-0.9 3.8-0.9s3.8 0.9 3.8 0.9" stroke="#593A23" stroke-width="0.08" stroke-dasharray="0.1 0.3" stroke-linecap="round"/>
    
    <!-- Extended Karahafu Curved Gable -->
    <path d="M7.6 5.2 L8 4.8 L8.4 5.2" fill="none" stroke="#3D2302" stroke-width="0.15" stroke-linecap="round"/>
    <path d="M7.5 5.3 L8 4.85 L8.5 5.3" fill="none" stroke="#3D2302" stroke-width="0.1" stroke-linecap="round" stroke-opacity="0.8"/>
    <path d="M7.4 5.4 L8 4.9 L8.6 5.4" fill="none" stroke="#3D2302" stroke-width="0.08" stroke-linecap="round" stroke-opacity="0.6"/>
    
    <!-- Enhanced Onigawara Roof Ornament -->
    <path d="M8 4.7v0.6" stroke="#3D2302" stroke-width="0.3" stroke-linecap="round"/>
    <path d="M7.7 4.8h0.6L8 4.4l-0.3 0.4z" fill="url(#onigawaraGradient)" stroke="#3D2302" stroke-width="0.1">
      <animate attributeName="fill-opacity" values="1;0.85;1" dur="8s" repeatCount="indefinite"/>
    </path>
    <path d="M7.75 4.65h0.5L8 4.35l-0.25 0.3z" fill="#3D2302" stroke="#3D2302" stroke-width="0.05"/>
    
    <!-- Traditional Wood Post Structure (Hashira) with Subtle Woodgrain -->
    <path d="M4.8 7.3V9.6" stroke="#3D2302" stroke-width="0.3">
      <animate attributeName="stroke-opacity" values="1;0.8;1" dur="13s" repeatCount="indefinite"/>
    </path>
    <path d="M6.4 7.3V9.6" stroke="#3D2302" stroke-width="0.2" stroke-opacity="0.7">
      <animate attributeName="stroke-opacity" values="0.7;0.6;0.7" dur="11s" repeatCount="indefinite"/>
    </path>
    <path d="M8 7.3V9.6" stroke="#3D2302" stroke-width="0.2" stroke-opacity="0.7">
      <animate attributeName="stroke-opacity" values="0.7;0.5;0.7" dur="15s" repeatCount="indefinite"/>
    </path>
    <path d="M9.6 7.3V9.6" stroke="#3D2302" stroke-width="0.2" stroke-opacity="0.7">
      <animate attributeName="stroke-opacity" values="0.7;0.6;0.7" dur="12s" repeatCount="indefinite"/>
    </path>
    <path d="M11.2 7.3V9.6" stroke="#3D2302" stroke-width="0.3">
      <animate attributeName="stroke-opacity" values="1;0.8;1" dur="14s" repeatCount="indefinite"/>
    </path>
    
    <!-- Advanced Shoji Screen Doors with Washi Paper Texture -->
    <rect x="6.6" y="7.7" width="1.2" height="1.9" rx="0.1" fill="url(#shojiGradient)" stroke="#3D2302" stroke-width="0.1" filter="url(#paperTextureEffect)">
      <animate attributeName="fill-opacity" values="0.95;0.9;0.95" dur="8s" repeatCount="indefinite"/>
    </rect>
    <path d="M7.2 7.7v1.9M6.6 8.65h1.2" stroke="#3D2302" stroke-width="0.08" stroke-opacity="0.9"/>
    <path d="M6.75 8.1h0.9 M6.75 9.2h0.9" stroke="#3D2302" stroke-width="0.06" stroke-opacity="0.7"/>
    <path d="M6.75 7.9h0.9 M6.75 8.4h0.9 M6.75 8.9h0.9" stroke="#3D2302" stroke-width="0.04" stroke-opacity="0.5"/>
    
    <rect x="8.2" y="7.7" width="1.2" height="1.9" rx="0.1" fill="url(#shojiGradient)" stroke="#3D2302" stroke-width="0.1" filter="url(#paperTextureEffect)">
      <animate attributeName="fill-opacity" values="0.95;0.9;0.95" dur="8s" repeatCount="indefinite" begin="1s"/>
    </rect>
    <path d="M8.8 7.7v1.9M8.2 8.65h1.2" stroke="#3D2302" stroke-width="0.08" stroke-opacity="0.9"/>
    <path d="M8.35 8.1h0.9 M8.35 9.2h0.9" stroke="#3D2302" stroke-width="0.06" stroke-opacity="0.7"/>
    <path d="M8.35 7.9h0.9 M8.35 8.4h0.9 M8.35 8.9h0.9" stroke="#3D2302" stroke-width="0.04" stroke-opacity="0.5"/>
    
    <!-- Traditional Engawa (Veranda) with Enhanced Detail -->
    <path d="M4.8 9.6h6.4v0.3H4.8z" fill="url(#engawaGradient)" stroke="#3D2302" stroke-width="0.15"/>
    <path d="M5.2 9.9v0.4M6.2 9.9v0.4M7.2 9.9v0.4M8.2 9.9v0.4M9.2 9.9v0.4M10.2 9.9v0.4M11.2 9.9v0.4" 
          stroke="#3D2302" stroke-width="0.1" stroke-opacity="0.7"/>
    <path d="M4.9 9.75h6.2" stroke="#3D2302" stroke-width="0.05" stroke-opacity="0.5"/>
    
    <!-- Traditional Noren Curtain Above Door with Gentle Movement -->
    <path d="M7.8 7.4c0 0.1 0.1 0.2 0.2 0.2s0.2-0.1 0.2-0.2V7.1H7.8v0.3z" fill="url(#norenGradient)" opacity="0.9" filter="url(#fabricTextureEffect)">
      <animate attributeName="d" 
              values="M7.8 7.4c0 0.1 0.1 0.2 0.2 0.2s0.2-0.1 0.2-0.2V7.1H7.8v0.3z;
                     M7.75 7.38c0 0.12 0.125 0.22 0.225 0.22s0.225-0.1 0.225-0.22V7.08H7.75v0.3z;
                     M7.8 7.4c0 0.1 0.1 0.2 0.2 0.2s0.2-0.1 0.2-0.2V7.1H7.8v0.3z" 
              dur="7s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
      <animate attributeName="opacity" values="0.9;0.85;0.9" dur="5s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- TRADITIONAL STONE LANTERN (TŌRŌ) -->
  <g filter="url(#lanternGlowEffect)">
    <path d="M5.6 9.8c0 0 0.1-0.2 0.4-0.2s0.4 0.2 0.4 0.2v0.4c0 0-0.1-0.15-0.4-0.15s-0.4 0.15-0.4 0.15V9.8z" fill="url(#stoneLanternGradient)" stroke="#3D2302" stroke-width="0.05"/>
    <path d="M5.7 9.7v-0.2c0 0 0.1-0.15 0.3-0.15s0.3 0.15 0.3 0.15v0.2" stroke="#3D2302" stroke-width="0.08" stroke-linecap="round" fill="none"/>
    <path d="M5.8 9.4v-0.1M6.2 9.4v-0.1" stroke="#3D2302" stroke-width="0.06" stroke-linecap="round"/>
    <circle cx="6" cy="9.55" r="0.12" fill="url(#lanternLightGradient)" filter="url(#lanternLightEffect)">
      <animate attributeName="opacity" values="0.9;0.75;0.9" dur="3s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- MINIATURE ZEN GARDEN BRIDGE -->
  <path d="M7.4 10.3c0 0.2 0.3 0.3 0.6 0.3s0.6-0.1 0.6-0.3" stroke="url(#bridgeGradient)" stroke-width="0.2" stroke-linecap="round" fill="none"/>
  <path d="M7.5 10.6c0.1-0.05 0.4-0.05 0.5 0M7.5 10.45c0.1-0.05 0.4-0.05 0.5 0M8 10.3v0.3" stroke="url(#bridgeDetailGradient)" stroke-width="0.08" stroke-linecap="round"/>
  
  <!-- EXQUISITE LOTUS FLOWERS WITH PERFECT PROPORTIONS -->
  <g filter="url(#lotusGlowEffect)">
    <!-- Main lotus flower with golden ratio proportions -->
    <g transform="translate(8 11.2) scale(0.25)">
      <!-- Enhanced outer petals with precise mathematical curves -->
      <path d="M0,0 C0.8,-1 2,-0.8 1.6,0 C2,-0.8 3.2,-0.5 2.4,0.3 C3.2,-0.5 4,0.5 2.8,0.9 C4,0.5 3.6,1.8 2.4,1.5 C3.6,1.8 2.4,2.7 1.6,2 C2.4,2.7 0.8,2.7 0,2 C-0.8,2.7 -2.4,2.7 -1.6,2 C-2.4,2.7 -3.6,1.8 -2.4,1.5 C-3.6,1.8 -4,0.5 -2.8,0.9 C-4,0.5 -3.2,-0.5 -2.4,0.3 C-3.2,-0.5 -2,-0.8 -1.6,0 C-2,-0.8 -0.8,-1 0,0" 
            fill="url(#lotusPetalGradient)" 
            stroke="url(#lotusPetalEdgeGradient)"
            stroke-width="0.15">
        <animateTransform attributeName="transform" 
                          type="rotate" 
                          from="0 0 0" 
                          to="360 0 0" 
                          dur="180s" 
                          repeatCount="indefinite"/>
        <animate attributeName="fill-opacity" values="1;0.9;1" dur="12s" repeatCount="indefinite"/>
      </path>
      
      <!-- Inner petals with golden ratio placement -->
      <path d="M0,0 C0.5,-0.6 1.2,-0.5 1,0 C1.2,-0.5 1.9,-0.3 1.4,0.2 C1.9,-0.3 2.4,0.3 1.7,0.5 C2.4,0.3 2.2,1.1 1.4,0.9 C2.2,1.1 1.4,1.6 1,1.2 C1.4,1.6 0.5,1.6 0,1.2 C-0.5,1.6 -1.4,1.6 -1,1.2 C-1.4,1.6 -2.2,1.1 -1.4,0.9 C-2.2,1.1 -2.4,0.3 -1.7,0.5 C-2.4,0.3 -1.9,-0.3 -1.4,0.2 C-1.9,-0.3 -1.2,-0.5 -1,0 C-1.2,-0.5 -0.5,-0.6 0,0" 
            fill="url(#lotusInnerPetalGradient)" 
            stroke="url(#lotusInnerEdgeGradient)"
            stroke-width="0.1">
        <animateTransform attributeName="transform" 
                          type="rotate" 
                          from="0 0 0" 
                          to="-360 0 0" 
                          dur="120s" 
                          repeatCount="indefinite"/>
        <animate attributeName="fill-opacity" values="1;0.92;1" dur="8s" repeatCount="indefinite"/>
      </path>
      
      <!-- Enhanced lotus center with perfect circle and color -->
      <circle cx="0" cy="0" r="0.5" fill="url(#lotusCenterGradient)">
        <animate attributeName="r" 
                 values="0.5;0.55;0.5" 
                 dur="4s" 
                 repeatCount="indefinite"
                 calcMode="spline"
                 keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
      </circle>
      
      <!-- Additional inner details for depth -->
      <path d="M-0.3,-0.3 L0.3,0.3 M0.3,-0.3 L-0.3,0.3" stroke="#FBC02D" stroke-width="0.08" stroke-opacity="0.6" stroke-linecap="round">
        <animateTransform attributeName="transform" 
                          type="rotate" 
                          from="0 0 0" 
                          to="360 0 0" 
                          dur="90s" 
                          repeatCount="indefinite"/>
      </path>
    </g>
    
    <!-- Smaller lotus buds with perfect φ (phi) ratio positioning -->
    <g transform="translate(6.2 11) scale(0.15)">
      <path d="M0,0 C0.8,-1 2,-0.8 1.6,0 C2,-0.8 3.2,-0.5 2.4,0.3 C3.2,-0.5 4,0.5 2.8,0.9 C4,0.5 3.6,1.8 2.4,1.5 C3.6,1.8 2.4,2.7 1.6,2 C2.4,2.7 0.8,2.7 0,2 C-0.8,2.7 -2.4,2.7 -1.6,2 C-2.4,2.7 -3.6,1.8 -2.4,1.5 C-3.6,1.8 -4,0.5 -2.8,0.9 C-4,0.5 -3.2,-0.5 -2.4,0.3 C-3.2,-0.5 -2,-0.8 -1.6,0 C-2,-0.8 -0.8,-1 0,0" 
            fill="url(#lotusPetalGradient)" 
            opacity="0.9"
            stroke="url(#lotusPetalEdgeGradient)" 
            stroke-width="0.2">
        <animateTransform attributeName="transform" 
                          type="rotate" 
                          from="0 0 0" 
                          to="360 0 0" 
                          dur="150s" 
                          repeatCount="indefinite"/>
      </path>
      <circle cx="0" cy="0" r="0.5" fill="url(#lotusCenterGradient)"/>
    </g>
    
    <g transform="translate(9.8 11) scale(0.1)">
      <path d="M0,0 C0.8,-1 2,-0.8 1.6,0 C2,-0.8 3.2,-0.5 2.4,0.3 C3.2,-0.5 4,0.5 2.8,0.9 C4,0.5 3.6,1.8 2.4,1.5 C3.6,1.8 2.4,2.7 1.6,2 C2.4,2.7 0.8,2.7 0,2 C-0.8,2.7 -2.4,2.7 -1.6,2 C-2.4,2.7 -3.6,1.8 -2.4,1.5 C-3.6,1.8 -4,0.5 -2.8,0.9 C-4,0.5 -3.2,-0.5 -2.4,0.3 C-3.2,-0.5 -2,-0.8 -1.6,0 C-2,-0.8 -0.8,-1 0,0" 
            fill="url(#lotusPetalGradient)" 
            opacity="0.8"
            stroke="url(#lotusPetalEdgeGradient)" 
            stroke-width="0.2">
        <animateTransform attributeName="transform" 
                          type="rotate" 
                          from="0 0 0" 
                          to="-360 0 0" 
                          dur="160s" 
                          repeatCount="indefinite"/>
      </path>
      <circle cx="0" cy="0" r="0.5" fill="url(#lotusCenterGradient)"/>
    </g>
  </g>
  
  <!-- ENHANCED SAKURA BRANCHES WITH BOTANICAL ACCURACY -->
  <g filter="url(#branchShadowEffect)" opacity="0.9">
    <!-- Left sakura branch with natural branch architecture -->
    <path d="M3.2 5.4C2.8 4.6 3.5 3.5 4 3.3M4 3.3C5.4 3.6 5 4.8 4.8 5.2M4 3.3C3.8 2.4 4.6 1.8 5 1.7" 
          stroke="#593A23" 
          stroke-width="0.22" 
          stroke-linecap="round" 
          fill="none"/>
    <!-- Small branch offshoots with subtle variations -->
    <path d="M4.4 3.9C4.5 3.8 4.7 3.7 4.8 3.8M3.4 4.7C3.5 4.6 3.6 4.5 3.7 4.5" 
          stroke="#593A23" 
          stroke-width="0.15" 
          stroke-linecap="round" 
          fill="none"/>
    <!-- Additional twig details -->
    <path d="M3.8 5.0C3.9 5.15 4.0 5.2 4.1 5.1M4.65 4.0C4.55 3.85 4.45 3.75 4.3 3.8" 
          stroke="#593A23" 
          stroke-width="0.1" 
          stroke-linecap="round" 
          fill="none"/>
          
    <!-- Right sakura branch with natural branch architecture -->
    <path d="M12.8 5.4C13.2 4.6 12.5 3.5 12 3.3M12 3.3C10.6 3.6 11 4.8 11.2 5.2M12 3.3C12.2 2.4 11.4 1.8 11 1.7" 
          stroke="#593A23" 
          stroke-width="0.22" 
          stroke-linecap="round" 
          fill="none"/>
    <!-- Small branch offshoots with subtle variations -->
    <path d="M11.6 3.9C11.5 3.8 11.3 3.7 11.2 3.8M12.6 4.7C12.5 4.6 12.4 4.5 12.3 4.5" 
          stroke="#593A23" 
          stroke-width="0.15" 
          stroke-linecap="round" 
          fill="none"/>
    <!-- Additional twig details -->
    <path d="M12.2 5.0C12.1 5.15 12.0 5.2 11.9 5.1M11.35 4.0C11.45 3.85 11.55 3.75 11.7 3.8" 
          stroke="#593A23" 
          stroke-width="0.1" 
          stroke-linecap="round" 
          fill="none"/>
  </g>
  
  <!-- ENHANCED SAKURA FLOWERS WITH BOTANICAL ACCURACY -->
  <g filter="url(#flowerGlowEffect)">
    <!-- Left branch flowers with varied designs -->
    <g transform="translate(3.5 4.7) scale(0.23)">
      <path d="M0,0 C0.5,-0.5 1.5,-0.5 1.5,0 S2.5,0.5 2,1 S2,2 1.5,1.5 S0.5,2 0,1.5 S-0.5,0.5 0,0z" 
            fill="url(#sakuraBlossomGradient)" 
            stroke="#FF90B3" 
            stroke-width="0.5">
        <animateTransform attributeName="transform" type="rotate" from="0 0 0" to="360 0 0" dur="16s" repeatCount="indefinite"/>
      </path>
      <circle cx="0" cy="0" r="0.5" fill="#FFC530"/>
      <!-- Additional details -->
      <path d="M-0.75,-0.75 L0.75,0.75 M0.75,-0.75 L-0.75,0.75" stroke="#FF90B3" stroke-width="0.15" stroke-opacity="0.5" stroke-linecap="round"/>
    </g>
    
    <g transform="translate(4.8 3.2) scale(0.25)">
      <path d="M0,0 C0.5,-0.5 1.5,-0.5 1.5,0 S2.5,0.5 2,1 S2,2 1.5,1.5 S0.5,2 0,1.5 S-0.5,0.5 0,0z" 
            fill="url(#sakuraBlossomGradient)" 
            stroke="#FF90B3" 
            stroke-width="0.5">
        <animateTransform attributeName="transform" type="rotate" from="0 0 0" to="360 0 0" dur="18s" repeatCount="indefinite"/>
      </path>
      <circle cx="0" cy="0" r="0.5" fill="#FFC530"/>
    </g>
    
    <g transform="translate(4.3 4.1) scale(0.18)">
      <path d="M0,0 C0.5,-0.5 1.5,-0.5 1.5,0 S2.5,0.5 2,1 S2,2 1.5,1.5 S0.5,2 0,1.5 S-0.5,0.5 0,0z" 
            fill="url(#sakuraBlossomGradient)" 
            stroke="#FF90B3" 
            stroke-width="0.5"
            opacity="0.85">
        <animateTransform attributeName="transform" type="rotate" from="0 0 0" to="-360 0 0" dur="20s" repeatCount="indefinite"/>
      </path>
      <circle cx="0" cy="0" r="0.5" fill="#FFC530"/>
    </g>
    
    <!-- Right branch flowers with varied designs -->
    <g transform="translate(12.5 4.7) scale(0.23)">
      <path d="M0,0 C0.5,-0.5 1.5,-0.5 1.5,0 S2.5,0.5 2,1 S2,2 1.5,1.5 S0.5,2 0,1.5 S-0.5,0.5 0,0z" 
            fill="url(#sakuraBlossomGradient)" 
            stroke="#FF90B3" 
            stroke-width="0.5">
        <animateTransform attributeName="transform" type="rotate" from="0 0 0" to="-360 0 0" dur="17s" repeatCount="indefinite"/>
      </path>
      <circle cx="0" cy="0" r="0.5" fill="#FFC530"/>
      <!-- Additional details -->
      <path d="M-0.75,-0.75 L0.75,0.75 M0.75,-0.75 L-0.75,0.75" stroke="#FF90B3" stroke-width="0.15" stroke-opacity="0.5" stroke-linecap="round"/>
    </g>
    
    <g transform="translate(11.2 3.2) scale(0.25)">
      <path d="M0,0 C0.5,-0.5 1.5,-0.5 1.5,0 S2.5,0.5 2,1 S2,2 1.5,1.5 S0.5,2 0,1.5 S-0.5,0.5 0,0z" 
            fill="url(#sakuraBlossomGradient)" 
            stroke="#FF90B3" 
            stroke-width="0.5">
        <animateTransform attributeName="transform" type="rotate" from="0 0 0" to="-360 0 0" dur="19s" repeatCount="indefinite"/>
      </path>
      <circle cx="0" cy="0" r="0.5" fill="#FFC530"/>
    </g>
    
    <g transform="translate(11.7 4.1) scale(0.18)">
      <path d="M0,0 C0.5,-0.5 1.5,-0.5 1.5,0 S2.5,0.5 2,1 S2,2 1.5,1.5 S0.5,2 0,1.5 S-0.5,0.5 0,0z" 
            fill="url(#sakuraBlossomGradient)" 
            stroke="#FF90B3" 
            stroke-width="0.5"
            opacity="0.85">
        <animateTransform attributeName="transform" type="rotate" from="0 0 0" to="360 0 0" dur="21s" repeatCount="indefinite"/>
      </path>
      <circle cx="0" cy="0" r="0.5" fill="#FFC530"/>
    </g>
  </g>

  <!-- ENHANCED FALLING CHERRY BLOSSOM PETALS WITH ADVANCED PHYSICS -->
  <g filter="url(#petalGlowEffect)">
    <!-- Primary petals with enhanced path physics -->
    <path d="M5 3.8c0.2 0 0.3 0.1 0.3 0.2 0 0.1-0.1 0.2-0.3 0.2s-0.3-0.1-0.3-0.2C4.7 3.9 4.8 3.8 5 3.8z" 
          fill="url(#petalGradient)" filter="url(#softPetalEffect)">
      <animateMotion path="M0,0 C0.3,1 0.1,2 0.4,3 C0.2,4 0.5,5 0.3,6 C0.6,7 0.4,8 0.7,9"
                    dur="10s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1; 0.42 0 0.58 1; 0.42 0 0.58 1"/>
      <animateTransform attributeName="transform" type="rotate"
                      values="0 5 3.8; 30 5 3.8; 60 5 3.8; 90 5 3.8; 120 5 3.8; 150 5 3.8; 180 5 3.8; 210 5 3.8; 240 5 3.8; 270 5 3.8; 300 5 3.8; 330 5 3.8; 360 5 3.8"
                      dur="5s" repeatCount="indefinite" additive="sum"/>
      <animate attributeName="opacity" values="0.95;0.8;0.95;0.8;0.95;0.7;0.5;0.3;0"
              dur="10s" repeatCount="indefinite"/>
    </path>
    
    <path d="M11 4c0.2 0 0.3 0.1 0.3 0.2 0 0.1-0.1 0.2-0.3 0.2s-0.3-0.1-0.3-0.2C10.7 4.1 10.8 4 11 4z" 
          fill="url(#petalGradient)" filter="url(#softPetalEffect)">
      <animateMotion path="M0,0 C-0.3,1 -0.1,2 -0.4,3 C-0.2,4 -0.5,5 -0.3,6 C-0.6,7 -0.4,8 -0.7,9"
                    dur="9s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1; 0.42 0 0.58 1; 0.42 0 0.58 1"/>
      <animateTransform attributeName="transform" type="rotate"
                      values="0 11 4; -30 11 4; -60 11 4; -90 11 4; -120 11 4; -150 11 4; -180 11 4; -210 11 4; -240 11 4; -270 11 4; -300 11 4; -330 11 4; -360 11 4"
                      dur="4.5s" repeatCount="indefinite" additive="sum"/>
      <animate attributeName="opacity" values="0.95;0.8;0.95;0.8;0.95;0.7;0.5;0.3;0"
              dur="9s" repeatCount="indefinite"/>
    </path>
    
    <!-- Secondary petals with enhanced harmonic motion -->
    <path d="M7 2.5c0.18 0 0.28 0.1 0.28 0.15 0 0.05-0.1 0.15-0.28 0.15s-0.28-0.1-0.28-0.15C6.72 2.6 6.82 2.5 7 2.5z" 
          fill="url(#petalGradient)" filter="url(#softPetalEffect)">
      <animateMotion path="M0,0 C0.2,0.8 0.5,1.6 0.3,2.4 C0.6,3.2 0.4,4 0.7,4.8 C0.5,5.6 0.8,6.4 0.6,7.2 C0.9,8 0.7,8.8 1.0,9.6"
                    dur="12s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1; 0.42 0 0.58 1; 0.42 0 0.58 1"/>
      <animateTransform attributeName="transform" type="rotate"
                      values="0 7 2.5; 45 7 2.5; 90 7 2.5; 135 7 2.5; 180 7 2.5; 225 7 2.5; 270 7 2.5; 315 7 2.5; 360 7 2.5"
                      dur="6s" repeatCount="indefinite" additive="sum"/>
      <animate attributeName="opacity" values="0.9;0.95;0.9;0.85;0.9;0.8;0.7;0.6;0.5;0.3;0.2;0"
              dur="12s" repeatCount="indefinite"/>
    </path>
    
    <path d="M9 2.7c0.18 0 0.28 0.1 0.28 0.15 0 0.05-0.1 0.15-0.28 0.15s-0.28-0.1-0.28-0.15C8.72 2.8 8.82 2.7 9 2.7z" 
          fill="url(#petalGradient)" filter="url(#softPetalEffect)">
      <animateMotion path="M0,0 C-0.2,0.9 -0.5,1.8 -0.3,2.7 C-0.6,3.6 -0.4,4.5 -0.7,5.4 C-0.5,6.3 -0.8,7.2 -0.6,8.1 C-0.9,9 -0.7,9.9 -1.0,10.8"
                    dur="11s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1; 0.42 0 0.58 1; 0.42 0 0.58 1"/>
      <animateTransform attributeName="transform" type="rotate"
                      values="0 9 2.7; -45 9 2.7; -90 9 2.7; -135 9 2.7; -180 9 2.7; -225 9 2.7; -270 9 2.7; -315 9 2.7; -360 9 2.7"
                      dur="5.5s" repeatCount="indefinite" additive="sum"/>
      <animate attributeName="opacity" values="0.9;0.95;0.9;0.85;0.9;0.8;0.7;0.5;0.3;0"
              dur="11s" repeatCount="indefinite"/>
    </path>
    
    <!-- Maple-inspired leaf with enhanced realism -->
        <!-- Maple-inspired leaf with enhanced realism -->
    <g filter="url(#mapleLeafEffect)" opacity="0.95">
      <path d="M9.9 5.3c0.2-0.3 0.4-0.4 0.5-0.2 0.1 0.2-0.1 0.3-0.2 0.4 0.3-0.2 0.5-0.1 0.4 0.1-0.1 0.2-0.3 0.2-0.5 0.2 0.3 0 0.4 0.2 0.3 0.4-0.1 0.2-0.3 0.1-0.5 0 0.2 0.2 0.2 0.4 0 0.5s-0.3-0.1-0.4-0.3c0.1 0.3 0 0.5-0.2 0.5-0.2 0-0.3-0.2-0.3-0.4 0 0.3-0.2 0.4-0.3 0.3-0.1-0.1-0.1-0.3 0-0.5-0.2 0.2-0.4 0.2-0.5 0.1-0.1-0.1 0-0.3 0.2-0.4-0.3 0.1-0.5 0-0.4-0.2 0.1-0.2 0.3-0.2 0.5-0.1-0.3-0.1-0.4-0.3-0.2-0.4 0.2-0.1 0.3 0 0.5 0.2-0.1-0.2-0.1-0.4 0.1-0.5 0.2-0.1 0.3 0.1 0.4 0.3-0.1-0.3 0.1-0.5 0.3-0.4 0.2 0.1 0.2 0.3 0.1 0.5 0.1-0.2 0.3-0.3 0.5-0.2 0.2 0.1 0.1 0.3-0.1 0.4z" 
            fill="url(#mapleLeafGradient)" 
            stroke="#BB2200" 
            stroke-width="0.04" 
            stroke-linejoin="round">
        <animate attributeName="d" 
                values="M9.9 5.3c0.2-0.3 0.4-0.4 0.5-0.2 0.1 0.2-0.1 0.3-0.2 0.4 0.3-0.2 0.5-0.1 0.4 0.1-0.1 0.2-0.3 0.2-0.5 0.2 0.3 0 0.4 0.2 0.3 0.4-0.1 0.2-0.3 0.1-0.5 0 0.2 0.2 0.2 0.4 0 0.5s-0.3-0.1-0.4-0.3c0.1 0.3 0 0.5-0.2 0.5-0.2 0-0.3-0.2-0.3-0.4 0 0.3-0.2 0.4-0.3 0.3-0.1-0.1-0.1-0.3 0-0.5-0.2 0.2-0.4 0.2-0.5 0.1-0.1-0.1 0-0.3 0.2-0.4-0.3 0.1-0.5 0-0.4-0.2 0.1-0.2 0.3-0.2 0.5-0.1-0.3-0.1-0.4-0.3-0.2-0.4 0.2-0.1 0.3 0 0.5 0.2-0.1-0.2-0.1-0.4 0.1-0.5 0.2-0.1 0.3 0.1 0.4 0.3-0.1-0.3 0.1-0.5 0.3-0.4 0.2 0.1 0.2 0.3 0.1 0.5 0.1-0.2 0.3-0.3 0.5-0.2 0.2 0.1 0.1 0.3-0.1 0.4z;
                        M9.95 5.25c0.22-0.28 0.42-0.38 0.52-0.18 0.1 0.2-0.12 0.32-0.22 0.42 0.3-0.2 0.48-0.08 0.38 0.12-0.1 0.2-0.32 0.22-0.52 0.22 0.3 0 0.4 0.22 0.28 0.42-0.12 0.2-0.32 0.1-0.5 0 0.2 0.2 0.2 0.4 0 0.5s-0.32-0.1-0.42-0.28c0.1 0.3 0 0.48-0.22 0.48-0.22 0-0.32-0.22-0.32-0.42 0 0.3-0.22 0.4-0.32 0.28-0.1-0.12-0.1-0.32 0-0.5-0.2 0.2-0.4 0.2-0.5 0.1-0.1-0.1 0-0.32 0.22-0.42-0.3 0.1-0.48 0-0.38-0.22 0.1-0.22 0.32-0.22 0.52-0.1-0.3-0.1-0.4-0.32-0.18-0.42 0.22-0.1 0.32 0 0.52 0.22-0.1-0.2-0.1-0.4 0.12-0.5 0.22-0.1 0.32 0.1 0.42 0.32-0.1-0.3 0.1-0.48 0.32-0.38 0.22 0.1 0.22 0.32 0.1 0.52 0.1-0.2 0.32-0.3 0.52-0.2 0.2 0.1 0.08 0.32-0.12 0.42z;
                        M9.9 5.3c0.2-0.3 0.4-0.4 0.5-0.2 0.1 0.2-0.1 0.3-0.2 0.4 0.3-0.2 0.5-0.1 0.4 0.1-0.1 0.2-0.3 0.2-0.5 0.2 0.3 0 0.4 0.2 0.3 0.4-0.1 0.2-0.3 0.1-0.5 0 0.2 0.2 0.2 0.4 0 0.5s-0.3-0.1-0.4-0.3c0.1 0.3 0 0.5-0.2 0.5-0.2 0-0.3-0.2-0.3-0.4 0 0.3-0.2 0.4-0.3 0.3-0.1-0.1-0.1-0.3 0-0.5-0.2 0.2-0.4 0.2-0.5 0.1-0.1-0.1 0-0.3 0.2-0.4-0.3 0.1-0.5 0-0.4-0.2 0.1-0.2 0.3-0.2 0.5-0.1-0.3-0.1-0.4-0.3-0.2-0.4 0.2-0.1 0.3 0 0.5 0.2-0.1-0.2-0.1-0.4 0.1-0.5 0.2-0.1 0.3 0.1 0.4 0.3-0.1-0.3 0.1-0.5 0.3-0.4 0.2 0.1 0.2 0.3 0.1 0.5 0.1-0.2 0.3-0.3 0.5-0.2 0.2 0.1 0.1 0.3-0.1 0.4z" 
                dur="7s" 
                repeatCount="indefinite" 
                calcMode="spline" 
                keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
        <animateTransform attributeName="transform" 
                        type="rotate" 
                        from="0 9.9 5.3" 
                        to="5 9.9 5.3" 
                        dur="4s" 
                        repeatCount="indefinite" 
                        additive="sum"
                        calcMode="spline" 
                        keySplines="0.4 0 0.6 1"/>
        <animate attributeName="opacity" 
                values="1;0.9;1" 
                dur="6s" 
                repeatCount="indefinite"/>
      </path>
      
      <!-- Detailed leaf veins for enhanced realism -->
      <path d="M9.9 5.3L9.9 4.9M9.9 5.3L10.3 5.1M9.9 5.3L10.2 5.5M9.9 5.3L9.6 5.1M9.9 5.3L9.6 5.5M9.9 5.3L9.5 5.3M9.9 5.3L10.3 5.3M9.9 5.3L9.9 5.7" 
            stroke="#990000" 
            stroke-width="0.03" 
            stroke-linecap="round"
            stroke-opacity="0.7">
        <animate attributeName="stroke-opacity" 
                values="0.7;0.5;0.7" 
                dur="5s" 
                repeatCount="indefinite"/>
        <animateMotion path="M0,0 C0.1,0.2 0.05,0.4 0.15,0.6 C0.1,0.8 0.15,1 0.1,1.2" 
                      dur="6s" 
                      repeatCount="indefinite" 
                      calcMode="spline" 
                      keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
      </path>
    </g>
  </g>
  
  <!-- CALLIGRAPHY BRUSHWORK WITH AUTHENTIC INK TECHNIQUES -->
  <g filter="url(#brushworkEffect)" opacity="0.8">
    <!-- Elegant "和" (harmony) character with authentic brushstroke techniques -->
    <path d="M12 3.4c0.15-0.1 0.3-0.2 0.35-0.35 0.05-0.15-0.05-0.25-0.25-0.2-0.2 0.05-0.4 0.3-0.4 0.45s0.15 0.2 0.3 0.1z" 
          fill="url(#inkGradient)" opacity="0.9">
      <animate attributeName="fill-opacity" values="0.9;0.85;0.9" dur="8s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- CLOUD PATTERNS WITH TRADITIONAL UIKKYO STYLE -->
  <g opacity="0.4" filter="url(#cloudGlowEffect)">
    <path d="M2.5 2.8c0.3-0.2 0.7-0.1 0.9 0.1s0.1 0.4-0.2 0.5-0.7-0.1-0.8-0.3 0-0.3 0.1-0.3z" fill="white">
      <animate attributeName="opacity" values="0.4;0.5;0.4" dur="10s" repeatCount="indefinite"/>
      <animateMotion path="M0,0 C0.1,0.05 0.2,0 0.3,0.05 C0.4,0 0.5,0.05 0.6,0" dur="30s" repeatCount="indefinite"/>
    </path>
    <path d="M13.5 2.5c-0.3-0.2-0.7-0.1-0.9 0.1s-0.1 0.4 0.2 0.5 0.7-0.1 0.8-0.3-0.05-0.3-0.1-0.3z" fill="white">
      <animate attributeName="opacity" values="0.4;0.55;0.4" dur="12s" repeatCount="indefinite"/>
      <animateMotion path="M0,0 C-0.1,0.05 -0.2,0 -0.3,0.05 C-0.4,0 -0.5,0.05 -0.6,0" dur="35s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- ENHANCED MOON WITH REALISTIC CRATER DETAILS -->
  <g filter="url(#moonGlowEffect)" opacity="0.9">
    <circle cx="13.5" cy="2.5" r="1" fill="url(#moonGradient)" filter="url(#moonTextureEffect)">
      <animate attributeName="opacity" values="0.9;0.95;0.9" dur="10s" repeatCount="indefinite"/>
    </circle>
    <!-- Subtle moon craters -->
    <path d="M13.3 2.3c0.1-0.1 0.3-0.1 0.4 0 0.1 0.1 0.1 0.3 0 0.4-0.1 0.1-0.3 0.1-0.4 0-0.1-0.1-0.1-0.3 0-0.4z" fill="#E0E0E0" opacity="0.5"/>
    <path d="M13.7 2.6c0.08-0.08 0.2-0.08 0.25 0 0.08 0.08 0.08 0.2 0 0.25-0.08 0.08-0.2 0.08-0.25 0-0.08-0.08-0.08-0.2 0-0.25z" fill="#E0E0E0" opacity="0.4"/>
  </g>
  
  <!-- KARESANSUI ZEN GARDEN PATTERNS WITH PROPER RAKING LINES -->
  <path d="M5.3 10.7c0.9 0 1.8 0 2.7 0M5.3 10.85c0.9 0 1.8 0 2.7 0M5.3 11c0.9 0 1.8 0 2.7 0M8 10.7c0.9 0 1.8 0 2.7 0M8 10.85c0.9 0 1.8 0 2.7 0M8 11c0.9 0 1.8 0 2.7 0" 
        stroke="url(#sandPatternGradient)" 
        stroke-width="0.03" 
        stroke-linecap="round"
        opacity="0.5"/>

  <!-- ENHANCED TRADITIONAL BORDER WITH INTERLOCKING PATTERNS -->
  <path d="M2 4c0.2-0.2 0.4-0.2 0.6 0 0.2 0.2 0.4 0.2 0.6 0 0.2-0.2 0.4-0.2 0.6 0 0.2 0.2 0.4 0.2 0.6 0 0.2-0.2 0.4-0.2 0.6 0 0.2 0.2 0.4 0.2 0.6 0 0.2-0.2 0.4-0.2 0.6 0 0.2 0.2 0.4 0.2 0.6 0 0.2-0.2 0.4-0.2 0.6 0 0.2 0.2 0.4 0.2 0.6 0 0.2-0.2 0.4-0.2 0.6 0" 
        stroke="url(#borderPatternGradient)" 
        stroke-width="0.12" 
        fill="none" 
        stroke-linecap="round"
        opacity="0.5"
        transform="translate(0 12.2)"/>
  
  <!-- ENHANCED FILTER EFFECTS -->
  <defs>

      <!-- ENHANCED FILTER EFFECTS -->
  <defs>
    <!-- Light & Shadow Effects -->
    <filter id="subtle-shadow" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse">
      <feDropShadow dx="0" dy="0.2" stdDeviation="0.1" flood-opacity="0.3" flood-color="#000000"/>
    </filter>
    
    <filter id="house-shadow" x="-10%" y="0%" width="120%" height="130%" filterUnits="userSpaceOnUse">
      <feDropShadow dx="0" dy="0.3" stdDeviation="0.15" flood-opacity="0.4" flood-color="#000000"/>
    </filter>
    
    <filter id="leaf-glow" x="-30%" y="-30%" width="160%" height="160%" filterUnits="userSpaceOnUse">
      <feGaussianBlur stdDeviation="0.05" result="blur"/>
      <feComposite in="SourceGraphic" in2="blur" operator="over"/>
    </filter>
    
    <filter id="lantern-glow" x="-50%" y="-50%" width="200%" height="200%" filterUnits="userSpaceOnUse">
      <feGaussianBlur stdDeviation="0.08" result="blur"/>
      <feComposite in="SourceGraphic" in2="blur" operator="over"/>
      <feColorMatrix type="matrix" values="1 0 0 0 0.2 0 1 0 0 0.1 0 0 1 0 0 0 0 0 1 0"/>
    </filter>
    
    <!-- Water Effects -->
    <filter id="water-reflection" x="-5%" y="-5%" width="110%" height="110%" filterUnits="userSpaceOnUse">
      <feGaussianBlur in="SourceAlpha" stdDeviation="0.1" result="blur"/>
      <feOffset in="blur" dx="0" dy="0.15" result="offsetBlur"/>
      <feComponentTransfer in="offsetBlur" result="brightBlur">
        <feFuncA type="linear" slope="0.4"/>
      </feComponentTransfer>
      <feComposite in="brightBlur" in2="SourceAlpha" operator="in" result="selectiveBlur"/>
      <feBlend in="SourceGraphic" in2="selectiveBlur" mode="normal"/>
    </filter>

    <filter id="gentle-ripple" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse">
      <feTurbulence type="fractalNoise" baseFrequency="0.08" numOctaves="2" seed="5" result="turbulence"/>
      <feDisplacementMap in="SourceGraphic" in2="turbulence" scale="1.5" xChannelSelector="R" yChannelSelector="G"/>
      <feGaussianBlur stdDeviation="0.3" result="blur"/>
      <feBlend in="SourceGraphic" in2="blur" mode="normal" result="blend"/>
      <feComposite in="blend" in2="SourceGraphic" operator="arithmetic" k1="0.5" k2="0.5" k3="0" k4="0"/>
    </filter>
    
    <!-- Sky and Moon Effects -->
    <filter id="moon-glow" x="-50%" y="-50%" width="200%" height="200%" filterUnits="userSpaceOnUse">
      <feGaussianBlur stdDeviation="0.2" result="blur"/>
      <feComposite in="SourceGraphic" in2="blur" operator="over"/>
    </filter>
    
    <filter id="soft-cloud" x="-20%" y="-20%" width="140%" height="140%" filterUnits="userSpaceOnUse">
      <feGaussianBlur stdDeviation="0.15"/>
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.8" intercept="0"/>
      </feComponentTransfer>
    </filter>
    
    <!-- Texture Effects -->
    <filter id="subtle-paper" x="0%" y="0%" width="100%" height="100%" filterUnits="userSpaceOnUse">
      <feTurbulence type="fractalNoise" baseFrequency="0.7" numOctaves="3" seed="5" stitchTiles="stitch" result="noise"/>
      <feColorMatrix in="noise" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0" result="lessNoise"/>
      <feBlend in="SourceGraphic" in2="lessNoise" mode="multiply"/>
    </filter>
    
    <filter id="maple-texture" x="0%" y="0%" width="100%" height="100%" filterUnits="userSpaceOnUse">
      <feGaussianBlur stdDeviation="0.03" result="blur"/>
      <feComponentTransfer in="blur">
        <feFuncA type="linear" slope="0.8" intercept="0"/>
      </feComponentTransfer>
      <feBlend in="SourceGraphic" in2="blur" mode="normal"/>
    </filter>
    
    <!-- Enhanced Details -->
    <filter id="crisp-details" x="0%" y="0%" width="100%" height="100%">
      <feComponentTransfer>
        <feFuncR type="linear" slope="1.05" intercept="0"/>
        <feFuncG type="linear" slope="1.05" intercept="0"/>
        <feFuncB type="linear" slope="1.05" intercept="0"/>
      </feComponentTransfer>
    </filter>
    
    <filter id="brushwork" x="-5%" y="-5%" width="110%" height="110%">
      <feGaussianBlur stdDeviation="0.05" result="blur"/>
      <feComposite in="SourceGraphic" in2="blur" operator="arithmetic" k1="0" k2="1" k3="0" k4="0"/>
    </filter>
  </defs>
    <!-- ENHANCED LUXURY GRADIENTS -->
<linearGradient id="celestialGradient" x1="8" y1="3" x2="8" y2="13" gradientUnits="userSpaceOnUse">
  <stop offset="0" stop-color="#4A1942"/>  <!-- Deep purple-pink at top -->
  <stop offset="0.3" stop-color="#7B2869"/>  <!-- Rich magenta -->
  <stop offset="0.7" stop-color="#9D3C72"/>  <!-- Medium raspberry -->
  <stop offset="1" stop-color="#C85C8E"/>  <!-- Lighter rose pink -->
</linearGradient>
    
    <radialGradient id="waterGradient" cx="8" cy="11.2" r="4.2" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#60ABDD"/>
      <stop offset="0.4" stop-color="#4D8EBE"/>
      <stop offset="0.8" stop-color="#3A6F9F"/>
      <stop offset="1" stop-color="#2E5A85"/>
    </radialGradient>
    
    <radialGradient id="waterHighlightGradient" cx="8" cy="11.2" r="3.8" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#90C8F0"/>
      <stop offset="0.5" stop-color="#75B4E8" stop-opacity="0.7"/>
      <stop offset="1" stop-color="#60ABDD" stop-opacity="0"/>
    </radialGradient>
    
    <linearGradient id="whiteKoiGradient" x1="6.8" y1="11.2" x2="7.3" y2="11.3" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFFFF"/>
      <stop offset="0.5" stop-color="#F0F0F5"/>
      <stop offset="1" stop-color="#E0E0EA"/>
    </linearGradient>
    
    <linearGradient id="orangeKoiGradient" x1="8.9" y1="11.3" x2="8.4" y2="11.4" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FF9D45"/>
      <stop offset="0.5" stop-color="#FF8930"/>
      <stop offset="1" stop-color="#F07A20"/>
    </linearGradient>
    
    <linearGradient id="rockGradient1" x1="4.2" y1="10.9" x2="4.75" y2="11.25" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#606060"/>
      <stop offset="0.5" stop-color="#787878"/>
      <stop offset="1" stop-color="#909090"/>
    </linearGradient>
    
    <linearGradient id="rockGradient2" x1="11.3" y1="10.85" x2="11.8" y2="11.2" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#606060"/>
      <stop offset="0.5" stop-color="#787878"/>
      <stop offset="1" stop-color="#909090"/>
    </linearGradient>
    
    <linearGradient id="toriiTopGradient" x1="8" y1="9.6" x2="8" y2="10" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#CB2B31"/>
      <stop offset="1" stop-color="#A01E22"/>
    </linearGradient>
    
    <linearGradient id="toriiPillarGradient" x1="8" y1="9.8" x2="8" y2="11" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#CB2B31"/>
      <stop offset="0.5" stop-color="#B02429"/>
      <stop offset="1" stop-color="#95191C"/>
    </linearGradient>
    
    <linearGradient id="woodTextureGradient" x1="8" y1="7.3" x2="8" y2="9.6" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#885E3D"/>
      <stop offset="0.5" stop-color="#704D30"/>
      <stop offset="1" stop-color="#593A23"/>
    </linearGradient>
    
    <linearGradient id="roofMainGradient" x1="8" y1="5.6" x2="8" y2="7.6" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#593A23"/>
      <stop offset="0.5" stop-color="#4A2F1B"/>
      <stop offset="1" stop-color="#3D2302"/>
    </linearGradient>
    
    <linearGradient id="shojiGradient" x1="7.2" y1="7.7" x2="7.2" y2="9.6" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFF5E6"/>
      <stop offset="1" stop-color="#F0E8D8"/>
    </linearGradient>
    
    <linearGradient id="engawaGradient" x1="8" y1="9.6" x2="8" y2="9.9" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#7D5936"/>
      <stop offset="1" stop-color="#694A2C"/>
    </linearGradient>
    
    <linearGradient id="norenGradient" x1="8" y1="7.1" x2="8" y2="7.6" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#1C3765"/>
      <stop offset="1" stop-color="#12253D"/>
    </linearGradient>
    
    <linearGradient id="onigawaraGradient" x1="8" y1="4.4" x2="8" y2="4.8" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#593A23"/>
      <stop offset="1" stop-color="#3D2302"/>
    </linearGradient>
    
    <linearGradient id="stoneLanternGradient" x1="6" y1="9.6" x2="6" y2="10.2" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#B0B0B0"/>
      <stop offset="1" stop-color="#909090"/>
    </linearGradient>
    
    <radialGradient id="lanternLightGradient" cx="6" cy="9.55" r="0.12" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFEEAA"/>
      <stop offset="0.5" stop-color="#FFDD77"/>
      <stop offset="1" stop-color="#FFCC44"/>
    </radialGradient>
    
    <linearGradient id="bridgeGradient" x1="8" y1="10.3" x2="8" y2="10.6" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#7D5936"/>
      <stop offset="1" stop-color="#593A23"/>
    </linearGradient>
    
    <linearGradient id="bridgeDetailGradient" x1="8" y1="10.3" x2="8" y2="10.6" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#9B6F42"/>
      <stop offset="1" stop-color="#7D5936"/>
    </linearGradient>
    
    <radialGradient id="lotusPetalGradient" cx="0" cy="0" r="2" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFFFF"/>
      <stop offset="0.3" stop-color="#FFF0F5"/>
      <stop offset="0.6" stop-color="#FFE0EB"/>
      <stop offset="1" stop-color="#FFD0E0"/>
    </radialGradient>
    
    <radialGradient id="lotusInnerPetalGradient" cx="0" cy="0" r="1.5" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFF8FA"/>
      <stop offset="0.4" stop-color="#FFE8F0"/>
      <stop offset="1" stop-color="#FFD8E6"/>
    </radialGradient>
    
    <radialGradient id="lotusCenterGradient" cx="0" cy="0" r="0.5" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFDD55"/>
      <stop offset="0.5" stop-color="#FFCC33"/>
      <stop offset="1" stop-color="#FFC000"/>
    </radialGradient>
    
    <linearGradient id="lotusPetalEdgeGradient" x1="-2" y1="0" x2="2" y2="0" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFB0D0"/>
      <stop offset="0.5" stop-color="#FFA0C0"/>
      <stop offset="1" stop-color="#FFB0D0"/>
    </linearGradient>
    
    <linearGradient id="lotusInnerEdgeGradient" x1="-1" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFBCDA"/>
      <stop offset="0.5" stop-color="#FFACD0"/>
      <stop offset="1" stop-color="#FFBCDA"/>
    </linearGradient>
    
    <radialGradient id="sakuraBlossomGradient" cx="0" cy="0" r="2" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFE6F0"/>
      <stop offset="0.3" stop-color="#FFD6E8"/>
      <stop offset="0.6" stop-color="#FFC6E0"/>
      <stop offset="1" stop-color="#FFB6D8"/>
    </radialGradient>
    
    <radialGradient id="petalGradient" cx="0" cy="0" r="0.3" fx="0.05" fy="-0.05" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFECF5"/>
      <stop offset="0.6" stop-color="#FFD6E8"/>
      <stop offset="1" stop-color="#FFC0DD"/>
    </radialGradient>
    
    <linearGradient id="mapleLeafGradient" x1="9.9" y1="4.8" x2="9.9" y2="5.8" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FF5533"/>
      <stop offset="0.5" stop-color="#DD3311"/>
      <stop offset="1" stop-color="#CC2200"/>
    </linearGradient>
    
    <linearGradient id="inkGradient" x1="12" y1="3" x2="12" y2="3.5" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#000000"/>
      <stop offset="1" stop-color="#333333"/>
    </linearGradient>
    
    <radialGradient id="moonGradient" cx="13.5" cy="2.5" r="1" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFDE7"/>
      <stop offset="0.5" stop-color="#FFF9C4"/>
      <stop offset="1" stop-color="#FFF59D"/>
    </radialGradient>
    
    <linearGradient id="sandPatternGradient" x1="5.3" y1="10.85" x2="10.7" y2="10.85" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#CCCCCC" stop-opacity="0.4"/>
      <stop offset="0.5" stop-color="#FFFFFF" stop-opacity="0.5"/>
      <stop offset="1" stop-color="#CCCCCC" stop-opacity="0.4"/>
    </linearGradient>
    
    <linearGradient id="borderPatternGradient" x1="2" y1="4" x2="14" y2="4" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#1C3765" stop-opacity="0.7"/>
      <stop offset="0.5" stop-color="#17284D" stop-opacity="0.7"/>
      <stop offset="1" stop-color="#1C3765" stop-opacity="0.7"/>
    </linearGradient>
  </defs>
</svg>