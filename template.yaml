AWSTemplateFormatVersion: '2010-09-09'

Parameters:
  ConsoleAcmCertArn:
    Description: The ARN of the ACM Certificate
    Type: String

  CrawlerIndexerFunctionName:
    Description: The name of the Lambda function for indexing
    Type: String
    Default: arn:aws:lambda:eu-central-1:750681925397:function:thinkertags-api-crawlerIndexer-I7qJiUDRRP4F

Outputs:
  BucketName:
    Description: The name of the S3 bucket
    Value: !Ref S3Bucket
    Export:
      Name: !Sub "${AWS::StackName}-BucketName"

  DistributionID:
    Description: The ID of the CloudFront distribution
    Value: !Ref CloudFrontDistribution
    Export:
      Name: !Sub "${AWS::StackName}-DistributionID"


Resources:
  S3Bucket:
    Type: 'AWS::S3::Bucket'
    Properties:
      AccessControl: 'Private'  # Removed as per cfn-lint W3045 warning
      NotificationConfiguration:
        LambdaConfigurations:
          - Event: s3:ObjectCreated:*
            Function: !Ref CrawlerIndexerFunctionName
            Filter:
              S3Key:
                Rules:
                  - Name: prefix
                    Value: blog/
                  - Name: suffix
                    Value: .md
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256

  PrerenderPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref CrawlerIndexerFunctionName
      Action: lambda:InvokeFunction
      Principal: s3.amazonaws.com
      SourceArn: !GetAtt S3Bucket.Arn
      SourceAccount: !Ref AWS::AccountId

  S3BucketPolicy:
    Type: AWS::S3::BucketPolicy
    Properties:
      Bucket: !Ref S3Bucket
      PolicyDocument:
        Statement:
        - Action: s3:GetObject
          Effect: Allow
          Resource: !Sub ${S3Bucket.Arn}/*
          Principal:
            Service: cloudfront.amazonaws.com
          Condition:
            StringEquals:
              AWS:SourceArn: !Sub arn:aws:cloudfront::${AWS::AccountId}:distribution/${CloudFrontDistribution}

  CloudFrontOriginAccessControl:
    Type: AWS::CloudFront::OriginAccessControl
    Properties:
      OriginAccessControlConfig:
        Description: Default Origin Access Control
        Name: !Ref AWS::StackName
        OriginAccessControlOriginType: s3
        SigningBehavior: always
        SigningProtocol: sigv4

  SecurityHeadersPolicy:
    Type: 'AWS::CloudFront::ResponseHeadersPolicy'
    Properties:
      ResponseHeadersPolicyConfig:
        Name: SecurityHeadersPolicy
        SecurityHeadersConfig:
          StrictTransportSecurity:
            AccessControlMaxAgeSec: ********
            IncludeSubdomains: true
            Override: true
          ContentSecurityPolicy:
            ContentSecurityPolicy: "default-src 'self' blob: https://d236p4wpt8c97r.cloudfront.net; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://*.googleapis.com https://api.geoapify.com; style-src 'self' 'unsafe-inline' https://api.geoapify.com https://fonts.googleapis.com https://*.googleapis.com; img-src 'self' https://d236p4wpt8c97r.cloudfront.net https://api.geoapify.com https://*.googleapis.com https://*.gstatic.com https://*.amazonaws.com data: blob:; font-src 'self' data: https://api.geoapify.com https://fonts.gstatic.com; connect-src 'self' https://d236p4wpt8c97r.cloudfront.net https://api.geoapify.com https://*.amazonaws.com https://*.thinkertags.com https://nominatim.openstreetmap.org https://*.googleapis.com; worker-src 'self' blob:;"
            Override: true
          FrameOptions:
            FrameOption: SAMEORIGIN
            Override: true
          ContentTypeOptions:
            Override: true
          ReferrerPolicy:
            ReferrerPolicy: strict-origin-when-cross-origin
            Override: true
        CustomHeadersConfig:
          Items:
            - Header: Permissions-Policy
              Value: "accelerometer=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()"
              Override: true

  BotDetectionFunction:
    Type: AWS::CloudFront::Function
    Properties:
      Name: !Sub "${AWS::StackName}-BotDetection"
      AutoPublish: true
      FunctionConfig:
        Comment: "Detects bots and serves pre-rendered content for blog posts"
        Runtime: cloudfront-js-1.0
      FunctionCode: |
        function handler(event) {
            var request = event.request;
            var headers = request.headers;
            var uri = request.uri;

            // Get the user agent
            var userAgent = '';
            if (headers['user-agent']) {
                userAgent = headers['user-agent'].value.toLowerCase();
            }

            // Exclude our own ThinkertagsBot
            if (userAgent.includes('thinkertagsbot')) {
                return request;
            }

            // Initialize bot detection
            var isBot = false;

            // List of bots that DO execute JavaScript and should be excluded
            var jsExecutingBots = [
                'googlebot', 'bingbot', 'yandexbot', 'duckduckbot',
                'baiduspider', 'applebot', 'semrushbot'
            ];

            // Check if the user agent is a bot that executes JavaScript
            var isJsExecutingBot = false;
            for (var i = 0; i < jsExecutingBots.length && !isJsExecutingBot; i++) {
                if (userAgent.includes(jsExecutingBots[i])) {
                    isJsExecutingBot = true;
                }
            }

            // If not a JS-executing bot, check for non-JS bots
            if (!isBot && !isJsExecutingBot) {
                // List of bot keywords focused on those that don't execute JavaScript
                var botKeywords = [
                    // Social media and messaging platforms
                    'facebook', 'facebookexternalhit', 'whatsapp', 'twitter',
                    'linkedin', 'pinterest', 'slack', 'telegram', 'discord',
                    'skype', 'snapchat', 'viber', 'line', 'wechat',

                    // Link preview services
                    'preview', 'socialsharepreview', 'embedly', 'iframely',

                    // General bot identifiers (for safety)
                    'bot', 'crawler', 'spider'
                ];

                // Check if the user agent contains any bot keywords
                for (var i = 0; i < botKeywords.length && !isBot; i++) {
                    if (userAgent.includes(botKeywords[i])) {
                        isBot = true;
                    }
                }
            }

            // If it's a bot, handle the request appropriately
            if (isBot) {
                // Check if the URI is for an image file or other static file that should be served directly
                var isStaticFile = /\.(jpe?g|png|gif|webp|svg|bmp|ico|xml|txt|json|pdf)$/i.test(uri);

                // If it's a static file, don't redirect to bot cache
                if (isStaticFile) {
                    return request;
                }

                // For non-image files, proceed with bot cache redirection
                // Remove leading slash if present
                var path = uri;
                if (path.startsWith('/')) {
                    path = path.substring(1);
                }

                // Remove trailing slash if present
                if (path.endsWith('/')) {
                    path = path.substring(0, path.length - 1);
                }

                // Handle empty path (homepage)
                if (path === '') {
                    path = 'home';
                }

                // Create the bot cache path
                var newUri = '/bot-cache/' + path + '/index.html';
                request.uri = newUri;
            }

            return request;
        }

  CloudFrontDistribution:
    Type: 'AWS::CloudFront::Distribution'
    Properties:
      DistributionConfig:
        Aliases:
          - 'thinkertags.com'
          - 'thinkertag.com'
          - 'thinkertags.fr'
          - 'thinkertags.de'
          - 'thinkertags.es'
          - 'thinkertags.co.uk'
        ViewerCertificate:
          AcmCertificateArn: !Ref ConsoleAcmCertArn
          SslSupportMethod: 'sni-only'
          MinimumProtocolVersion: 'TLSv1.2_2019'
        Origins:
          - Id: S3Origin
            DomainName: !Sub '${S3Bucket}.s3.${AWS::Region}.amazonaws.com'
            S3OriginConfig:
              OriginAccessIdentity: ''
            OriginAccessControlId: !GetAtt CloudFrontOriginAccessControl.Id
        Enabled: true
        DefaultRootObject: 'index.html'
        CustomErrorResponses:
          - ErrorCode: 403
            ResponsePagePath: '/index.html'
            ResponseCode: 200
          - ErrorCode: 404
            ResponsePagePath: '/index.html'
            ResponseCode: 200
        CacheBehaviors:
          - PathPattern: '*.xml'
            TargetOriginId: 'S3Origin'
            ViewerProtocolPolicy: 'redirect-to-https'
            CachePolicyId: '658327ea-f89d-4fab-a63d-7e88639e58f6'
            OriginRequestPolicyId: 'b689b0a8-53d0-40ab-baf2-68738e2966ac'
            ResponseHeadersPolicyId: !Ref SecurityHeadersPolicy
          - PathPattern: '*.txt'
            TargetOriginId: 'S3Origin'
            ViewerProtocolPolicy: 'redirect-to-https'
            CachePolicyId: '658327ea-f89d-4fab-a63d-7e88639e58f6'
            OriginRequestPolicyId: 'b689b0a8-53d0-40ab-baf2-68738e2966ac'
            ResponseHeadersPolicyId: !Ref SecurityHeadersPolicy
        DefaultCacheBehavior:
          TargetOriginId: 'S3Origin'
          ViewerProtocolPolicy: 'redirect-to-https'
          CachePolicyId: '658327ea-f89d-4fab-a63d-7e88639e58f6'
          OriginRequestPolicyId: 'b689b0a8-53d0-40ab-baf2-68738e2966ac'
          ResponseHeadersPolicyId: !Ref SecurityHeadersPolicy
          FunctionAssociations:
            - EventType: viewer-request
              FunctionARN: !GetAtt BotDetectionFunction.FunctionARN
        HttpVersion: 'http2and3'

  DNSRecord:
    Type: 'AWS::Route53::RecordSet'
    Properties:
      HostedZoneId: 'Z031034829BBKB8OEMGP8'
      Name: 'thinkertags.com.'
      Type: 'A'
      AliasTarget:
        HostedZoneId: 'Z2FDTNDATAQYW2'  # fixed for CloudFront distributions
        DNSName: !GetAtt [CloudFrontDistribution, DomainName]
        EvaluateTargetHealth: false
  DNSRecord2:
    Type: 'AWS::Route53::RecordSet'
    Properties:
      HostedZoneId: 'Z0778787SHNSH6ECR8NM'
      Name: 'thinkertag.com.'
      Type: 'A'
      AliasTarget:
        HostedZoneId: 'Z2FDTNDATAQYW2'  # fixed for CloudFront distributions
        DNSName: !GetAtt [CloudFrontDistribution, DomainName]
        EvaluateTargetHealth: false
  FRDNSRecord:
    Type: 'AWS::Route53::RecordSet'
    Properties:
      HostedZoneId: 'Z081683134LRVJQUU53Y8'
      Name: 'thinkertags.fr.'
      Type: 'A'
      AliasTarget:
        HostedZoneId: 'Z2FDTNDATAQYW2'  # fixed for CloudFront distributions
        DNSName: !GetAtt [CloudFrontDistribution, DomainName]
        EvaluateTargetHealth: false
  DEDNSRecord:
    Type: 'AWS::Route53::RecordSet'
    Properties:
      HostedZoneId: 'Z08035121B5PGX1XE3NFU'
      Name: 'thinkertags.de.'
      Type: 'A'
      AliasTarget:
        HostedZoneId: 'Z2FDTNDATAQYW2'  # fixed for CloudFront distributions
        DNSName: !GetAtt [CloudFrontDistribution, DomainName]
        EvaluateTargetHealth: false
  ESDNSRecord:
    Type: 'AWS::Route53::RecordSet'
    Properties:
      HostedZoneId: 'Z0206211213U3VMGAQP9A'
      Name: 'thinkertags.es.'
      Type: 'A'
      AliasTarget:
        HostedZoneId: 'Z2FDTNDATAQYW2'  # fixed for CloudFront distributions
        DNSName: !GetAtt [CloudFrontDistribution, DomainName]
        EvaluateTargetHealth: false
  UKDNSRecord:
    Type: 'AWS::Route53::RecordSet'
    Properties:
      HostedZoneId: 'Z0500581322DOO13FAKZK'
      Name: 'thinkertags.co.uk.'
      Type: 'A'
      AliasTarget:
        HostedZoneId: 'Z2FDTNDATAQYW2'  # fixed for CloudFront distributions
        DNSName: !GetAtt [CloudFrontDistribution, DomainName]
        EvaluateTargetHealth: false