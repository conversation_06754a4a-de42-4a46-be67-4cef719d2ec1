name: Deploy to Production

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'

    - name: Install dependencies
      run: npm ci

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: eu-central-1

    - name: Deploy SSL CloudFormation Stack
      run: |
        aws cloudformation deploy --template-file template-ssl.yaml --stack-name thinkertags-ssl2 --capabilities CAPABILITY_NAMED_IAM --region us-east-1

    - name: Get SSL Certificate ARN
      id: get-ssl
      run: |
        SSL_ARN=$(aws cloudformation describe-stacks --stack-name thinkertags-ssl2 --region us-east-1 --query "Stacks[0].Outputs[?OutputKey=='ConsoleAcmCertArn'].OutputValue" --output text)
        echo "SSL_ARN=${SSL_ARN}" >> $GITHUB_ENV

    - name: Deploy CloudFormation Stack
      run: |
        aws cloudformation deploy --template-file template.yaml --stack-name thinkertags --capabilities CAPABILITY_NAMED_IAM --parameter-overrides ConsoleAcmCertArn=$SSL_ARN

    - name: Build project
      env:
        VITE_API_KEY_GEOAMPIFY: ${{ secrets.VITE_API_KEY_GEOAMPIFY }}
        VITE_COOKIE_DOMAIN: ${{ secrets.VITE_COOKIE_DOMAIN }}
        VITE_API_ENDPOINT: ${{ secrets.VITE_API_ENDPOINT }}
        VITE_PUBLIC_API_ENDPOINT: ${{ vars.VITE_PUBLIC_API_ENDPOINT }}
        VITE_GOOGLE_MAPS_API_KEY: ${{ secrets.VITE_GOOGLE_MAPS_API_KEY }}
        VITE_AWS_MAPS_REGION: ${{ secrets.VITE_AWS_MAPS_REGION }}
        VITE_AWS_MAPS_API_KEY: ${{ secrets.VITE_AWS_MAPS_API_KEY }}
        VITE_AWS_USER_UPLOAD_CLOUDFRONT: ${{ vars.VITE_AWS_USER_UPLOAD_CLOUDFRONT }}


      run: npm run deploy

    - name: Get S3 Bucket Name
      id: get-bucket
      run: |
        BUCKET_NAME=$(aws cloudformation describe-stacks --stack-name thinkertags --query "Stacks[0].Outputs[?OutputKey=='BucketName'].OutputValue" --output text)
        echo "S3_BUCKET=s3://${BUCKET_NAME}" >> $GITHUB_ENV

    - name: Deploy to S3
      run: |
        LOCAL_DIST_FOLDER="dist"
        aws s3 sync $LOCAL_DIST_FOLDER ${{ env.S3_BUCKET }} --delete

    - name: Get CloudFront Distribution ID
      id: get-distribution-id
      run: |
        DISTRIBUTION_ID=$(aws cloudformation describe-stacks --stack-name thinkertags --query "Stacks[0].Outputs[?OutputKey=='DistributionID'].OutputValue" --output text)
        echo "DISTRIBUTION_ID=${DISTRIBUTION_ID}" >> $GITHUB_ENV

    - name: Invalidate CloudFront cache
      run: |
        aws cloudfront create-invalidation --distribution-id ${{ env.DISTRIBUTION_ID }} --paths "/*"