name: Invalidate CloudFront Cache

on:
  workflow_dispatch:

jobs:
  invalidate:
    runs-on: ubuntu-latest
    steps:
    - name: Set up AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ secrets.AWS_REGION }}

    - name: Get CloudFront Distribution ID
      id: get-distribution-id
      run: |
        DISTRIBUTION_ID=$(aws cloudformation describe-stacks --stack-name gustafpartners-online --query "Stacks[0].Outputs[?OutputKey=='DistributionID'].OutputValue" --output text)
        echo "DISTRIBUTION_ID=${DISTRIBUTION_ID}" >> $GITHUB_ENV

    - name: Invalidate CloudFront cache
      run: |
        aws cloudfront create-invalidation --distribution-id ${{ env.DISTRIBUTION_ID }} --paths "/*"

