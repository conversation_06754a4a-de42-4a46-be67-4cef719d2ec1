import React, { memo, useMemo } from 'react';
import Skeleton from 'react-loading-skeleton';
import { ToggleSwitch } from '../components/ToggleSwitch';
import '../styles/UserList.css';

const UserAvatar = memo(({ picture, displayName }) => {
  const handleImageError = (e) => {
    console.error('UserList image failed to load:', e.target.src);
    e.target.onerror = null;
    e.target.src = '/user_placeholder.svg';
  };
  
  return (
    <div className="user-avatar-container">
      {picture ? (
        <img
          src={picture}
          alt={displayName}
          className="user-list-avatar"
          loading="lazy"
          width={70}
          height={70}
          onError={handleImageError}
        />
      ) : (
        <img
          src="/user_placeholder.svg"
          alt="Placeholder"
          className="user-list-avatar"
          width={70}
          height={70}
        />
      )}
    </div>
  );
});

const UserListItem = memo(({ user, getUserDisplayName, onUserClick }) => {
  const userRole =
    user.userStatus !== 'CONFIRMED' && user.userStatus !== 'CONFIRMED_AUTO'
      ? 'Pending'
      : (user.groups && user.groups.length ? user.groups[0] : 'Unassigned');

  const displayName = getUserDisplayName(user);

  return (
    <div
      className="user-list-item"
      onClick={() => onUserClick(user.username)}
      role="button"
      tabIndex={0}
      aria-label={`View user ${displayName}`}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onUserClick(user.username);
        }
      }}
    >
      <UserAvatar picture={user.picture} displayName={displayName} />
      <div className="user-info">
        <div className="user-name">{displayName}</div>
        <p className="user-email">{user.email || user.username}</p>
      </div>
      <div className="user-role">{userRole}</div>
    </div>
  );
});

const EmptyState = memo(() => (
  <div className="user-list-item">
    <div className="user-avatar-container">
      <img
        src="/user_placeholder.svg"
        alt="No users found"
        className="user-list-avatar"
        width={70}
        height={70}
      />
    </div>
    <div className="user-info">
      <p className="user-email">No users found</p>
    </div>
  </div>
));

export const UserList = memo(function UserList({
  filter,
  onFilterChange,
  filteredUsers,
  loadingUsers,
  isAuthLoading,
  getUserDisplayName,
  onUserClick
}) {
  const filterToggle = useMemo(() => (
    <ToggleSwitch
      options={['All', 'Operators', 'Pending']}
      activeOption={filter}
      onChange={onFilterChange}
      specialRounded={['All', 'Operators', 'Pending']}
    />
  ), [filter, onFilterChange]);

  const listContent = useMemo(() => {
    if (loadingUsers || isAuthLoading) {
      return Array(5).fill(0).map((_, i) => (
        <div key={i} className="user-list-item">
          <div className="user-avatar-container">
            <Skeleton circle width={70} height={70} />
          </div>
          <div className="user-info">
            <Skeleton width={120} height={16} />
            <Skeleton width={180} height={14} style={{ marginTop: 6 }} />
          </div>
          <div className="user-role">
            <Skeleton width={60} height={16} />
          </div>
        </div>
      ));
    }

    if (filteredUsers.length === 0) {
      return <EmptyState />;
    }

    return filteredUsers.map((user) => (
      <UserListItem
        key={user.username}
        user={user}
        getUserDisplayName={getUserDisplayName}
        onUserClick={onUserClick}
      />
    ));
  }, [filteredUsers, loadingUsers, isAuthLoading, getUserDisplayName, onUserClick]);

  return (
    <>
      {filterToggle}

      <div 
        className="user-list user-list-top-margin"
        role="list"
        aria-busy={loadingUsers || isAuthLoading}
        aria-label="User list"
      >
        {listContent}
      </div>
    </>
  );
});