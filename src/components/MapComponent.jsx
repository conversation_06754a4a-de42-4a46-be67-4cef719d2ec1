import React, { useMemo, useRef, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Map } from 'react-map-gl/maplibre';
import maplibregl from 'maplibre-gl';
import 'maplibre-gl/dist/maplibre-gl.css';
import '../styles/MapComponent.css';

const MAP_CONTAINER_STYLE = {
  width: '100%',
  height: '400px'
};

const MAP_STYLES = {
  standardLight: { style: 'Standard', colorScheme: 'Light' },
  standardDark: { style: 'Standard', colorScheme: 'Dark' },
  monochromeLight: { style: 'Monochrome', colorScheme: 'Light' },
  monochromeDark: { style: 'Monochrome', colorScheme: 'Dark' },
  hybrid: { style: 'Hybrid' },
  satellite: { style: 'Satellite' }
};

export { MapComponent };

function MapComponent({ lat, long, mapStyle = 'monochromeDark' }) {
  const mapRef = useRef(null);
  const markerRef = useRef(null);
  const [mapLoaded, setMapLoaded] = useState(false);

  const coordinates = useMemo(() => {
    if (lat === null || long === null || lat === undefined || long === undefined) {
      return null;
    }

    const parsedLat = Number(lat);
    const parsedLong = Number(long);

    if (
      isNaN(parsedLat) ||
      isNaN(parsedLong) ||
      parsedLat < -90 ||
      parsedLat > 90 ||
      parsedLong < -180 ||
      parsedLong > 180
    ) {
      return null;
    }

    return {
      latitude: parsedLat,
      longitude: parsedLong,
      zoom: 14
    };
  }, [lat, long]);

  const apiKey = import.meta.env.VITE_AWS_MAPS_API_KEY;
  const region = import.meta.env.VITE_AWS_MAPS_REGION;

  const getMapStyle = (styleKey) => {
    const styleConfig = MAP_STYLES[styleKey];
    const baseUrl = `https://maps.geo.${region}.amazonaws.com/v2/styles/${styleConfig.style}/descriptor?key=${apiKey}`;
    return styleConfig.colorScheme ? `${baseUrl}&color-scheme=${styleConfig.colorScheme}` : baseUrl;
  };

  useEffect(() => {
    if (!coordinates || !mapLoaded || !mapRef.current?.getMap()) return;

    // Remove existing marker if it exists
    if (markerRef.current) {
      markerRef.current.remove();
    }

    const markerElement = document.createElement('div');
    markerElement.className = 'custom-marker';
    
    // Create pulse rings
    const pulseRing1 = document.createElement('div');
    pulseRing1.className = 'pulse-ring';
    const pulseRing2 = document.createElement('div');
    pulseRing2.className = 'pulse-ring';
    
    // Create pin image
    const img = document.createElement('img');
    img.src = '/pin.svg';
    img.className = 'custom-marker-pin';
    img.alt = 'Location pin';
    
    // Append elements
    markerElement.appendChild(pulseRing1);
    markerElement.appendChild(pulseRing2);
    markerElement.appendChild(img);

    markerRef.current = new maplibregl.Marker({
      element: markerElement,
      anchor: 'bottom',
      offset: [0, 0]
    })
      .setLngLat([coordinates.longitude, coordinates.latitude])
      .addTo(mapRef.current.getMap());

    return () => {
      if (markerRef.current) {
        markerRef.current.remove();
      }
    };
  }, [coordinates, mapLoaded]);

  if (!coordinates) {
    return (
      <div className="map-error" style={MAP_CONTAINER_STYLE}>
        Invalid coordinates provided
      </div>
    );
  }

  return (
    <div className="map-wrapper">
      <Map
        ref={mapRef}
        initialViewState={coordinates}
        mapStyle={getMapStyle(mapStyle)}
        style={MAP_CONTAINER_STYLE}
        cooperativeGestures={true}
        onLoad={() => setMapLoaded(true)}
      />
    </div>
  );
}

MapComponent.propTypes = {
  lat: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
  long: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
  mapStyle: PropTypes.oneOf(Object.keys(MAP_STYLES))
};