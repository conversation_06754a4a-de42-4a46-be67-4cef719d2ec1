import React, { useEffect, useRef, useState } from 'react';
import '../../styles/AudioPlayer.css';
import { AudioVisualizer } from 'react-audio-visualize';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';

const AudioPlayer = ({
  audioSrc,
  title = "Audio Article",
  author = "Thinkertags",
  artworkSrc = "/logo192.png",
  skeleton_debug = false // Debug mode to force skeleton display
}) => {
  const audioRef = useRef(null);
  const visualizerRef = useRef(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [fileExists, setFileExists] = useState(true);
  const [error, setError] = useState(null);
  const [audioBlob, setAudioBlob] = useState(null);
  const [visualizerReady, setVisualizerReady] = useState(false);

  // Check if the audio file exists and fetch it as a blob
  useEffect(() => {
    if (!audioSrc) {
      console.error('No audio source provided');
      setIsLoading(false);
      setFileExists(false);
      return;
    }

    // First check if the file exists
    fetch(audioSrc, { method: 'HEAD' })
      .then(response => {
        if (!response.ok) {
          console.error(`Audio file not found: ${audioSrc}`);
          setFileExists(false);
          setIsLoading(false);
        } else {
          setFileExists(true);

          // Now fetch the file as a blob for the visualizer
          fetch(audioSrc)
            .then(response => response.blob())
            .then(blob => {
              setAudioBlob(blob);
              setIsLoading(false);
            })
            .catch(err => {
              console.error(`Error fetching audio blob: ${err}`);
              setError('Error loading audio visualization');
              setIsLoading(false);
            });
        }
      })
      .catch(err => {
        console.error(`Error checking audio file: ${err}`);
        setFileExists(false);
        setIsLoading(false);
      });
  }, [audioSrc]);

  // Set up audio element event listeners
  useEffect(() => {
    const audio = audioRef.current;

    if (!audio || !audioSrc || !fileExists) return;

    const handleLoadedMetadata = () => {
      console.log('Audio metadata loaded, duration:', audio.duration);
      if (!isNaN(audio.duration)) {
        setDuration(audio.duration);
      } else {
        // Try to get duration another way
        setTimeout(() => {
          console.log('Trying to get duration again:', audio.duration);
          if (!isNaN(audio.duration)) {
            setDuration(audio.duration);
          }
        }, 1000);
      }
      setIsLoading(false);
    };

    const handleTimeUpdate = () => {
      console.log('Time update event, currentTime:', audio.currentTime);

      // Make sure we're getting a valid time
      if (!isNaN(audio.currentTime) && audio.currentTime > 0) {
        setCurrentTime(audio.currentTime);
      }
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
    };

    const handleError = (e) => {
      console.error('Audio error:', e);
      setError('Error loading audio file');
      setIsLoading(false);
    };

    // Add a canplay event handler to get duration
    const handleCanPlay = () => {
      console.log('Audio can play, duration:', audio.duration);
      if (!isNaN(audio.duration)) {
        setDuration(audio.duration);
      }
    };

    // Add event listeners
    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('error', handleError);

    // Set up a manual timer to update the current time as a backup
    let timer;
    if (isPlaying && audio) {
      timer = setInterval(() => {
        if (!isNaN(audio.currentTime)) {
          setCurrentTime(audio.currentTime);
        }
      }, 250); // Update 4 times per second
    }

    // Clean up
    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('error', handleError);

      if (timer) clearInterval(timer);
    };
  }, [audioSrc, fileExists, isPlaying]);

  // Check if we have a valid audio source
  const hasValidAudio = audioSrc && typeof audioSrc === 'string' && fileExists && !error;

  // Set up Media Session API
  useEffect(() => {
    // Early return if audio element isn't available
    if (!audioRef.current || !hasValidAudio) return;

    // Function to update media session metadata
    const updateMediaSessionMetadata = () => {
      if ('mediaSession' in navigator) {
        console.log('Setting Media Session metadata:', { title, author, artworkSrc });

        // Create artwork array with multiple sizes for better compatibility
        const artwork = [];

        // Add artwork in multiple sizes if available
        if (artworkSrc) {
          // Determine image type based on URL or data URL
          let imageType = 'image/jpeg'; // Default type

          if (artworkSrc.startsWith('data:')) {
            // Handle data URLs
            const dataUrlMatch = artworkSrc.match(/^data:(image\/[a-z]+);base64,/);
            if (dataUrlMatch && dataUrlMatch[1]) {
              imageType = dataUrlMatch[1];
            }
          } else {
            // Handle regular URLs based on file extension
            imageType = artworkSrc.toLowerCase().endsWith('.png') ? 'image/png' :
                       artworkSrc.toLowerCase().endsWith('.jpg') || artworkSrc.toLowerCase().endsWith('.jpeg') ? 'image/jpeg' :
                       'image/png';
          }

          // Log the artwork URL for debugging
          console.log('Using artwork URL:',
                     artworkSrc.startsWith('data:') ? 'data:URL (cropped square image)' : artworkSrc,
                     'with type:', imageType);

          // Add multiple sizes for better compatibility
          artwork.push(
            { src: artworkSrc, sizes: '96x96', type: imageType },
            { src: artworkSrc, sizes: '128x128', type: imageType },
            { src: artworkSrc, sizes: '192x192', type: imageType },
            { src: artworkSrc, sizes: '256x256', type: imageType },
            { src: artworkSrc, sizes: '384x384', type: imageType },
            { src: artworkSrc, sizes: '512x512', type: imageType }
          );
        }

        try {
          navigator.mediaSession.metadata = new MediaMetadata({
            title: title || 'Audio Article',
            artist: author || 'Thinkertags',
            album: 'Thinkertags Blog',
            artwork: artwork
          });

          console.log('Media Session metadata set successfully');
        } catch (error) {
          console.error('Error setting Media Session metadata:', error);
        }
      } else {
        console.log('Media Session API not supported');
      }
    };

    // Set metadata as soon as possible
    updateMediaSessionMetadata();

    // Set up media session handlers if available
    if ('mediaSession' in navigator) {
      try {
        // Set up action handlers with error handling
        const setupActionHandler = (action, handler) => {
          try {
            navigator.mediaSession.setActionHandler(action, handler);
          } catch (error) {
            console.warn(`The media session action "${action}" is not supported.`, error);
          }
        };

        // Play action
        setupActionHandler('play', () => {
          const playPromise = audioRef.current.play();
          if (playPromise !== undefined) {
            playPromise
              .then(() => {
                setIsPlaying(true);
                if ('playbackState' in navigator.mediaSession) {
                  navigator.mediaSession.playbackState = 'playing';
                }
              })
              .catch(err => console.error('Error playing audio:', err));
          }
        });

        // Pause action
        setupActionHandler('pause', () => {
          audioRef.current.pause();
          setIsPlaying(false);
          if ('playbackState' in navigator.mediaSession) {
            navigator.mediaSession.playbackState = 'paused';
          }
        });

        // Seek action
        setupActionHandler('seekto', (details) => {
          if (details.seekTime !== undefined && !isNaN(details.seekTime)) {
            audioRef.current.currentTime = details.seekTime;
            setCurrentTime(details.seekTime);
          }
        });

        // Additional actions for better compatibility
        setupActionHandler('seekbackward', (details) => {
          const skipTime = details.seekOffset || 10;
          audioRef.current.currentTime = Math.max(audioRef.current.currentTime - skipTime, 0);
          setCurrentTime(audioRef.current.currentTime);
        });

        setupActionHandler('seekforward', (details) => {
          const skipTime = details.seekOffset || 10;
          audioRef.current.currentTime = Math.min(
            audioRef.current.currentTime + skipTime,
            audioRef.current.duration || 0
          );
          setCurrentTime(audioRef.current.currentTime);
        });

        // Update position state if supported
        if ('setPositionState' in navigator.mediaSession) {
          const updatePositionState = () => {
            try {
              navigator.mediaSession.setPositionState({
                duration: audioRef.current.duration || 0,
                playbackRate: audioRef.current.playbackRate || 1,
                position: audioRef.current.currentTime || 0
              });
            } catch (error) {
              console.warn('Error updating position state:', error);
            }
          };

          // Update position state when time updates
          audioRef.current.addEventListener('timeupdate', updatePositionState);

          // Clean up
          return () => {
            audioRef.current?.removeEventListener('timeupdate', updatePositionState);
          };
        }
      } catch (error) {
        console.error('Error setting up Media Session handlers:', error);
      }
    }

    // Set metadata again when audio can play
    const handleCanPlay = () => {
      updateMediaSessionMetadata();
    };

    audioRef.current.addEventListener('canplay', handleCanPlay);

    return () => {
      audioRef.current?.removeEventListener('canplay', handleCanPlay);
    };
  }, [audioRef, hasValidAudio, title, author, artworkSrc]);

  // Handle play/pause
  const handlePlayPause = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      // Pause playback
      audio.pause();
      setIsPlaying(false);

      // Update Media Session state
      if ('mediaSession' in navigator) {
        try {
          navigator.mediaSession.playbackState = 'paused';
        } catch (error) {
          console.warn('Error updating Media Session playback state:', error);
        }
      }
    } else {
      // Update Media Session metadata before playing
      if ('mediaSession' in navigator) {
        try {
          // Ensure metadata is set before playing
          navigator.mediaSession.metadata = new MediaMetadata({
            title: title || 'Audio Article',
            artist: author || 'Thinkertags',
            album: 'Thinkertags Blog',
            artwork: artworkSrc ? (() => {
              // Determine image type based on URL or data URL
              let imageType = 'image/jpeg'; // Default type

              if (artworkSrc.startsWith('data:')) {
                // Handle data URLs
                const dataUrlMatch = artworkSrc.match(/^data:(image\/[a-z]+);base64,/);
                if (dataUrlMatch && dataUrlMatch[1]) {
                  imageType = dataUrlMatch[1];
                }
              } else {
                // Handle regular URLs based on file extension
                imageType = artworkSrc.toLowerCase().endsWith('.png') ? 'image/png' :
                           artworkSrc.toLowerCase().endsWith('.jpg') || artworkSrc.toLowerCase().endsWith('.jpeg') ? 'image/jpeg' :
                           'image/png';
              }

              console.log('Play action: Using artwork URL:',
                         artworkSrc.startsWith('data:') ? 'data:URL (cropped square image)' : artworkSrc,
                         'with type:', imageType);

              return [
                { src: artworkSrc, sizes: '96x96', type: imageType },
                { src: artworkSrc, sizes: '128x128', type: imageType },
                { src: artworkSrc, sizes: '192x192', type: imageType },
                { src: artworkSrc, sizes: '256x256', type: imageType },
                { src: artworkSrc, sizes: '384x384', type: imageType },
                { src: artworkSrc, sizes: '512x512', type: imageType }
              ];
            })() : []
          });

          console.log('Media Session metadata updated before play');
        } catch (error) {
          console.error('Error setting Media Session metadata before play:', error);
        }
      }

      // Start playback
      const playPromise = audio.play();

      if (playPromise !== undefined) {
        playPromise
          .then(() => {
            console.log('Audio playback started successfully');
            setIsPlaying(true);

            // Update Media Session state
            if ('mediaSession' in navigator) {
              try {
                navigator.mediaSession.playbackState = 'playing';

                // Update position state if supported
                if ('setPositionState' in navigator.mediaSession) {
                  navigator.mediaSession.setPositionState({
                    duration: audio.duration || 0,
                    playbackRate: audio.playbackRate || 1,
                    position: audio.currentTime || 0
                  });
                }
              } catch (error) {
                console.warn('Error updating Media Session state after play:', error);
              }
            }

            // Force an update of the current time
            setCurrentTime(audio.currentTime);

            // Set up a manual timer as a backup for timeupdate events
            const timer = setInterval(() => {
              if (audio && !audio.paused && !isNaN(audio.currentTime)) {
                setCurrentTime(audio.currentTime);

                // Update position state periodically
                if ('mediaSession' in navigator && 'setPositionState' in navigator.mediaSession) {
                  try {
                    navigator.mediaSession.setPositionState({
                      duration: audio.duration || 0,
                      playbackRate: audio.playbackRate || 1,
                      position: audio.currentTime || 0
                    });
                  } catch (error) {
                    // Silently fail on position state errors
                  }
                }
              } else {
                clearInterval(timer);
              }
            }, 250); // Update 4 times per second

            // Clean up the timer when component unmounts or audio pauses
            return () => clearInterval(timer);
          })
          .catch(err => {
            console.error('Error playing audio:', err);
            setError('Could not play audio file');
            setIsPlaying(false);
          });
      } else {
        // For older browsers that don't return a promise
        setIsPlaying(true);
        if ('mediaSession' in navigator) {
          try {
            navigator.mediaSession.playbackState = 'playing';
          } catch (error) {
            console.warn('Error updating Media Session playback state:', error);
          }
        }
      }
    }
  };

  // Handle progress bar click
  const handleProgressClick = (clickPosition) => {
    const audio = audioRef.current;
    if (!audio || isNaN(audio.duration) || audio.duration <= 0) return;

    const newTime = clickPosition * audio.duration;
    audio.currentTime = newTime;

    // Update state and UI immediately
    setCurrentTime(newTime);
  };

  // Format time in MM:SS
  const formatTime = (time) => {
    if (isNaN(time)) return '00:00';

    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);

    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // Set a timeout to prevent infinite loading
  useEffect(() => {
    if (isLoading) {
      const timer = setTimeout(() => {
        console.log('Loading timeout reached, forcing display of player');
        setIsLoading(false);
      }, 5000); // 5 seconds timeout

      return () => clearTimeout(timer);
    }
  }, [isLoading]);

  // Monitor the AudioVisualizer to detect when it's actually rendered
  useEffect(() => {
    if (!audioBlob) return;

    // Reset visualizer ready state when audio blob changes
    setVisualizerReady(false);

    // Use a longer delay to ensure the waveform is fully rendered
    // The AudioVisualizer component needs time to process the audio data
    // We're using a longer delay (1500ms) to ensure the waveform is fully processed
    const visualizerReadyDelay = setTimeout(() => {
      console.log('AudioVisualizer ready delay completed');
      setVisualizerReady(true);
    }, 1500);

    return () => clearTimeout(visualizerReadyDelay);
  }, [audioBlob]);

  // We'll keep the component structure the same during loading,
  // but show a skeleton for the waveform

  return (
    <div className="audio-player-container">
      <div className="audio-player-header">
        <div className="audio-player-title">
          <svg width="16" height="16" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg" className="audio-player-headphones-icon">
            <path d="M0 10.6934C0 13.6328 0.849609 16.875 2.28516 19.3945C2.5293 19.8145 2.98828 19.9316 3.42773 19.6875C3.83789 19.4629 3.95508 19.0039 3.70117 18.5449C2.39258 16.1621 1.66016 13.3203 1.66016 10.6934C1.66016 5.27344 4.9707 1.66016 9.94141 1.66016C14.9023 1.66016 18.2227 5.27344 18.2227 10.6934C18.2227 13.3203 17.4805 16.1621 16.1719 18.5449C15.918 19.0039 16.0352 19.4629 16.4453 19.6875C16.8848 19.9316 17.3535 19.8145 17.5879 19.3945C19.0234 16.875 19.8828 13.6328 19.8828 10.6934C19.8828 4.26758 15.918 0 9.94141 0C3.95508 0 0 4.26758 0 10.6934ZM3.05664 19.1504C3.37891 20.2734 4.33594 20.791 5.46875 20.4688C6.5918 20.1465 7.11914 19.1699 6.78711 18.0469L5.39062 13.2812C5.06836 12.168 4.11133 11.6406 2.97852 11.9629C1.85547 12.2949 1.32812 13.2617 1.66016 14.3945L3.05664 19.1504ZM16.8164 19.1504L18.2129 14.3945C18.5449 13.252 18.0273 12.2949 16.8945 11.9629C15.7617 11.6406 14.8145 12.168 14.4824 13.2812L13.0859 18.0469C12.7539 19.1797 13.2812 20.1465 14.4043 20.4688C15.5469 20.791 16.4941 20.2734 16.8164 19.1504Z" fill="black" fillOpacity="0.85"/>
          </svg>
          Listen to this article
        </div>
        <div className="audio-player-time">
          {formatTime(currentTime)} / {formatTime(duration)}
        </div>
      </div>
      <div className="audio-player">
        <audio
          ref={audioRef}
          src={hasValidAudio ? audioSrc : undefined}
          preload="metadata"
          playsInline
          controls={false}
          title={title}
          onLoadedMetadata={(e) => {
            console.log('Direct onLoadedMetadata event, duration:', e.target.duration);
            if (!isNaN(e.target.duration)) {
              setDuration(e.target.duration);
            }
          }}
        >
          {/* Add source with type for better compatibility */}
          {hasValidAudio && <source src={audioSrc} type="audio/mpeg" />}
          {/* Add track element for metadata */}
          <track kind="metadata" label={title} />
        </audio>

        <div className="audio-player-main-content">
          <button
            className="audio-player-play-button"
            onClick={handlePlayPause}
            aria-label={isPlaying ? 'Pause' : 'Play'}
            disabled={!hasValidAudio}
            tabIndex="-1" // Prevent focus styling
          >
            {isPlaying ? (
              <svg viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                <circle cx="18" cy="18" r="18" fill="black" />
                <rect x="13" y="11" width="3" height="14" rx="1.5" fill="white" />
                <rect x="20" y="11" width="3" height="14" rx="1.5" fill="white" />
              </svg>
            ) : (
              <svg viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                <circle cx="18" cy="18" r="18" fill="black" />
                <path d="M14 11L25 18L14 25V11Z" fill="white" />
              </svg>
            )}
          </button>

          <div
            className="audio-player-waveform-container"
            onClick={(e) => {
              if (!audioRef.current || isNaN(audioRef.current.duration) || audioRef.current.duration <= 0) return;

              const rect = e.currentTarget.getBoundingClientRect();
              const clickPosition = (e.clientX - rect.left) / rect.width;
              handleProgressClick(clickPosition);
            }}
          >
            {/* Always render both the skeleton and visualizer, but control visibility with CSS */}
            <div className={`audio-player-skeleton ${(!isLoading && audioBlob && visualizerReady && !skeleton_debug) ? 'hidden' : ''}`}>
              <Skeleton
                height={60}
                width="100%"
                baseColor="#f0f0f0"
                highlightColor="#f8f8f8"
                duration={1.5}
                borderRadius={4}
                style={{ display: 'block' }}
              />
            </div>

            {audioBlob && (
              <div
                className={`audio-player-visualizer-container ${(isLoading || !visualizerReady || skeleton_debug) ? 'hidden' : ''}`}
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  zIndex: 1,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <AudioVisualizer
                  ref={visualizerRef}
                  blob={audioBlob}
                  width={800}
                  height={60}
                  barWidth={6}
                  gap={4}
                  barColor={'#BBBBBB'}
                  barPlayedColor={'#333333'}
                  currentTime={currentTime}
                />
              </div>
            )}
          </div>
        </div>

        {!hasValidAudio && (
          <div className="audio-player-error">
            {!fileExists
              ? `Could not find audio file at "${audioSrc}". The file may be missing.`
              : error || "Could not load audio file. The file may be in an unsupported format."}
          </div>
        )}
      </div>
    </div>
  );
};

export default AudioPlayer;
