.heading-font-tester {
  position: fixed;
  bottom: 20px;
  left: 20px;
  width: 350px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  transition: all 0.3s ease;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.heading-font-tester.collapsed {
  max-height: 60px;
}

.heading-font-tester-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f5f5f5;
  cursor: pointer;
  user-select: none;
}

.heading-font-tester-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.heading-font-tester-toggle {
  font-size: 20px;
  font-weight: bold;
  color: #666;
}

.heading-font-tester-content {
  padding: 15px 20px;
  overflow-y: auto;
}

.heading-font-tester-description {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 14px;
  color: #666;
}

.heading-font-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.heading-font-option {
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.heading-font-option:hover {
  background-color: #f9f9f9;
  border-color: #d0d0d0;
}

.heading-font-option.selected {
  background-color: #f0f7ff;
  border-color: #0066cc;
}

.heading-font-option-sample {
  margin-bottom: 8px;
  font-size: 18px;
  line-height: 1.5;
  font-weight: 600;
}

.heading-font-option-name {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.heading-style-options {
  margin-bottom: 20px;
}

.heading-style-options h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 14px;
  color: #333;
}

.heading-style-buttons {
  display: flex;
  gap: 8px;
}

.heading-style-button {
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  background-color: white;
  cursor: pointer;
  font-size: 13px;
  color: #333;
  transition: all 0.2s ease;
}

.heading-style-button:hover {
  background-color: #f9f9f9;
  border-color: #d0d0d0;
}

.heading-style-button.selected {
  background-color: #f0f7ff;
  border-color: #0066cc;
  color: #0066cc;
}

.heading-font-tester-info {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
  font-size: 13px;
  color: #666;
}

.heading-font-tester-info p {
  margin: 0;
}

@media (max-width: 768px) {
  .heading-font-tester {
    width: calc(100% - 40px);
    bottom: 10px;
    left: 10px;
    right: 10px;
  }
}
