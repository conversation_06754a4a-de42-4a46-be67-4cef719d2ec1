.font-tester {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 350px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  transition: all 0.3s ease;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.font-tester.collapsed {
  max-height: 60px;
}

.font-tester-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f5f5f5;
  cursor: pointer;
  user-select: none;
}

.font-tester-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.font-tester-toggle {
  font-size: 20px;
  font-weight: bold;
  color: #666;
}

.font-tester-content {
  padding: 15px 20px;
  overflow-y: auto;
}

.font-tester-description {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 14px;
  color: #666;
}

.font-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.font-option {
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.font-option:hover {
  background-color: #f9f9f9;
  border-color: #d0d0d0;
}

.font-option.selected {
  background-color: #f0f7ff;
  border-color: #0066cc;
}

.font-option-sample {
  margin-bottom: 8px;
  font-size: 16px;
  line-height: 1.5;
}

.font-option-name {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.font-size-control {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.font-size-control label {
  display: block;
  margin-bottom: 10px;
  font-size: 14px;
  color: #666;
}

.font-size-slider {
  display: flex;
  align-items: center;
  gap: 10px;
}

.font-size-slider input[type="range"] {
  flex: 1;
  height: 6px;
  -webkit-appearance: none;
  appearance: none;
  background: #e0e0e0;
  border-radius: 3px;
  outline: none;
}

.font-size-slider input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: #0066cc;
  border-radius: 50%;
  cursor: pointer;
}

.font-size-slider input[type="range"]::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #0066cc;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

.font-size-buttons {
  display: flex;
  gap: 5px;
}

.font-size-buttons button {
  width: 30px;
  height: 30px;
  border-radius: 4px;
  border: 1px solid #d0d0d0;
  background-color: #f5f5f5;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.font-size-buttons button:hover {
  background-color: #e0e0e0;
}

.font-tester-info {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
  font-size: 13px;
  color: #666;
}

.font-tester-info p {
  margin: 0;
}

@media (max-width: 768px) {
  .font-tester {
    width: calc(100% - 40px);
    bottom: 10px;
    right: 10px;
    left: 10px;
  }
}
