import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import '../../styles/RelatedBlogPosts.css';
import { extractMetadata, transformImageUrl } from '../../utils/blogUtils';

/**
 * RelatedBlogPosts component displays preview cards for related blog posts
 *
 * @param {Object} props Component props
 * @param {string} props.currentPostId The ID of the current blog post
 * @param {Array<Object>} props.relatedPosts Array of related post objects with url and title
 */
const RelatedBlogPosts = ({ currentPostId, relatedPosts = [] }) => {
  const [relatedPostsData, setRelatedPostsData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchRelatedPosts = async () => {
      if (!relatedPosts || relatedPosts.length === 0) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);

        // Extract slugs from the URLs
        const postsInfo = relatedPosts.map(post => {
          const url = post.url;
          // Handle different URL formats
          let slug;
          if (url.includes('/research-and-insights/')) {
            slug = url.split('/research-and-insights/')[1];
          } else if (url.startsWith('/blog/')) {
            slug = url.substring(6);
          } else {
            slug = url;
          }
          return { slug, title: post.title };
        });

        // Fetch each related post in parallel
        const postsData = await Promise.all(
          postsInfo.map(async ({ slug, title }) => {
            try {
              // Try new structure first, fall back to old if needed
              let response = await fetch(`/blog/${slug}/${slug}.md`);

              // If new structure fails, try old structure
              if (!response.ok) {
                response = await fetch(`/blog/${slug}.md`);
                if (!response.ok) {
                  throw new Error(`Failed to fetch blog post: ${response.status}`);
                }
              }

              const markdown = await response.text();
              const meta = extractMetadata(markdown);

              // Process banner image path
              const bannerImagePath = meta.bannerImagePath
                ? transformImageUrl(meta.bannerImagePath, slug)
                : null;

              // Use the title from the link if available, otherwise use the one from metadata
              const postTitle = title || meta.title;

              return {
                slug,
                title: postTitle,
                bannerImage: bannerImagePath,
                subtitle: meta.subtitle || 'Research and Insights'
              };
            } catch (error) {
              console.error(`Error fetching related post ${slug}:`, error);
              return null;
            }
          })
        );

        // Filter out any failed fetches
        setRelatedPostsData(postsData.filter(post => post !== null));
      } catch (error) {
        console.error('Error fetching related posts:', error);
        setError('Failed to load related posts');
      } finally {
        setIsLoading(false);
      }
    };

    fetchRelatedPosts();
  }, [relatedPosts]);

  if (isLoading) {
    return (
      <div className="related-posts">
        <h2 className="related-posts-title">Read More</h2>
        <div className="related-posts-loading">Loading more articles...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="related-posts">
        <h2 className="related-posts-title">Read More</h2>
        <div className="related-posts-error">{error}</div>
      </div>
    );
  }

  if (relatedPostsData.length === 0) {
    return null;
  }

  return (
    <div className="related-posts">
      <h2 className="related-posts-title">Read More</h2>
      <div className="related-posts-grid">
        {relatedPostsData.map((post) => (
          <Link
            key={post.slug}
            to={`/research-and-insights/${post.slug}`}
            className="related-post-card"
          >
            {post.bannerImage && (
              <div className="related-post-image">
                <img src={post.bannerImage} alt={post.title} />
              </div>
            )}
            <div className="related-post-content">
              <h3 className="related-post-title">{post.title}</h3>
              <p className="related-post-subtitle">{post.subtitle}</p>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default RelatedBlogPosts;
