import React, { useState, useEffect, useRef } from 'react';
import '../../styles/BlogReactions.css';

/**
 * BlogReactions component allows users to react to blog posts with emojis
 * Styled as an iMessage conversation with animation on scroll
 *
 * @param {Object} props Component props
 * @param {string} props.postId The ID of the blog post
 * @param {string} props.articleTitle The title of the article (optional)
 * @param {Function} props.onReactionClick Optional callback when a reaction is clicked
 */
const BlogReactions = ({ postId, articleTitle = '', onReactionClick }) => {
  // State to track which reaction is selected
  const [selectedReaction, setSelectedReaction] = useState(null);
  // State to track if component is visible
  const [isVisible, setIsVisible] = useState(false);
  // State to track if typing indicator should be shown
  const [showTypingIndicator, setShowTypingIndicator] = useState(true);
  // State to track which bubble shows the typing indicator (1 or 2)
  const [typingBubble, setTypingBubble] = useState(1);
  // State to track if screen is small
  const [isSmallScreen, setIsSmallScreen] = useState(window.innerWidth <= 634);
  // Reference to the component
  const chatRef = useRef(null);

  // Simplified to just three reaction options
  const reactions = [
    { emoji: '🤔', label: 'Not really', id: 'thinking' },
    { emoji: '👏', label: 'Yes', id: 'applause' },
    { emoji: '❤️', label: 'Very much', id: 'love' }
  ];

  // Handle window resize events
  useEffect(() => {
    const handleResize = () => {
      setIsSmallScreen(window.innerWidth <= 634);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Set up Intersection Observer to detect when component is in viewport
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        // When component enters viewport, set isVisible to true
        if (entry.isIntersecting) {
          setIsVisible(true);

          // First typing indicator in the first bubble for 3 seconds
          setTimeout(() => {
            // Show "Soo" in first bubble and move typing indicator to second bubble
            setTypingBubble(2);

            // After 3 more seconds, hide typing indicator and show second message
            setTimeout(() => {
              setShowTypingIndicator(false);
            }, 3000);
          }, 3000);

          // Once we've seen it, no need to keep observing
          observer.disconnect();
        }
      },
      {
        // Start animation when element is 20% visible
        threshold: 0.2
      }
    );

    // Start observing the component
    if (chatRef.current) {
      observer.observe(chatRef.current);
    }

    // Clean up observer on component unmount
    return () => {
      if (chatRef.current) {
        observer.unobserve(chatRef.current);
      }
    };
  }, []);

  // Get the selected reaction object
  const getSelectedReactionObj = () => {
    return reactions.find(r => r.id === selectedReaction);
  };

  // Handle reaction click
  const handleReactionClick = (reactionId) => {
    // Toggle selection if clicking the same reaction again
    const newSelection = selectedReaction === reactionId ? null : reactionId;
    setSelectedReaction(newSelection);

    // Call the callback if provided
    if (onReactionClick) {
      onReactionClick({
        postId,
        reactionId: newSelection,
        timestamp: new Date().toISOString()
      });
    }

    // In the future, this would make an API call to save the reaction
    console.log(`User reacted with ${newSelection} to post ${postId}`);
  };

  return (
    <div className="blogreactions-wrapper" ref={chatRef}>

      <div className={`imessage-chat ${isVisible ? 'visible' : 'hidden'}`}>
        {/* Left side messages (from Thinkertags) */}
        <div className="message-group left">
          <div className={`message-bubble short-bubble ${isVisible && typingBubble !== 1 ? 'animate-in first' : ''}`}>
            {typingBubble === 1 && showTypingIndicator ? (
              <div className="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            ) : (
              'Soo'
            )}
          </div>
          <div className={`message-bubble ${isVisible && !showTypingIndicator ? 'animate-in second' : (typingBubble === 2 && showTypingIndicator ? 'typing-active' : 'hidden-message')}`}>
            {typingBubble === 2 && showTypingIndicator ? (
              <div className="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            ) : (
              !showTypingIndicator && (isSmallScreen
                ? "How did you like our insights?"
                : `How did you like ${articleTitle ? `"${articleTitle}"` : 'article'}?`)
            )}
          </div>
        </div>

        {/* Right side message (user's reaction) */}
        {selectedReaction && (
          <div className="message-group right">
            <div className="message-bubble animate-in">
              <span className="reaction-emoji">{getSelectedReactionObj()?.emoji}</span>
            </div>
          </div>
        )}

        {/* Emoji reaction options - mimics iMessage reaction bar */}
        <div className={`reaction-options-wrapper ${isVisible && !showTypingIndicator ? 'animate-in third' : 'hidden-message'}`}>
          <span className="reaction-prompt">Quick Reactions</span>
          <div className="reaction-options-container">
            {reactions.map((reaction) => (
              <button
                key={reaction.id}
                className={`reaction-option ${selectedReaction === reaction.id ? 'selected' : ''}`}
                onClick={() => handleReactionClick(reaction.id)}
                aria-label={`React with ${reaction.label}`}
                title={reaction.label}
              >
                <span className="reaction-emoji">{reaction.emoji}</span>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
//This is a comment
export default BlogReactions;
