import React, { useState, useEffect } from 'react';
import './HeadingFontTester.css';

const HeadingFontTester = () => {
  const [selectedHeadingFont, setSelectedHeadingFont] = useState('playfair');
  const [showHeadingFontTester, setShowHeadingFontTester] = useState(true);
  const [headingStyle, setHeadingStyle] = useState('uppercase'); // normal, uppercase, small-caps, bold, thin

  // Heading font options
  const headingFontOptions = [
    { id: 'playfair', name: 'Playfair Display (Current)', fontFamily: '"Playfair Display", serif' },
    { id: 'eb-garamond', name: '<PERSON><PERSON>', fontFamily: '"E<PERSON> Garamond", Georgia, serif' },
    { id: 'montserrat', name: '<PERSON><PERSON><PERSON> (Bold Sans-Serif)', fontFamily: 'Montserrat, sans-serif' },
    { id: 'source-sans', name: 'Source Sans Pro (Clean Sans-Serif)', fontFamily: '"Source Sans Pro", sans-serif' },
    { id: 'lora', name: '<PERSON><PERSON> (Elegant Serif)', fontFamily: '<PERSON>ra, serif' },
    { id: 'system-sans', name: 'System Sans-Serif', fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif' },
  ];

  // Heading style options
  const headingStyleOptions = [
    { id: 'normal', name: 'Normal' },
    { id: 'uppercase', name: 'UPPERCASE' },
    { id: 'small-caps', name: 'Small Caps' },
    { id: 'bold', name: 'Bold' },
    { id: 'thin', name: 'Thin' },
  ];

  // Apply the selected heading font to the blog content
  useEffect(() => {
    const headings = document.querySelectorAll('.blog-post-content h2, .blog-post-content h3, .blog-post-content h4');
    if (!headings.length) return;

    // Store original font-family for each heading if not already stored
    headings.forEach(heading => {
      if (!heading.dataset.originalFontFamily) {
        heading.dataset.originalFontFamily = window.getComputedStyle(heading).fontFamily;
        heading.dataset.originalTextTransform = window.getComputedStyle(heading).textTransform;
        heading.dataset.originalFontVariant = window.getComputedStyle(heading).fontVariant;
      }
    });

    // Apply selected font
    const selectedFontOption = headingFontOptions.find(option => option.id === selectedHeadingFont);
    if (selectedFontOption) {
      headings.forEach(heading => {
        heading.style.fontFamily = selectedFontOption.fontFamily;
      });
    }

    // Apply selected style
    headings.forEach(heading => {
      // Store original font weight if not already stored
      if (!heading.dataset.originalFontWeight) {
        heading.dataset.originalFontWeight = window.getComputedStyle(heading).fontWeight;
      }

      switch (headingStyle) {
        case 'uppercase':
          heading.style.textTransform = 'uppercase';
          heading.style.fontVariant = 'normal';
          heading.style.letterSpacing = '0.05em';
          heading.style.fontWeight = heading.dataset.originalFontWeight;
          break;
        case 'small-caps':
          heading.style.textTransform = 'none';
          heading.style.fontVariant = 'small-caps';
          heading.style.letterSpacing = '0.03em';
          heading.style.fontWeight = heading.dataset.originalFontWeight;
          break;
        case 'bold':
          heading.style.textTransform = 'none';
          heading.style.fontVariant = 'normal';
          heading.style.letterSpacing = '';
          heading.style.fontWeight = '800'; // Extra bold
          break;
        case 'thin':
          heading.style.textTransform = 'none';
          heading.style.fontVariant = 'normal';
          heading.style.letterSpacing = '0.02em'; // Slightly wider spacing for thin text
          heading.style.fontWeight = '400'; // Regular weight (thinner than default)
          break;
        default: // normal
          heading.style.textTransform = 'none';
          heading.style.fontVariant = 'normal';
          heading.style.letterSpacing = '';
          heading.style.fontWeight = heading.dataset.originalFontWeight;
      }
    });

    return () => {
      // Cleanup - restore original font when component unmounts
      headings.forEach(heading => {
        if (heading.dataset.originalFontFamily) {
          heading.style.fontFamily = heading.dataset.originalFontFamily;
          heading.style.textTransform = heading.dataset.originalTextTransform || '';
          heading.style.fontVariant = heading.dataset.originalFontVariant || '';
          heading.style.letterSpacing = '';
          heading.style.fontWeight = heading.dataset.originalFontWeight || '';
        }
      });
    };
  }, [selectedHeadingFont, headingStyle, headingFontOptions]);

  const handleHeadingFontChange = (fontId) => {
    setSelectedHeadingFont(fontId);
  };

  const handleHeadingStyleChange = (styleId) => {
    setHeadingStyle(styleId);
  };

  const toggleHeadingFontTester = () => {
    setShowHeadingFontTester(!showHeadingFontTester);
  };

  return (
    <div className={`heading-font-tester ${showHeadingFontTester ? 'expanded' : 'collapsed'}`}>
      <div className="heading-font-tester-header" onClick={toggleHeadingFontTester}>
        <h3>Heading Font Tester</h3>
        <span className="heading-font-tester-toggle">{showHeadingFontTester ? '−' : '+'}</span>
      </div>

      {showHeadingFontTester && (
        <div className="heading-font-tester-content">
          <p className="heading-font-tester-description">
            Try different fonts and styles for headings (H2-H4) while keeping EB Garamond for body text.
          </p>

          <div className="heading-font-options">
            {headingFontOptions.map(font => (
              <div
                key={font.id}
                className={`heading-font-option ${selectedHeadingFont === font.id ? 'selected' : ''}`}
                onClick={() => handleHeadingFontChange(font.id)}
              >
                <div className="heading-font-option-sample" style={{ fontFamily: font.fontFamily }}>
                  <span className="heading-sample-text">The quick brown fox</span>
                </div>
                <div className="heading-font-option-name">{font.name}</div>
              </div>
            ))}
          </div>

          <div className="heading-style-options">
            <h4>Heading Style</h4>
            <div className="heading-style-buttons">
              {headingStyleOptions.map(style => (
                <button
                  key={style.id}
                  className={`heading-style-button ${headingStyle === style.id ? 'selected' : ''}`}
                  onClick={() => handleHeadingStyleChange(style.id)}
                >
                  {style.name}
                </button>
              ))}
            </div>
          </div>

          <div className="heading-font-tester-info">
            <p>Currently viewing: <strong>{headingFontOptions.find(f => f.id === selectedHeadingFont)?.name}</strong> with <strong>{headingStyleOptions.find(s => s.id === headingStyle)?.name}</strong> style</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default HeadingFontTester;
