import React, { useState, useEffect } from 'react';
import './FontTester.css';

const FontTester = () => {
  const [selectedFont, setSelectedFont] = useState('eb-garamond'); // Default to E<PERSON> Garamond
  const [showFontTester, setShowFontTester] = useState(true);
  const [fontSize, setFontSize] = useState(20); // Default font size in px

  // Font options
  const fontOptions = [
    { id: 'eb-garamond', name: '<PERSON><PERSON> (Current)', fontFamily: '"EB Garamond", Georgia, serif' },
    { id: 'system', name: 'System Sans-Serif', fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif' },
    { id: 'georgia', name: 'Georgia Pro', fontFamily: 'Georgia, "Georgia Pro", serif' },
    { id: 'charter', name: 'Charter', fontFamily: 'Charter, serif' },
    { id: 'ibm-plex', name: 'IBM Plex Serif', fontFamily: '"IBM Plex Serif", serif' }
  ];

  // Apply the selected font and font size to the blog content
  useEffect(() => {
    const blogContent = document.querySelector('.blog-post-content');
    if (!blogContent) return;

    // Store original font-family and font-size
    if (!blogContent.dataset.originalFontFamily) {
      blogContent.dataset.originalFontFamily = window.getComputedStyle(blogContent).fontFamily;
      blogContent.dataset.originalFontSize = window.getComputedStyle(blogContent).fontSize;
    }

    // Apply selected font or restore original
    if (selectedFont === 'system') {
      blogContent.style.fontFamily = blogContent.dataset.originalFontFamily;
    } else {
      const selectedFontOption = fontOptions.find(option => option.id === selectedFont);
      if (selectedFontOption) {
        blogContent.style.fontFamily = selectedFontOption.fontFamily;
      }
    }

    // Apply font size
    blogContent.style.fontSize = `${fontSize}px`;

    // Apply font size to paragraphs for better readability
    const paragraphs = blogContent.querySelectorAll('p');
    paragraphs.forEach(p => {
      p.style.fontSize = `${fontSize}px`;
      p.style.lineHeight = `${Math.max(1.4, 1.6 - (fontSize - 20) * 0.01)}`; // Adjust line height based on font size
    });

    // Apply letter spacing based on font size
    blogContent.style.letterSpacing = `-${Math.max(0.01, 0.01 + (fontSize - 20) * 0.001)}em`;
    paragraphs.forEach(p => {
      p.style.letterSpacing = `-${Math.max(0.01, 0.01 + (fontSize - 20) * 0.001)}em`;
    });

    return () => {
      // Cleanup - restore original font and font size when component unmounts
      if (blogContent && blogContent.dataset.originalFontFamily) {
        blogContent.style.fontFamily = blogContent.dataset.originalFontFamily;
        blogContent.style.fontSize = blogContent.dataset.originalFontSize;
        blogContent.style.letterSpacing = '';

        // Reset paragraph styles
        const paragraphs = blogContent.querySelectorAll('p');
        paragraphs.forEach(p => {
          p.style.fontSize = '';
          p.style.lineHeight = '';
          p.style.letterSpacing = '';
        });
      }
    };
  }, [selectedFont, fontSize, fontOptions]);

  const handleFontChange = (fontId) => {
    setSelectedFont(fontId);
  };

  const handleFontSizeChange = (e) => {
    setFontSize(parseFloat(e.target.value));
  };

  const toggleFontTester = () => {
    setShowFontTester(!showFontTester);
  };

  return (
    <div className={`font-tester ${showFontTester ? 'expanded' : 'collapsed'}`}>
      <div className="font-tester-header" onClick={toggleFontTester}>
        <h3>Font Tester</h3>
        <span className="font-tester-toggle">{showFontTester ? '−' : '+'}</span>
      </div>

      {showFontTester && (
        <div className="font-tester-content">
          <p className="font-tester-description">
            Try different serif fonts for the body text while keeping Playfair Display for headings.
          </p>

          <div className="font-options">
            {fontOptions.map(font => (
              <div
                key={font.id}
                className={`font-option ${selectedFont === font.id ? 'selected' : ''}`}
                onClick={() => handleFontChange(font.id)}
              >
                <div className="font-option-sample" style={{ fontFamily: font.fontFamily }}>
                  <span className="font-sample-text">The quick brown fox jumps over the lazy dog.</span>
                </div>
                <div className="font-option-name">{font.name}</div>
              </div>
            ))}
          </div>

          <div className="font-size-control">
            <label htmlFor="font-size">Font Size: {fontSize}px</label>
            <div className="font-size-slider">
              <input
                type="range"
                id="font-size"
                min="16"
                max="24"
                step="1"
                value={fontSize}
                onChange={handleFontSizeChange}
              />
              <div className="font-size-buttons">
                <button onClick={() => setFontSize(Math.max(16, fontSize - 1))}>A-</button>
                <button onClick={() => setFontSize(Math.min(24, fontSize + 1))}>A+</button>
              </div>
            </div>
          </div>

          <div className="font-tester-info">
            <p>Currently viewing: <strong>{fontOptions.find(f => f.id === selectedFont)?.name}</strong></p>
          </div>
        </div>
      )}
    </div>
  );
};

export default FontTester;
