import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCookies } from 'react-cookie';
import { Auth } from 'aws-amplify';
import { User } from 'lucide-react';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import '../styles/Navbar.css';
import { useLocation } from './LocationContext';
import { useSteppingMode } from './SteppingModeContext';

const Navbar = ({ user, setUser, loading }) => {
  const [cookies, setCookie, removeCookie] = useCookies(['idToken', 'location_mode']);
  const navigate = useNavigate();
  const { location, setLocation, isLocationReady, setIsLocationReady, setIsLoadingLocation } = useLocation();
  const { isSteppingMode, toggleSteppingMode } = useSteppingMode();
  const [isLocationEnabled, setIsLocationEnabled] = useState(cookies.location_mode === 'true');

  // Check if we're on the landing page or a /f path
  const currentPath = window.location.pathname;
  const isLandingPage = currentPath === '/enterprise' || currentPath === '/'
  const isFormPage = currentPath.startsWith('/f/');
  const isDocumentViewPage = /^\/assets\/[^/]+\/documents\/[^/]+$/.test(currentPath);
  const isBlogPostPage = currentPath.startsWith('/blog/');


  // No need for the useEffect that was watching cookies.stepping_mode
  // since we're now using the SteppingModeContext

  // Get location when location mode is enabled
  useEffect(() => {
    const getLocation = async () => {
      if (isLocationEnabled && !location.latitude && !location.longitude) {
        try {
          setIsLoadingLocation(true);
          const position = await new Promise((resolve, reject) => {
            navigator.geolocation?.getCurrentPosition(resolve, reject);
          });
          setLocation({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          });
          setIsLocationReady(true);
        } catch (error) {
          console.error('Error getting location:', error);
          setIsLocationReady(true);
        }
      } else if (!isLocationEnabled) {
        setLocation({ latitude: null, longitude: null });
        setIsLocationReady(true);
        setIsLoadingLocation(false);
      }
    };

    getLocation();
  }, [isLocationEnabled, location.latitude, location.longitude, setLocation, setIsLocationReady, setIsLoadingLocation]);

  const cookieDomain = import.meta.env.VITE_COOKIE_DOMAIN;

  // Use the toggleSteppingMode function directly from context
  // No need for a wrapper function that could cause unnecessary re-renders

  // Log when Navbar renders - for debugging purposes
  console.log('Navbar rendered, stepping mode:', isSteppingMode);

  const handleLocationToggle = () => {
    const newValue = !isLocationEnabled;
    setIsLocationEnabled(newValue);
    setIsLocationReady(false);
    setIsLoadingLocation(true);

    if (newValue) {
      setCookie('location_mode', 'true', {
        path: '/',
        domain: cookieDomain,
        sameSite: 'none',
        secure: !!cookieDomain,
        maxAge: 24 * 60 * 60,
      });
    } else {
      removeCookie('location_mode', {
        path: '/',
        domain: cookieDomain,
        sameSite: 'none',
        secure: !!cookieDomain,
      });
    }
  };

  const handleLogout = async () => {
    try {
      await Auth.signOut();
      removeCookie('idToken', { domain: cookieDomain });
      setUser(null);
      navigate('/');
    } catch (error) {
      console.log('error signing out: ', error);
    }
  };

  const handleLogin = () => {
    navigate('/login');
  };

  const handleConsole = () => {
    // If user is logged in, navigate to appropriate page
    navigate('/tags');
  };

  const renderNavItem = (width, height, children) => {
    if (loading) {
      return (
        <div style={{ width, height }}>
          <Skeleton height={height} borderRadius="10px" />
        </div>
      );
    }
    return children;
  };

  const isOperator = user?.role === 'Operator';
  const navUserText = isOperator ? "My Profile" : "Manage";
  const navUserDestination = isOperator && user?.username ? `/users/${user.username}` : '/invite';

  if (isFormPage || isDocumentViewPage) {
    return <></>; // Return empty fragment instead of null
  }

  // Always show the navbar with logo on blog post pages

  return (
    <div>
      {user && !isLandingPage ? (
        <nav className="isometric-navbar">
          <div className="nav-container">
          {renderNavItem('111px', '51px',
              <div className="nav-item two-one pause-button row-2" onClick={() => navigate('/tags')}>
                <div className="icon-wrapper-groups">
                  <img src="/navbar_tags_icon.svg" alt="Browse Tags and Assets" />
                </div>
                <div className="text-wrapper-groups">
                  <span className="top-text">Tags</span>
                  <span className="bottom-text">Browse</span>
                </div>
              </div>
            )}

            {renderNavItem('111px', '51px',
              <div className="nav-item two-one pause-button" onClick={toggleSteppingMode}>
                <div className="icon-wrapper">
                  <div className={isSteppingMode ? "pause-icon" : "pause_disabled-icon"}></div>
                </div>
                <div className="text-wrapper">
                  <span className="top-text">Redirect</span>
                  <span className="bottom-text">{isSteppingMode ? "Paused" : "Follow"}</span>
                </div>
              </div>
            )}

            {renderNavItem('50px', '50px',
              <div className="nav-item one-one col-start-4 row-2" onClick={handleLocationToggle}>
                <div className="icon-wrapper-location">
                  <img
                    src={isLocationEnabled ? "/location_active.svg" : "/location_disabled.svg"}
                    alt="Location"
                  />
                </div>
              </div>
            )}

            {renderNavItem('111px', '51px',
              <div className="nav-item two-one pause-button row-2" onClick={() => navigate('/assets/workbench')}>
                <div className="icon-wrapper-groups">
                  <img src="/groups.svg" alt="Assets Workbench" />
                </div>
                <div className="text-wrapper-groups">
                  <span className="top-text">Assets</span>
                  <span className="bottom-text">Workbench</span>
                </div>
              </div>
            )}

            {renderNavItem('111px', '51px',
              <div className="nav-item two-one pause-button row-2" onClick={() => navigate(navUserDestination)}>
                <div className="icon-wrapper-groups">
                  <img src="/invite.svg" alt="Manage Users" />
                </div>
                <div className="text-wrapper-groups">
                  <span className="top-text">Users</span>
                  <span className="bottom-text">{navUserText}</span>
                </div>
              </div>
            )}

            {renderNavItem('111px', '51px',
              <div className="nav-item two-one log-out" onClick={handleLogout}>
                <span>Log out</span>
                <span className="bottom-text"></span>
              </div>
            )}
          </div>
        </nav>
      ) : (
        <div className="logo-container">
          <div></div>
          <div className="logo" onClick={() => navigate('/')}>
            <img src="/logo.svg" alt="thinkertags" />
          </div>
          {!isLandingPage && (
            <div className="sign-in-out-button">
              <button className="sign-in-button" onClick={user ? handleConsole : handleLogin}>Access Console</button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export { Navbar };