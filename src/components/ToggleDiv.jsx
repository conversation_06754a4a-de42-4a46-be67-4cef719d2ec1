import React, { useState } from 'react';
import '../styles/ToggleDiv.css';

const ToggleDiv = ({ data, currentState, onToggleChange }) => {
  
  // Re-introducing the isActive state to control expansion/collapse
  const [isActive, setIsActive] = useState(data.isInitiallyExpanded || false);

  const handleMainClick = () => {
    setIsActive(!isActive);
    console.log('isActive', isActive)
  };

  return (
    <div className="toggle-div">
      <div className={`toggle-div-main ${!isActive ? 'collapsed' : ''}`} onClick={handleMainClick}>
        <div className="toggle-div-text-and-icon">
          <span className="toggle-icon material-icons-outlined">{data.icon}</span>
          <div className="toggle-text-container">
            <div className="toggle-title-text">{data.title}</div>
            <div className="toggle-title-body">{data.description}</div>
          </div>
          <div className="spacer"></div>
          <div className="status-text" style={{
            backgroundColor: currentState ? data.activeBgColor : data.inactiveBgColor,
            color: currentState ? data.activeColor : data.inactiveColor
          }}>
            {currentState ? data.activeText : data.inactiveText}
          </div>
          <span className="toggle-icon material-icons-outlined">
            {isActive ? data.iconCollapse : data.iconExpand}
          </span>
        </div>
      </div>

      <div className={`toggle-div-collapsable-1 ${isActive ? '' : 'collapsed'}`}>
        <div className="toggle-div-collapsable-1-sub">
          <div className="toggle-text-container">
            <div className="toggle-title-text">{data.subTitle}</div>
            <div className="toggle-title-body">{data.subDescription}</div>
          </div>
          <label className="switch">
            <input
              type="checkbox"
              checked={currentState}
              onChange={() => onToggleChange(!currentState)}
            />
            <span className="slider round"></span>
          </label>
        </div>
      </div>

      <div className={`toggle-div-collapsable-2 ${isActive ? '' : 'collapsed'} ${data.useSimpleTextInsteadOfInput ? 'simple-text' : ''}`}>
    {data.useSimpleTextInsteadOfInput ? (
        <div className="simple-text-content">{data.simpleTextValue}</div>
    ) : (
      <>
        <div className="label">
            <label>{data.urlLabel}</label>
        </div>
        <input 
            type='url' 
            disabled={!currentState} 
            className='input url-input' 
            placeholder={data.urlPlaceholder} 
            value={data.urlValue} 
            onChange={e => data.handleUrlChange(e.target.value)}
        />
      </>
    )}
</div>

    </div>
  );
};

const ToggleDivSimple = ({ data, onToggleChange }) => {
  
  // Re-introducing the isActive state to control expansion/collapse
  const [isActive, setIsActive] = useState(data.isInitiallyExpanded || false);

  const handleMainClick = () => {
    setIsActive(!isActive);
    console.log('isActive', isActive)
  };

  return (
    <div className="toggle-div">
      <div className={`toggle-div-main ${!isActive ? 'collapsed' : ''}`} onClick={handleMainClick}>
        <div className="toggle-div-text-and-icon">
          <span className="toggle-icon material-icons-outlined">{data.icon}</span>
          <div className="toggle-text-container">
            <div className="toggle-title-text">{data.title}</div>
            <div className="toggle-title-body">{data.description}</div>
          </div>
          <div className="spacer"></div>
          <div className="status-text" style={{
            backgroundColor: data.urlValue ? data.activeBgColor : data.inactiveBgColor,
            color: data.urlValue  ? data.activeColor : data.inactiveColor
          }}>
            {data.urlValue ? data.activeText : data.inactiveText}
          </div>
          <span className="toggle-icon material-icons-outlined">
            {isActive ? data.iconCollapse : data.iconExpand}
          </span>
        </div>
      </div>

      <div className={`toggle-div-collapsable-2 ${isActive ? '' : 'collapsed'} ${data.useSimpleTextInsteadOfInput ? 'simple-text' : ''}`}>
    {data.useSimpleTextInsteadOfInput ? (
        <div className="simple-text-content">{data.simpleTextValue}</div>
    ) : (
      <>
        <div className="label">
            <label>{data.urlLabel}</label>
        </div>
        <input 
            type='url' 
            className='input url-input' 
            placeholder={data.urlPlaceholder} 
            value={data.urlValue} 
            onChange={e => data.handleUrlChange(e.target.value)} 
        />
      </>
    )}
</div>

    </div>
  );
};

export {ToggleDiv, ToggleDivSimple}
