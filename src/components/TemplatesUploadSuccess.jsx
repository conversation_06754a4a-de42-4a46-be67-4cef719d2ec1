import React, { useEffect } from 'react';
import '../styles/UpdateSuccessful.css';
import { useNavigate, useParams } from 'react-router-dom';

const SuccessAnimation = () => {
  const navigate = useNavigate();
  const { tagId } = useParams();

  useEffect(() => {
    // Store the current scroll position
    const currentScroll = window.scrollY;

    // Set the scroll position to the top
    window.scrollTo(0, 0);

    const timer = setTimeout(() => {
      navigate(`/tags`);
    }, 1800);

    // Restore the scroll position and clean up the timer when the component is unmounted
    return () => {
      clearTimeout(timer);
      window.scrollTo(0, currentScroll);
    };
  }, [navigate, tagId]);

  return (
    <div className="animation-wrapper">
      <div className="icon-container">
        <svg
          className="checkmark-icon"
          xmlns="http://www.w3.org/2000/svg"
          height="100"
          width="100"
          viewBox="0 0 48 48"
          aria-hidden="true"
        >
          <circle className="checkmark-circle" fill="#5bb543" cx="24" cy="24" r="22" />
          <path
            className="checkmark-tick"
            fill="none"
            stroke="#FFF"
            strokeWidth="6"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeMiterlimit="10"
            d="M14 27l5.917 4.917L34 17"
          />
        </svg>
        <div className="confirmation-text">
          <h2>{tagId}</h2>
          <p>Your templates has been uploaded successfully!</p>
        </div>
      </div>
    </div>
  );
};

export default SuccessAnimation;
