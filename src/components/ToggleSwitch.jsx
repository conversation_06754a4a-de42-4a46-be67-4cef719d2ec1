import React, { useState, useCallback, useEffect } from 'react';
import { motion } from 'framer-motion';
import '../styles/ToggleSwitch.css';

const ToggleSwitch = ({ options, onChange = () => {}, activeOption, specialRounded = [] }) => {
  const [activeIndex, setActiveIndex] = useState(0);

  // Update activeIndex when activeOption changes externally
  useEffect(() => {
    if (activeOption) {
      const index = options.indexOf(activeOption);
      if (index !== -1) {
        setActiveIndex(index);
      }
    }
  }, [activeOption, options]);

  const handleToggle = useCallback((index) => {
    setActiveIndex(index);
    onChange(options[index]);
  }, [onChange, options]);

  const isSpecialRounded = specialRounded.includes(options[activeIndex]);

  return (
    <div 
      className="toggle-switch-container"
      style={{
        marginBottom: isSpecialRounded ? '15px' : '0'
      }}
    >
      <div 
        className="toggle-switch"
        style={{
          borderRadius: isSpecialRounded ? '8px' : '8px 8px 0 0'
        }}
      >
        {options.map((option, index) => (
          <button
            key={option}
            className={`toggle-option ${index === activeIndex ? 'active' : ''}`}
            onClick={() => handleToggle(index)}
          >
            {option}
          </button>
        ))}
        <motion.div
          className="sliders"
          initial={false}
          animate={{
            left: `calc(${(activeIndex / options.length) * 100}% + (${activeIndex === 0 ? '5px' : activeIndex === options.length - 1 ? '-2px' : '0px'}))`,
          }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
          style={{ 
            width: `calc((100% - 10px) / ${options.length})`,
            borderRadius: isSpecialRounded ? '6px' : '6px'
          }}
        />
      </div>
    </div>
  );
};

export { ToggleSwitch };