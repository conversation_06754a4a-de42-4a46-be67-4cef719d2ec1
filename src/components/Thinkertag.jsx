import React from 'react';
import QRCode, { QRCodeCanvas } from 'qrcode.react';
import Skeleton from 'react-loading-skeleton';
import '../styles/Thinkertag.css';
import { Weight } from 'lucide-react';

const SIZES = {
  normal: {
    qrWidth: '86px',
    qrHeight: '86px',
    padding: '22px',
    paddingTop: '22px',
    paddingBottom: '5px',
    borderRadius: '13px',
    outerBorderRadius: '20px',
    outerPadding: '8px',
    fontSize: '12px'
  },
  small: {
    qrWidth: '66px',
    qrHeight: '66px',
    padding: '17px',
    paddingTop: '16.5px',
    paddingBottom: '4px',
    borderRadius: '10px',
    outerBorderRadius: '14px',
    outerPadding: '5px',
    fontSize: '9px'
  }
};

const Thinkertag = ({ 
  value = 'skeleton',
  url, 
  size = 'normal',
  tagId = 'ABCDEF',
  disableShadow,
  disableSheen,
  customSizes = {},
  isLoading = false
}) => {
  const sizeConfig = { ...SIZES[size], ...customSizes };
  
  const containerStyle = Object.entries(sizeConfig).reduce((acc, [key, value]) => ({
    ...acc,
    [`--${key}`]: value
  }), {});

  if (isLoading) {
    // Calculate total width/height including all padding
    const qrWidth = parseInt(sizeConfig.qrWidth);
    const padding = parseInt(sizeConfig.padding);
    const outerPadding = parseInt(sizeConfig.outerPadding);
    
    // Total size = QR size + (padding * 2) + (outer padding * 2)
    const totalSize = qrWidth + (padding * 2) + (outerPadding * 2);
    
    return (
        <Skeleton 
          width={`${totalSize}px`}
          height={`${totalSize}px`}
          borderRadius={sizeConfig.outerBorderRadius}
          baseColor="#e2e2e2"
          highlightColor="#f0f0f0"
          style={{ 
            boxShadow: 'none'
          }}/>
    );
  }

  const containerClasses = [
    'qr-code-container',
    `qr-theme-${value}`,
  ].filter(Boolean).join(' ');

  const wrapperClasses = [
    'qr-code-style-wrapper',
    disableShadow ? 'qr-no-shadow' : '',
    disableSheen ? 'qr-no-sheen' : ''
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses} style={containerStyle}>
      <div className={wrapperClasses}>
        {!disableSheen && <div className="sheen" />}
        <div className="qr-code-white-wrapper">
          <div className="qr-codes">
            <QRCodeCanvas value={url} size={512} />
          </div>
          <div className="qr-code-value">{tagId}</div>
        </div>
      </div>
    </div>
  );
};

export { Thinkertag };