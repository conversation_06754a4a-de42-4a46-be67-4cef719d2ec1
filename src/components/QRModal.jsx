import React from 'react';
import QRCode from 'qrcode.react';
import Modal from 'react-modal';
import '../styles/QRModal.css'; // Import your styles

Modal.setAppElement('#root'); // This line is needed for accessibility reasons

const QRModal = ({ isOpen, closeModal, url }) => {
  return (
    <Modal 
      isOpen={isOpen} 
      onRequestClose={closeModal}
      style={{
        overlay: {
          backgroundColor: 'rgba(0, 0, 0, 0.5)'
        },
        content: {
          top: '50%',
          left: '50%',
          right: 'auto',
          bottom: 'auto',
          marginRight: '-50%',
          transform: 'translate(-50%, -50%)',
          borderRadius: '20px', // Gives the modal rounded corners
          display: 'flex', // Centers the content inside the modal
          background: 'rgba(248, 248, 248, 1)',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '30px', // Adds some padding around the content
        }
      }}
    >


{url && 
  <div className="modal-container">
    <div className="h1">Ta-da! Here is your <br /> <span className="gradient-text">ThinkerTag</span></div>
      <div className="qr-glow-wrapper">
       <div className="qr-wrapper">
        <QRCode value={url} size={128} />
      </div>
    </div>
    <div className="qr-id">{url.split("/").pop()}</div>    
  </div>
}
    
    </Modal>
  );
};

export default QRModal;
