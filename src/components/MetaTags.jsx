import { useEffect } from 'react';
import { generateArticleStructuredData, generateOrganizationStructuredData, injectStructuredData } from '../utils/structuredDataUtils';

/**
 * A simple component to dynamically update meta tags in the document head
 * @param {Object} props - The component props
 * @param {string} props.title - The page title
 * @param {string} props.description - The page description
 * @param {string} props.imageUrl - URL to the image to be used in previews
 * @param {number} props.imageWidth - Width of the image (optional)
 * @param {number} props.imageHeight - Height of the image (optional)
 * @param {string} props.url - The canonical URL for the page
 * @param {string} props.type - The type of content (default: 'article')
 * @param {string} props.author - The author of the content
 * @param {string} props.siteName - The site name (default: 'Thinkertags Research and Insights')
 * @param {string} props.datePublished - The publication date in ISO format (optional)
 * @param {string} props.dateModified - The last modification date in ISO format (optional)
 */
const MetaTags = ({
  title,
  description,
  imageUrl,
  imageWidth,
  imageHeight,
  url,
  type = 'article',
  author,
  siteName = 'Thinkertags Research and Insights',
  datePublished,
  dateModified
}) => {
  useEffect(() => {
    // Save the original title to restore it when component unmounts
    const originalTitle = document.title;

    // Update the document title
    if (title) {
      document.title = title;
    }

    // Helper function to create or update a meta tag
    const setMetaTag = (name, content) => {
      if (!content) return;

      // Try to find an existing tag
      let meta = document.querySelector(`meta[name="${name}"]`) ||
                document.querySelector(`meta[property="${name}"]`);

      // If the tag doesn't exist, create it
      if (!meta) {
        meta = document.createElement('meta');
        if (name.startsWith('og:') || name.startsWith('twitter:')) {
          meta.setAttribute('property', name);
        } else {
          meta.setAttribute('name', name);
        }
        document.head.appendChild(meta);
      }

      // Set the content attribute
      meta.setAttribute('content', content);
    };

    // Set basic meta tags
    setMetaTag('description', description);

    // Set Open Graph meta tags
    setMetaTag('og:title', title);
    setMetaTag('og:description', description);
    setMetaTag('og:image', imageUrl);
    setMetaTag('og:url', url);
    setMetaTag('og:type', type);
    setMetaTag('og:site_name', siteName);

    // Add image dimensions if available
    if (imageWidth) {
      setMetaTag('og:image:width', String(imageWidth));
    }
    if (imageHeight) {
      setMetaTag('og:image:height', String(imageHeight));
    }

    // Set Twitter Card meta tags
    setMetaTag('twitter:card', 'summary_large_image');
    setMetaTag('twitter:title', title);
    setMetaTag('twitter:description', description);
    setMetaTag('twitter:image', imageUrl);
    setMetaTag('twitter:site', '@thinkertags');
    setMetaTag('twitter:creator', '@thinkertags');

    // Set article specific meta tags if type is article
    if (type === 'article' && author) {
      setMetaTag('article:author', author);
      // Add current date as published time if not provided
      const now = new Date().toISOString();
      setMetaTag('article:published_time', now);
    }

    // Set author meta tag if provided
    if (author) {
      setMetaTag('author', author);
    }

    // Add canonical link
    let canonicalLink = document.querySelector('link[rel="canonical"]');
    if (!canonicalLink) {
      canonicalLink = document.createElement('link');
      canonicalLink.setAttribute('rel', 'canonical');
      document.head.appendChild(canonicalLink);
    }
    canonicalLink.setAttribute('href', url);

    // Add structured data based on content type
    if (type === 'article') {
      // Use current date if datePublished not provided
      const pubDate = datePublished || new Date().toISOString();
      const modDate = dateModified || pubDate;

      const articleData = generateArticleStructuredData({
        title,
        description,
        url,
        imageUrl,
        author,
        datePublished: pubDate,
        dateModified: modDate
      });

      injectStructuredData(articleData);
    } else if (type === 'website' && url === 'https://thinkertags.com/') {
      // Only inject organization data on the homepage
      const orgData = generateOrganizationStructuredData();
      injectStructuredData(orgData);
    }

    // Cleanup function to restore original title and remove added meta tags
    return () => {
      document.title = originalTitle;
      // Remove canonical link
      if (canonicalLink) {
        document.head.removeChild(canonicalLink);
      }

      // Remove structured data script
      const structuredDataScript = document.querySelector('script[type="application/ld+json"]');
      if (structuredDataScript) {
        document.head.removeChild(structuredDataScript);
      }
    };
  }, [title, description, imageUrl, imageWidth, imageHeight, url, type, author, siteName, datePublished, dateModified]);

  // This component doesn't render anything
  return null;
};

export default MetaTags;
