import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCookies } from 'react-cookie';
import { Auth } from 'aws-amplify';

const TagRedirect = () => {
  const navigate = useNavigate();
  const [cookies, setCookie] = useCookies(['idToken']); // Access and update the 'idToken' cookie

  useEffect(() => {
    const updateTokenAndRedirect = async () => {
      try {
        // Fetch the latest token from the Auth library
        const session = await Auth.currentSession();
        const idToken = session.getIdToken().getJwtToken();

        // Update the 'idToken' cookie
        setCookie('idToken', idToken, {
          path: '/',
          domain: '.thinkertags.com',
          sameSite: 'none',
          secure: true,
          maxAge: 24 * 60 * 60, // Expires in 1 day
        });

        const urlSearchParams = new URLSearchParams(window.location.search);
        const tagId = urlSearchParams.get('tag'); // Retrieve the 'tag' query parameter

        // Redirect to the desired URL
        if (tagId) {
          window.location.href = `https://api.thinkertags.com/${tagId}`;
        } else {
          console.log("No tagId");
          navigate('/'); // Redirect to home if tagId is not provided
        }
      } catch (error) {
        console.error('Failed to update token:', error);
        navigate('/'); // Handle the error as needed
      }
    };

    updateTokenAndRedirect();
  }, [navigate, setCookie]);

  return null; // This component does not render anything
};

export { TagRedirect };
