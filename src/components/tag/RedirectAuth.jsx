import React from 'react';
import Skeleton from 'react-loading-skeleton';
import { Input } from '../Input';

/**
 * RedirectAuth - A reusable component for the "Redirect Authenticated" feature
 *
 * @param {boolean} isLoading - Indicates if data is loading
 * @param {boolean} isActive - Whether the redirect is active
 * @param {boolean} isUrlLocked - Whether the URL input is locked (linked to the StoreFront)
 * @param {string} url - The current URL value
 * @param {function} onToggle - Handler for toggling the redirect on/off
 * @param {function} onUrlChange - Handler for URL input changes
 * @param {function} onUrlBlur - Handler for URL input blur events
 * @param {function} ensureProtocol - Function to ensure URL has proper protocol
 */
const RedirectAuth = ({
  isLoading,
  isActive,
  isUrlLocked,
  url,
  onToggle,
  onUrlChange,
  onUrlBlur,
  ensureProtocol
}) => {
  // Handle blur with protocol check
  const handleBlur = (e) => {
    if (!isUrlLocked && onUrlBlur) {
      onUrlBlur(e, ensureProtocol);
    }
  };

  return (
    <>
      {isLoading ? (
        <div style={{ marginBottom: '15px', marginTop: '7.5px' }}>
          <Skeleton
            height={66}
            borderRadius={10}
            baseColor="#ebebeb"
            highlightColor="#ffffff"
          />
        </div>
      ) : (
        <div className="settings-input-group-two">
          <div
            className={`toggle-div-collapsable-1-sub-1 ${
              isActive ? 'expanded' : ''
            }`}
          >
            <div className="toggle-text-container-parent">
              <div className="link-primary-icon">
                <img
                  src="/redirect-private.svg"
                  alt="Auth Redirect"
                  width="16"
                  height="16"
                />
              </div>
              <div className="toggle-text-container">
                <div className="settings-input-group-h4">Redirect Authenticated</div>
                <div className="settings-input-group-h4-sub">
                  Enable redirect for authenticated users
                </div>
              </div>
            </div>

            <label className="switch">
              <input
                type="checkbox"
                checked={isActive}
                onChange={(e) => onToggle(e.target.checked)}
                disabled={isUrlLocked}
              />
              <span className="slider round" />
            </label>
          </div>
          <div
            className={`redirect-input-container ${
              isActive ? 'expanded' : ''
            }`}
          >
            <div
              style={{
                borderTop: '1px solid #E5E7EB',
                margin: '12px 0',
              }}
            />
            <div className="input-group" style={{ transform: 'translateZ(0)' }}>
              <label>
                Redirect
                {isUrlLocked && (
                  <span style={{ fontSize: '12px', color: '#666', fontWeight: 'normal', marginLeft: '5px' }}>
                    (Linked to the StoreFront)
                  </span>
                )}
              </label>
              <Input
                className={`input ${isUrlLocked ? 'locked-input' : ''}`}
                name="privateUrl"
                placeholder="https://manage.mywebsite.com"
                value={url}
                type="url"
                enforceProtocol
                onChange={onUrlChange}
                onBlur={handleBlur}
                readOnly={isUrlLocked}
                style={isUrlLocked ? { backgroundColor: '#f5f5f5', borderColor: '#ddd', cursor: 'not-allowed' } : {}}
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default RedirectAuth;