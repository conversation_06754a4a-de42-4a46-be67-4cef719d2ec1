import React from 'react';
import { Thinkertag } from '../Thinkertag';
import { Input } from '../Input';
import Skeleton from 'react-loading-skeleton';

/**
 * ThinkertagsPreviewPanel component - displays tag QR code and basic info
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.isLoading - Whether data is currently loading
 * @param {string} props.tagStyle - Visual style of the tag
 * @param {string} props.tagId - ID of the tag
 * @param {string} props.tagName - Name of the tag
 * @param {string} props.publicUrl - Public URL for redirection
 * @param {boolean} props.isPublicUrlLocked - Whether public URL is locked due to asset connection
 * @param {Function} props.onNameChange - Handler for tag name changes
 * @param {Function} props.onPublicUrlChange - Handler for public URL changes
 * @param {Function} props.onPublicUrlBlur - Handler for public URL blur event
 */
export function ThinkertagsPreviewPanel({
  isLoading = false,
  tagStyle = 'default',
  tagId = '',
  tagName = '',
  publicUrl = '',
  isPublicUrlLocked = false,
  onNameChange = () => {},
  onPublicUrlChange = () => {},
  onPublicUrlBlur = () => {}
}) {
  return (
    <div className="thinkertag-preview-panel">
      <div className="thinkertag-preview-qr-wrapper">
        <Thinkertag
          isLoading={isLoading}
          value={tagStyle || 'default'}
          tagId={String(tagId || '')}
          url={`https://api.thinkertags.com/${String(tagId || '')}`}
          size="small"
        />
      </div>

      <div className="settings-content-wrapper">
        {/* Tag Name */}
        {isLoading ? (
          <div className="input-group">
            <label>Tag Name</label>
            <div style={{ paddingTop: '1.5px' }}>
              <Skeleton height={41.5} borderRadius={3} />
            </div>
          </div>
        ) : (
          <div className="input-group">
            <label>Tag Name</label>
            <Input
              className="input-v2"
              name="customName"
              placeholder="My ThinkerTag #1"
              value={tagName || ''}
              onChange={onNameChange}
              autoComplete="off"
              dataFormType="other"
            />
          </div>
        )}

        <div className="divider">
          <div className="divider-right" />
        </div>

        {/* Public Redirect */}
        {isLoading ? (
          <div className="input-group">
            <label>Redirect</label>
            <div style={{ paddingTop: '1.5px' }}>
              <Skeleton height={41.5} borderRadius={3} />
            </div>
          </div>
        ) : (
          <div className="input-group">
            <label>
              Redirect
              {isPublicUrlLocked && (
                <span style={{ fontSize: '12px', color: '#666', fontWeight: 'normal', marginLeft: '5px' }}>
                  (Linked to the StoreFront)
                </span>
              )}
            </label>
            <Input
              className={`input-v2 ${isPublicUrlLocked ? 'locked-input' : ''}`}
              name="publicUrl"
              placeholder="https://manage.mywebsite.com"
              value={publicUrl || ''}
              type="url"
              enforceProtocol
              onChange={onPublicUrlChange}
              onBlur={onPublicUrlBlur}
              readOnly={isPublicUrlLocked}
              style={isPublicUrlLocked ? { backgroundColor: '#f5f5f5', borderColor: '#ddd', cursor: 'not-allowed' } : {}}
            />
          </div>
        )}
      </div>
    </div>
  );
}