import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import Skeleton from 'react-loading-skeleton';
import { SearchableFilterableList } from '../SearchableFilterableList';

/**
 * ConnectToAsset component - allows connecting a tag to an asset
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.isLoading - Whether the component is in loading state
 * @param {boolean} props.isAssetLinked - Whether the tag is currently linked to an asset
 * @param {Object} props.selectedAsset - The selected asset data (id, name, category)
 * @param {Array} props.assetTags - Array of tag IDs associated with the selected asset
 * @param {Function} props.onToggleAssetLink - Callback for toggling asset connection
 * @param {Function} props.onItemSelect - Callback for selecting an asset
 * @param {Function} props.onAssetClear - Callback for clearing asset selection
 * @param {boolean} props.hasUnsavedChanges - Whether changes need to be saved
 * @param {Function} props.setHasUnsavedChanges - Setter for unsaved changes state
 */
export function ConnectToAsset({ 
  isLoading,
  isAssetLinked,
  selectedAsset,
  assetTags = [],
  onToggleAssetLink,
  onItemSelect,
  onAssetClear,
  hasUnsavedChanges,
  setHasUnsavedChanges
}) {
  const navigate = useNavigate();
  const { tagId: currentTagId } = useParams(); // Get current tag ID from URL params

  // Handle asset link toggle
  const handleAssetLinkToggle = (e) => {
    if (onToggleAssetLink) {
      onToggleAssetLink(e.target.checked);
    }
  };

  if (isLoading) {
    return (
      <div style={{ marginBottom: '15px', marginTop: '7.5px' }}>
        <Skeleton
          height={66}
          borderRadius={10}
          baseColor="#ebebeb"
          highlightColor="#ffffff"
        />
      </div>
    );
  }

  return (
    <div className="settings-input-group-two">
      <div
        className={`toggle-div-collapsable-1-sub-1 ${isAssetLinked ? 'expanded' : ''}`}
      >
        <div className="toggle-text-container-parent">
          <div className="link-primary-icon">
            <img
              src="/link_primary.svg"
              alt="Connect"
              width="16"
              height="16"
            />
          </div>
          <div className="toggle-text-container">
            <div className="settings-input-group-h4">Connect to Asset</div>
            <div className="settings-input-group-h4-sub">
              Manage this tag settings with an Asset
            </div>
          </div>
        </div>

        <label className="switch">
          <input
            type="checkbox"
            checked={isAssetLinked}
            onChange={(e) => {
              handleAssetLinkToggle(e);
              if (setHasUnsavedChanges) {
                setHasUnsavedChanges(true);
              }
            }}
          />
          <span className="slider round" />
        </label>
      </div>

      <div
        className={`asset-collapse-container ${
          isAssetLinked ? 'expanded' : ''
        }`}
      >
        <div
          style={{
            borderTop: '1px solid #E5E7EB',
            margin: '12px 0',
          }}
        />
          {selectedAsset?.id ? (
            <div
              className="selected-group-header"
              onClick={() => navigate(`/assets/${selectedAsset.id}`)}
              style={{ cursor: 'pointer' }}
            >
              <div className="selected-group-header-info">
                <div className="selected-group-header-title">
                  {selectedAsset.name}
                </div>
                {selectedAsset.category && (
                  <div className="selected-group-header-category">
                    {selectedAsset.category}
                  </div>
                )}
              </div>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onAssetClear();
                }}
                className="remove-group-button"
                title="Remove Asset"
              >
                <img
                  src="/remove.svg"
                  alt="Remove Asset"
                  className="remove-group-img"
                />
              </button>
            </div>
          ) : (
            <SearchableFilterableList
              onItemSelect={onItemSelect}
              onClear={onAssetClear}
              placeholder="Select an Asset"
            />
          )}

          {assetTags.length > 0 && (
            <>
              <div className="divider-line" />
              <div className="tags-list-wrapper">
                <div className="tags-list-title">Tags in this Asset</div>
                {assetTags.map((tagId) => (
                  <div
                    key={tagId}
                    className="group-tag-item"
                    onClick={() => navigate(`/tags/${tagId}`)}
                  >
                    <div className="group-tag-id">{tagId}</div>
                    {/* Only show arrow if this is not the current tag */}
                    {tagId !== currentTagId && (
                      <img
                        src="/arrow_right.svg"
                        alt="Arrow Forward"
                        className="back-arrow"
                      />
                    )}
                  </div>
                ))}
              </div>
            </>
          )}
      </div>
    </div>
  );
}