import React, { useState } from 'react';
import '../../styles/ToggleDiv.css'; // Reusing the ToggleDiv CSS

const ButtonDiv = ({ data, onButtonClick }) => {
  const [isExpanded, setIsExpanded] = useState(data.isInitiallyExpanded || false);

  const handleTogglePanel = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className="settings-input-group-two">
      {/* Header section */}
      <div 
        className={`toggle-div-collapsable-1-sub-1 ${isExpanded ? 'expanded' : ''}`} 
        onClick={handleTogglePanel}
      >
        <div className="toggle-text-container-parent">
          <div className="link-primary-icon">
            {data.icon && (
              <img
                src={data.icon}
                alt={data.title}
                width="16"
                height="16"
              />
            )}
          </div>
          <div className="toggle-text-container">
            <div className="toggle-title-text">{data.title}</div>
            <div className="toggle-title-body">{data.description}</div>
          </div>
        </div>
        <div className="collapse-icon-container">
          <div className="collapse-icon">
            <img
              src={isExpanded ? "/expand_up.svg" : "/expand_down.svg"}
              alt={isExpanded ? "Collapse" : "Expand"}
              className="toggle-icon"
              width="20" 
              height="20"
            />
          </div>
        </div>
      </div>

      {/* Collapsible content section */}
      <div className={`location-collapse-container ${isExpanded ? 'expanded' : ''}`}>
        <div
          style={{
            borderTop: '1px solid #E5E7EB',
            margin: '12px 0',
          }}
        />
        
        <div className="panel-content-wrapper" style={{ 
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div className="toggle-text-container">
  <div className="toggle-title-text">{data.subTitle}</div>
  <div className="toggle-title-body" style={{ 
    fontWeight: 400,
    fontSize: '14px',
    color: '#5f5f5f',
    marginLeft: '14px'
  }}>{data.subDescription}</div>
</div>
          <button 
            className="action-button-button-div" 
            onClick={onButtonClick}
            style={{
              backgroundColor: '#e8574c',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              padding: '8px 16px',
              fontWeight: '500',
              cursor: 'pointer',
              fontSize: '14px',
              transition: 'background-color 0.2s'
            }}
          >
            {data.buttonText}
          </button>
        </div>
      </div>
    </div>
  );
};

export { ButtonDiv };