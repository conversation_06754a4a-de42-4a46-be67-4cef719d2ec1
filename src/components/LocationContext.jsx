import React, { createContext, useState, useContext } from 'react';

const LocationContext = createContext();

export const LocationProvider = ({ children }) => {
  const [location, setLocation] = useState({ latitude: null, longitude: null });
  const [isLocationReady, setIsLocationReady] = useState(false);
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);

  return (
    <LocationContext.Provider 
      value={{ 
        location, 
        setLocation, 
        isLocationReady, 
        setIsLocationReady,
        isLoadingLocation,
        setIsLoadingLocation
      }}
    >
      {children}
    </LocationContext.Provider>
  );
};

export const useLocation = () => useContext(LocationContext);