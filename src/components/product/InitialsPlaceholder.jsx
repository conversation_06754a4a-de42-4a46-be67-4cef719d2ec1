import React from 'react';
import PropTypes from 'prop-types';

/**
 * Generates a placeholder with initials on a gradient background
 * The gradient is deterministically generated from the name to ensure consistency
 */
export const InitialsPlaceholder = ({ 
  name = '', 
  size = 'medium', 
  className = '',
  fallbackInitial = 'P'
}) => {
  // Generate gradient based on name
  const getGradient = () => {
    if (!name) return 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
    
    // Simple hash function to generate consistent colors for same name
    const hash = name.split('').reduce((acc, char) => {
      return char.charCodeAt(0) + ((acc << 5) - acc);
    }, 0);
    
    const h1 = Math.abs(hash % 360);
    const h2 = (h1 + 40) % 360;
    
    return `linear-gradient(135deg, hsl(${h1}, 70%, 60%) 0%, hsl(${h2}, 70%, 45%) 100%)`;
  };

  // Extract initials from name
  const getInitials = () => {
    if (!name) return fallbackInitial;
    
    const words = name.trim().split(/\s+/);
    if (words.length === 1) {
      return words[0].charAt(0).toUpperCase();
    }
    
    return (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
  };

  // Size classes for different use cases
  const sizeClasses = {
    small: 'w-8 h-8 text-xs',
    medium: 'w-12 h-12 text-base',
    large: 'w-16 h-16 text-lg',
    xlarge: 'w-20 h-20 text-xl'
  };

  const sizeClass = sizeClasses[size] || sizeClasses.medium;
  
  return (
    <div 
      className={`ph-placeholder-avatar ${sizeClass} ${className}`}
      style={{
        background: getGradient(),
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        fontWeight: 'bold',
        borderRadius: '8px',
        overflow: 'hidden'
      }}
    >
      {getInitials()}
    </div>
  );
};

InitialsPlaceholder.propTypes = {
  name: PropTypes.string,
  size: PropTypes.oneOf(['small', 'medium', 'large', 'xlarge']),
  className: PropTypes.string,
  fallbackInitial: PropTypes.string
};