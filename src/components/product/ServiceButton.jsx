import React, { useState } from 'react';
import FeedbackMessage from '../user/FeedbackMessage';

export function ServiceButton({ 
  buttonConfig,
  productName, 
  serialNumber, 
  onSubmit, 
  isLoading 
}) {
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState({
    userName: '',
    userEmail: '',
    userMessage: ''
  });
  const [errorMessage, setErrorMessage] = useState('');
  const [isButtonHovered, setIsButtonHovered] = useState(false);
  
  // Check if button is properly configured
  const isServiceButtonConfigured = buttonConfig?.buttonEmail && buttonConfig?.buttonText;
  
  const handleButtonClick = () => {
    if (!isServiceButtonConfigured) {
      setErrorMessage('No recipient email configured. Please contact the asset owner.');
      return;
    }

    // Check if contact form should be shown or if we should send directly
    if (buttonConfig.showContactForm === false) {
      // Skip showing form and submit the request directly
      onSubmit({
        userName: 'Anonymous',
        userEmail: '',
        userMessage: 'Service request submitted',
        productName,
        serialNumber
      });
    } else {
      // Show the contact form with animation
      setShowForm(true);
    }
    
    setErrorMessage('');
  };
  
  const handleFormSubmit = (e) => {
    e.preventDefault();
    
    // Validate form
    if (!formData.userName || !formData.userEmail || !formData.userMessage) {
      setErrorMessage('Please fill in all required fields.');
      return;
    }
    
    // Submit form data
    onSubmit({
      ...formData,
      productName,
      serialNumber
    });
    
    // Reset form after submission
    setFormData({
      userName: '',
      userEmail: '',
      userMessage: ''
    });
    
    // Close form with animation
    setShowForm(false);
  };
  
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errorMessage) {
      setErrorMessage('');
    }
  };
  
  const handleCancel = () => {
    // Close form with animation
    setShowForm(false);
    setErrorMessage('');
    setFormData({
      userName: '',
      userEmail: '',
      userMessage: ''
    });
  };
  
  // Service button icon (reusing from existing code)
  const serviceIcon = (
    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M14.25 9.75L9.75 14.25" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M12.2197 5.87988C11.0597 5.67988 9.92969 5.85988 8.89969 6.32988C7.85969 6.80988 7.01969 7.58988 6.46969 8.55988C5.92969 9.52988 5.70969 10.6399 5.84969 11.7499C5.98969 12.8599 6.47969 13.8899 7.24969 14.7099L10.9497 19.1699C11.1397 19.4099 11.3797 19.6099 11.6597 19.7399C11.9297 19.8699 12.2297 19.9399 12.5197 19.9399C12.8197 19.9399 13.1097 19.8699 13.3897 19.7399C13.6597 19.6099 13.8997 19.4099 14.0897 19.1699L17.7897 14.7099C18.5497 13.8999 19.0497 12.8799 19.1997 11.7799C19.3397 10.6799 19.1297 9.55988 18.6097 8.57988C18.0797 7.59988 17.2397 6.80988 16.1997 6.31988C15.1597 5.84988 13.9997 5.66988 12.8697 5.85988" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );
  
  return (
    <div className="ph-service-button-container">
      {errorMessage && <FeedbackMessage message={errorMessage} />}
      
      {/* Button with collapse animation - hide when form is shown */}
      <div className={`location-collapse-container ${!showForm ? 'expanded' : ''}`}>
        <button 
          className="ph-service-button"
          onClick={handleButtonClick}
          onMouseEnter={() => setIsButtonHovered(true)}
          onMouseLeave={() => setIsButtonHovered(false)}
          disabled={isLoading || !isServiceButtonConfigured}
        >
          <span className="ph-button-icon">
            {isLoading ? (
              <div className="ph-button-spinner"></div>
            ) : (
              serviceIcon
            )}
          </span>
          <span className="ph-button-text">
            {isLoading ? 'Sending...' : buttonConfig?.buttonText || 'Request Service'}
          </span>
          {!isLoading && (
            <span className={`ph-button-arrow ${isButtonHovered ? 'ph-visible' : ''}`}>
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3.3335 8H12.6668" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M8 3.33331L12.6667 7.99998L8 12.6666" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </span>
          )}
        </button>
      </div>

      {/* Collapsible form using same animation as other components */}
      <div className={`location-collapse-container ${showForm ? 'expanded' : ''}`}>
        <div className="ph-service-form-container">
          <form onSubmit={handleFormSubmit} className="ph-service-request-form">
            <h3 className="settings-input-group-h4">Request Service</h3>
            
            <div className="input-group">
              <label htmlFor="userName">Your Name</label>
              <input
                id="userName"
                name="userName"
                type="text"
                value={formData.userName}
                onChange={handleInputChange}
                placeholder="Enter your name"
                className="input"
                required
              />
            </div>
            
            <div className="input-group">
              <label htmlFor="userEmail">Your Email</label>
              <input
                id="userEmail"
                name="userEmail"
                type="email"
                value={formData.userEmail}
                onChange={handleInputChange}
                placeholder="Enter your email"
                className="input"
                required
              />
            </div>
            
            <div className="input-group">
              <label htmlFor="userMessage">Message</label>
              <textarea
                id="userMessage"
                name="userMessage"
                value={formData.userMessage}
                onChange={handleInputChange}
                placeholder="Describe what service you need"
                rows={4}
                className="input"
                required
              />
            </div>
            
            <div className="settings-footer">
              <button 
                type="button" 
                onClick={handleCancel}
                className="settings-footer-button secondary-button"
              >
                Cancel
              </button>
              <button 
                type="submit" 
                className="settings-footer-button"
                disabled={isLoading}
              >
                {isLoading ? 'Sending...' : 'Submit Request'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}