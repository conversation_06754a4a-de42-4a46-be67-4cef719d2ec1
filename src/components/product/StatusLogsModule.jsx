import React, { useState } from 'react';
import { ToggleSwitch } from '../ToggleSwitch';
import Skeleton from 'react-loading-skeleton';
import { TagActivity } from '../TagActivity'; // Import TagActivity component

export const StatusLogsModule = ({ 
  data, 
  isLoading, 
  isEditing,
  onStatusChange,
  onDownloadScans,
  onDownloadLogs,
  assetId // Add assetId prop to match AssetPage pattern
}) => {
  const [activeTab, setActiveTab] = useState('Status');
  const [newStatus, setNewStatus] = useState(data?.status || 'Initiated');
  const [isSaving, setIsSaving] = useState(false);
  const [updateMode, setUpdateMode] = useState(false);
  const [isScansExpanded, setIsScansExpanded] = useState(true);
  const [isLogsExpanded, setIsLogsExpanded] = useState(true);
  const [isStatusExpanded, setIsStatusExpanded] = useState(true);

  if (isLoading) {
    return (
      <div className="settings-input-group-two">
        <Skeleton height={40} width={150} style={{ marginBottom: '10px' }} />
        <Skeleton height={20} count={3} width="90%" style={{ marginBottom: '8px' }} />
      </div>
    );
  }
  
  // Find custom status by name
  const getCustomStatus = (statusName) => {
    if (!data.customStatuses || !Array.isArray(data.customStatuses) || !statusName) {
      return null;
    }
    
    // Try exact match
    let status = data.customStatuses.find(s => 
      s.name.toLowerCase() === statusName.toLowerCase()
    );
    
    // Try matching by ID if name match fails
    if (!status) {
      const sanitizedName = statusName.toLowerCase().replace(/\s+/g, '-');
      status = data.customStatuses.find(s => s.id === sanitizedName);
    }
    
    return status;
  };

  // Determine status color based on custom statuses or fallback to default classes
  const getStatusStyle = (statusName) => {
    const customStatus = getCustomStatus(statusName);
    
    if (customStatus) {
      // Return custom styling
      return {
        backgroundColor: `${customStatus.color}20`,
        color: customStatus.color,
        borderColor: `${customStatus.color}40`,
        padding: '6px 12px',
        minWidth: 'auto',
        width: 'fit-content',
        maxWidth: '100%'
      };
    }
    
    // Fallback to default color classes if no custom status found
    switch(statusName) {
      case 'Out of Service':
      case 'Pending Repair':
      case 'Cancelled':
        return { 
          backgroundColor: '#ffebee',
          color: '#e53935',
          borderColor: '#ffcdd2',
          padding: '6px 12px',
          minWidth: 'auto',
          width: 'fit-content',
          maxWidth: '100%'
        };
      case 'In Progress':
      case 'Initiated':
      case 'On Hold':
        return { 
          backgroundColor: '#fff8e1',
          color: '#f57f17',
          borderColor: '#ffecb3',
          padding: '6px 12px',
          minWidth: 'auto',
          width: 'fit-content',
          maxWidth: '100%'
        };
      case 'In Service':
      case 'Completed':
        return { 
          backgroundColor: '#e8f5e9',
          color: '#2e7d32',
          borderColor: '#c8e6c9',
          padding: '6px 12px',
          minWidth: 'auto',
          width: 'fit-content',
          maxWidth: '100%'
        };
      default:
        return { 
          backgroundColor: '#f5f5f5',
          color: '#616161',
          borderColor: '#e0e0e0',
          padding: '6px 12px',
          minWidth: 'auto',
          width: 'fit-content',
          maxWidth: '100%'
        };
    }
  };

  // Get all available status options
  const getStatusOptions = () => {
    if (data.customStatuses && data.customStatuses.length > 0) {
      return data.customStatuses.map(status => status.name);
    }
    
    return [
      'Initiated',
      'In Progress',
      'On Hold',
      'Completed',
      'Cancelled'
    ];
  };

  // Handle status update
  const handleStatusUpdate = () => {
    if (newStatus === data.status || isSaving) return;
    
    setIsSaving(true);
    
    // Call the parent's onStatusChange handler
    onStatusChange(newStatus);
    
    // Reset the UI state after a brief delay to allow animation
    setTimeout(() => {
      setIsSaving(false);
      setUpdateMode(false);
    }, 500);
  };

  // Toggle update mode
  const toggleUpdateMode = (e) => {
    if (e) e.stopPropagation();
    setUpdateMode(!updateMode);
    setNewStatus(data.status || 'Initiated');
  };

  // Toggle scans expansion
  const toggleScansExpand = () => {
    setIsScansExpanded(!isScansExpanded);
  };

  // Toggle logs expansion
  const toggleLogsExpand = () => {
    setIsLogsExpanded(!isLogsExpanded);
  };

  // Toggle status expansion
  const toggleStatusExpand = () => {
    setIsStatusExpanded(!isStatusExpanded);
  };
  
  // Render tab header for all tabs with collapsible behavior
  const renderTabHeader = (title, subtitle, isExpanded, toggleFn, iconSrc = "/storefront_statuses.svg") => (
    <div 
      className={`toggle-div-collapsable-1-sub-1 ${isExpanded ? 'expanded' : ''}`}
      onClick={toggleFn}
      style={{ cursor: 'pointer' }}
    >
      <div className="toggle-text-container-parent">
        <div className="link-primary-icon">
          <img
            src={iconSrc}
            alt={title}
            width="16"
            height="16"
            onError={(e) => {e.target.style.display = 'none'}}
          />
        </div>
        <div className="toggle-text-container">
          <div className="settings-input-group-h4">{title}</div>
          <div className="settings-input-group-h4-sub">{subtitle}</div>
        </div>
      </div>
      <div className="collapse-icon-container">
        <div className="collapse-icon">
          <img
            src={isExpanded ? "/expand_up.svg" : "/expand_down.svg"}
            alt={isExpanded ? "Collapse" : "Expand"}
            className="toggle-icon"
            width="20" 
            height="20"
          />
        </div>
      </div>
    </div>
  );

  return (
    <div className="settings-input-group-two">
      <ToggleSwitch 
        options={['Status', 'Logs', 'Scans']} 
        onChange={setActiveTab}
        activeOption={activeTab}
        specialRounded={['Status', 'Logs', 'Scans']}
      />
      
      {/* Each tab content is rendered based on activeTab state */}
      <div className={`tab-content-container ${activeTab}`}>
        {activeTab === 'Status' && (
          <div className="tab-section-content">
            {/* Status tab header with expand/collapse - matching other tabs */}
            {renderTabHeader("Current Status", "Manage Asset status", isStatusExpanded, toggleStatusExpand)}
            
            {/* Collapsible content container using same structure as NotesModule */}
            <div className={`location-collapse-container ${isStatusExpanded ? 'expanded' : ''}`}>
              <div
                style={{
                  borderTop: '1px solid #E5E7EB',
                  margin: '12px 0', // Matches NotesModule
                }}
              />
              
              <div className="settings-section-content">
                {/* Status indicator with edit button positioned on the right */}
                <div className="status-display-container" style={{ 
                  display: 'flex', 
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  position: 'relative',
                  padding: '4px 0 12px' 
                }}>
                  <div 
                    className="status-indicator" 
                    style={getStatusStyle(data.status)}
                  >
                    {data.status || 'Initiated'}
                  </div>
                  
                  {/* Round edit button */}
                  {!updateMode && !isEditing && (
                    <button 
                      className="status-edit-button"
                      onClick={toggleUpdateMode}
                      aria-label="Edit status"
                      title="Edit status"
                      style={{
                        width: '32px',
                        height: '32px',
                        borderRadius: '50%',
                        backgroundColor: '#F3F4F6',
                        border: '1px solid #E5E7EB',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        cursor: 'pointer',
                        padding: 0,
                        marginRight: '8px',
                        boxShadow: '0 1px 2px rgba(0,0,0,0.05)'
                      }}
                    >
                      <img 
                        src="/pencil-edit.svg" 
                        alt="Edit" 
                        width="16" 
                        height="16" 
                      />
                    </button>
                  )}
                </div>
                
                {/* Edit form - appears when update mode is active */}
                <div 
                  className="edit-mode-container"
                  style={{
                    height: updateMode && !isEditing ? 'auto' : '0',
                    overflow: 'hidden',
                    opacity: updateMode && !isEditing ? 1 : 0,
                    transition: 'all 0.3s ease-in-out',
                    marginTop: updateMode && !isEditing ? '12px' : '0'
                  }}
                >
                  {updateMode && !isEditing && (
                    <div className="status-container">
                      <div
                        style={{
                          borderTop: '1px solid #E5E7EB',
                          margin: '12px 0', // Matches NotesModule
                        }}
                      />
                      
                      <div className="status-update-controls">
                        <div className="select-wrapper" style={{ marginBottom: '16px', position: 'relative' }}>
                          <select
                            value={newStatus}
                            onChange={(e) => setNewStatus(e.target.value)}
                            className="input"
                            disabled={isSaving}
                            style={{
                              appearance: 'none',
                              WebkitAppearance: 'none',
                              MozAppearance: 'none',
                              paddingRight: '35px',
                              backgroundImage: 'none',
                              width: '100%'
                            }}
                          >
                            {getStatusOptions().map(option => (
                              <option key={option} value={option}>{option}</option>
                            ))}
                          </select>
                          <img 
                            src="/expand_down.svg"
                            alt=""
                            style={{
                              position: 'absolute',
                              right: '8px',
                              top: '35%',
                              transform: 'translateY(-50%)',
                              pointerEvents: 'none',
                              width: '24px',
                              height: '24px'
                            }}
                            aria-hidden="true"
                          />
                        </div>
                        
                        <div className="status-action-buttons">
                          <button
                            className="status-action-button confirm"
                            onClick={handleStatusUpdate}
                            disabled={newStatus === data.status || isSaving}
                            aria-label="Confirm status update"
                            title="Confirm"
                          >
                            {isSaving ? (
                              <span className="loading-spinner"></span>
                            ) : (
                              <span className="button-text">Confirm</span>
                            )}
                          </button>
                          <button
                            className="status-action-button cancel"
                            onClick={toggleUpdateMode}
                            disabled={isSaving}
                            aria-label="Cancel status update"
                            title="Cancel"
                          >
                            <span className="button-text">Cancel</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                
                {/* Edit mode for the whole component */}
                {isEditing && (
                  <div className="select-wrapper" style={{ margin: '12px 0', position: 'relative' }}>
                    <select 
                      className="input"
                      value={data.status || 'Initiated'}
                      onChange={(e) => onStatusChange(e.target.value)}
                      style={{
                        appearance: 'none',
                        WebkitAppearance: 'none',
                        MozAppearance: 'none',
                        paddingRight: '35px',
                        backgroundImage: 'none',
                        width: '100%'
                      }}
                    >
                      {data.customStatuses && data.customStatuses.length > 0 ? (
                        data.customStatuses.map(status => (
                          <option key={status.id} value={status.name}>{status.name}</option>
                        ))
                      ) : (
                        <>
                          <option value="Initiated">Initiated</option>
                          <option value="In Progress">In Progress</option>
                          <option value="On Hold">On Hold</option>
                          <option value="Completed">Completed</option>
                          <option value="Cancelled">Cancelled</option>
                        </>
                      )}
                    </select>
                    <img 
                      src="/expand_down.svg"
                      alt=""
                      style={{
                        position: 'absolute',
                        right: '8px',
                        top: '54%',
                        transform: 'translateY(-50%)',
                        pointerEvents: 'none',
                        width: '24px',
                        height: '24px'
                      }}
                      aria-hidden="true"
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
        
        {activeTab === 'Logs' && (
          <div className="tab-section-content">
            {/* Logs tab header with expand/collapse */}
            {renderTabHeader("Status History", "View all status changes", isLogsExpanded, toggleLogsExpand)}
            
            {/* Collapsible content container using same structure as NotesModule */}
            <div className={`location-collapse-container ${isLogsExpanded ? 'expanded' : ''}`}>
              <div
                style={{
                  borderTop: '1px solid #E5E7EB',
                  margin: '12px 0', // Matches NotesModule
                }}
              />
              
              <div className="settings-section-content">
                {data.logs && data.logs.length > 0 ? (
                  <>
                    {/* Constrained container for logs with scroll - matching Scans tab */}
                    <div style={{
                      maxHeight: '400px', // Cap the height like Scans
                      overflowY: 'auto', // Enable vertical scrolling
                      marginBottom: '16px', // Space before download button
                      border: '1px solid #e5e7eb', // Subtle border to define the area
                      borderRadius: '6px',
                      padding: '12px' // Internal padding
                    }}>
                      {data.logs
                        .slice() // Create a copy to avoid mutating the original array
                        .reverse() // Reverse to show newest first
                        .map((log, index) => (
                        <div key={index} className="detail-row" style={{
                          display: 'flex',
                          alignItems: 'center',
                          padding: '12px 0',
                          borderBottom: index < data.logs.length - 1 ? '1px solid #f3f4f6' : 'none',
                          gap: '12px'
                        }}>
                          <div style={{ 
                            minWidth: '140px',
                            fontSize: '13px',
                            color: '#6b7280',
                            fontWeight: '500'
                          }}>
                            {formatDateTime(log.timestamp)}
                          </div>
                          <span 
                            className="status-indicator" 
                            style={{
                              ...getStatusStyle(log.status),
                              fontSize: '12px',
                              fontWeight: '600',
                              borderRadius: '4px',
                              border: '1px solid',
                              borderColor: getStatusStyle(log.status).borderColor
                            }}
                          >
                            {log.status}
                          </span>
                        </div>
                      ))}
                    </div>
                    
                    {onDownloadLogs && (
                      <button 
                        className="settings-footer-button"
                        onClick={onDownloadLogs}
                        style={{ marginTop: '0' }} // Remove extra margin since we have marginBottom above
                      >
                        Download as Excel
                      </button>
                    )}
                  </>
                ) : (
                  <div className="empty-state">No status history available</div>
                )}
              </div>
            </div>
          </div>
        )}
        
        {activeTab === 'Scans' && (
          <div className="tab-section-content">
            {/* Scans tab header with expand/collapse */}
            {renderTabHeader("Scan History", "View all Asset scans", isScansExpanded, toggleScansExpand)}
            
            {/* Collapsible content container using same structure as NotesModule */}
            <div className={`location-collapse-container ${isScansExpanded ? 'expanded' : ''}`}>
              <div
                style={{
                  borderTop: '1px solid #E5E7EB',
                  margin: '12px 0', // Matches NotesModule
                }}
              />
              
              <div className="settings-section-content">
                {isLoading ? (
                  <div className="loading-scans">
                    <Skeleton height={20} count={3} style={{ marginBottom: '5px' }} />
                  </div>
                ) : (
                  <>
                    {/* Constrained container for TagActivity with scroll */}
                    <div style={{
                      maxHeight: '400px', // Cap the height
                      overflowY: 'auto', // Enable vertical scrolling
                      marginBottom: '16px', // Space before download button
                      border: '1px solid #e5e7eb', // Subtle border to define the area
                      borderRadius: '6px',
                      padding: '12px' // Internal padding
                    }}>
                      {assetId ? (
                        <TagActivity assetId={assetId} />
                      ) : (
                        <div className="empty-state">No asset ID available for scan history</div>
                      )}
                    </div>
                    
                    {onDownloadScans && (
                      <button 
                        className="settings-footer-button"
                        onClick={onDownloadScans}
                        style={{ marginTop: '0' }} // Remove extra margin since we have marginBottom above
                      >
                        Download as Excel
                      </button>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Helper function to format date and time
const formatDateTime = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
};