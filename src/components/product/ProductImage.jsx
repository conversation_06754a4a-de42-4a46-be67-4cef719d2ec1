import React, { useState, useEffect } from 'react';
import Skeleton from 'react-loading-skeleton';
import { InitialsPlaceholder } from './InitialsPlaceholder';

export const ProductImage = ({ 
  imageUrl, 
  name, 
  isEditing, 
  onImageUpload, 
  imageEnabled = true,
  isLoading = false,
  size // New prop to control size (e.g. '86px' for QR code match)
}) => {
  // Track image loading state
  const [imageStatus, setImageStatus] = useState({
    isLoading: Boolean(imageUrl),
    hasError: false
  });

  // Reset image loading state when image URL changes
  useEffect(() => {
    if (imageUrl) {
      setImageStatus({ isLoading: true, hasError: false });
    }
  }, [imageUrl]);

  const handleImageChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      setImageStatus({ isLoading: true, hasError: false });
      onImageUpload(e.target.files[0]);
    }
  };

  const handleImageError = () => {
    setImageStatus({ isLoading: false, hasError: true });
  };

  const handleImageLoad = () => {
    setImageStatus({ isLoading: false, hasError: false });
  };

  // Calculate styles for size control
  const sizeStyle = size ? {
    width: size,
    height: size,
    minWidth: size,
    minHeight: size
  } : {};

  if (isLoading) {
    return (
      <div className="ph-image-skeleton" style={sizeStyle}>
        <Skeleton width="100%" height="100%" borderRadius={8} />
      </div>
    );
  }

  if (!imageEnabled) {
    return null;
  }

  return (
    <div className="ph-image-card" style={sizeStyle}>
      {imageUrl ? (
        <div className={`ph-image-wrapper ${imageStatus.isLoading ? 'ph-loading' : imageStatus.hasError ? 'ph-error' : 'ph-loaded'}`}>
          {/* Show loading spinner when image is loading */}
          {imageStatus.isLoading && (
            <div className="ph-loading-indicator">
              <div className="ph-spinner"></div>
            </div>
          )}
          
          {/* Actual image */}
          <img 
            src={imageUrl} 
            alt={name || 'Product'} 
            className="ph-image" 
            onError={handleImageError}
            onLoad={handleImageLoad}
          />
          
          {/* Show placeholder if image has error */}
          {imageStatus.hasError && (
            <InitialsPlaceholder 
              name={name} 
              size={size ? "medium" : "large"} 
              className="ph-placeholder-avatar" 
            />
          )}
          
          {/* Edit overlay for editing mode */}
          {isEditing && (
            <div className="ph-upload-overlay">
              <label htmlFor="ph-image-upload" className="ph-upload-button">
                <img 
                  src={imageStatus.hasError || !imageUrl ? "/upload.svg" : "/edit.svg"} 
                  alt={imageStatus.hasError || !imageUrl ? "Upload" : "Edit"} 
                  width="16" 
                  height="16" 
                  onError={(e) => {e.target.style.display = 'none'}}
                />
                <span>
                  {imageStatus.hasError || !imageUrl ? "Upload Image" : "Change Image"}
                </span>
                <input
                  id="ph-image-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleImageChange}
                  style={{ display: 'none' }}
                />
              </label>
            </div>
          )}
        </div>
      ) : (
        <div className="ph-image-wrapper ph-placeholder">
          <InitialsPlaceholder 
            name={name} 
            size={size ? "medium" : "large"} 
            className="ph-placeholder-avatar" 
          />
          
          {isEditing && (
            <div className="ph-upload-overlay">
              <label htmlFor="ph-image-upload" className="ph-upload-button">
                <img 
                  src="/upload.svg" 
                  alt="Upload" 
                  width="16" 
                  height="16" 
                  onError={(e) => {e.target.style.display = 'none'}}
                />
                <span>Upload Image</span>
                <input
                  id="ph-image-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleImageChange}
                  style={{ display: 'none' }}
                />
              </label>
            </div>
          )}
        </div>
      )}
    </div>
  );
};