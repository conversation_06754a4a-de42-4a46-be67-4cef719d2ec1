import React, { useState, useMemo, useRef, useCallback, useEffect } from 'react';
import { MapPin, X } from 'lucide-react';
import '../styles/SearchableFilterableList.css';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import { Auth, API } from 'aws-amplify';


const SearchIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" width="24" height="24">
    <path strokeLinecap="round" strokeLinejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
  </svg>
);

const ChevronUpDownIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" width="24" height="24">
    <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 15 12 18.75 15.75 15m-7.5-6L12 5.25 15.75 9" />
  </svg>
);

const RemoveIcon = () => (
  <img src="/remove.svg" alt="Remove" width="16" height="16" />
);

const LinkPrimaryIcon = () => (
  <img src="/link_primary.svg" alt="Remove" width="16" height="16" />
);

const LinkSecondaryIcon = () => (
  <img src="/link_secondary.svg" alt="Remove" width="16" height="16" />
);


const SkeletonLoader = () => (
  <>
    {[1, 2, 3].map((item) => (
      <div key={item} className="searchable-list-item" style={{ height: '39px' }}>
        <Skeleton width={160} height={16} />
        <Skeleton width={120} height={13} style={{ marginTop: '2px' }} />
      </div>
    ))}
  </>
);

const SearchableFilterableList = ({
  onItemSelect,
  onClear,
  placeholder = 'Select a collection',
  filters = ['All', 'Empty Assets'],
  initialSelectedItem = null,
  radius = 30 // Default radius in km
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilter, setActiveFilter] = useState('All');
  const [selectedItem, setSelectedItem] = useState(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLocationEnabled, setIsLocationEnabled] = useState(false);
  const [userLocation, setUserLocation] = useState(null);
  const [isLocating, setIsLocating] = useState(false);

  const searchTimeout = useRef(null);

  // Get user's location
// Improved getUserLocation with timeout and better error handling
const getUserLocation = () => {
  if (!navigator.geolocation) {
    console.error('Geolocation is not supported by your browser');
    setIsLocating(false);
    setIsLocationEnabled(false);
    return;
  }

  setIsLocating(true);
  
  // Add timeout options for geolocation
  const options = {
    timeout: 10000,
    maximumAge: 0,
    enableHighAccuracy: false
  };

  const locationPromise = new Promise((resolve, reject) => {
    navigator.geolocation.getCurrentPosition(resolve, reject, options);
  });

  // Race against a timeout
  Promise.race([
    locationPromise,
    new Promise((_, reject) => setTimeout(() => reject(new Error('Location timeout')), 10000))
  ])
    .then((position) => {
      setUserLocation({
        latitude: position.coords.latitude,
        longitude: position.coords.longitude
      });
      setIsLocating(false);
      setIsLocationEnabled(true);
    })
    .catch((error) => {
      console.error('Error getting location:', error);
      setIsLocating(false);
      setIsLocationEnabled(false);
      // Reset user location to ensure clean state
      setUserLocation(null);
    });
};

  // Toggle location filter
  const handleLocationToggle = () => {
    if (!isLocationEnabled) {
      getUserLocation();
    } else {
      setIsLocationEnabled(false);
    }
  };

  // Build API query parameters
  const buildQueryParams = (searchTerm = '') => {
    const params = new URLSearchParams();

    if (searchTerm) {
      params.append('search', searchTerm);
    }

    if (isLocationEnabled && userLocation) {
      params.append('latitude', userLocation.latitude);
      params.append('longitude', userLocation.longitude);
      params.append('radius', radius);
    }

    return params.toString();
  };

  // Fetch assets from API with combined parameters
  const fetchAssets = async (searchTerm = '') => {
    setIsLoading(true);
    try {
      const session = await Auth.currentSession();
      const queryParams = buildQueryParams(searchTerm);
      const url = `/assets${queryParams ? `?${queryParams}` : ''}`;

      const response = await API.get("api", url, {
        headers: {
          Authorization: `Bearer ${session.getAccessToken().getJwtToken()}`,
          'id-Token': session.getIdToken().getJwtToken()
        }
      });

      if (response && response.items) {
        const results = response.items.map(item => ({
          id: item.id,
          name: item.name,
          category: item.description || 'Uncategorized',
          distance: item.distance // Added distance from API response
        }));
        setSearchResults(results);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchInitialAssets = async () => {
    setIsLoading(true);
    try {
      // Use the same fetchAssets function but with no search term
      await fetchAssets('');
    } catch (error) {
      console.error('Initial load error:', error);
      setSearchResults([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Update search when location is enabled and coordinates are received
  useEffect(() => {
    if (isLocationEnabled && userLocation && !isLocating) {
      // Clear any existing timeout
      if (searchTimeout.current) {
        clearTimeout(searchTimeout.current);
      }

      // Fetch results with current search term (if any) and new location
      fetchAssets(searchTerm);
    }
  }, [isLocationEnabled, userLocation, isLocating]);

  // When location is disabled, refresh results without location params
  useEffect(() => {
    if (!isLocationEnabled && isExpanded) {
      fetchAssets(searchTerm);
    }
  }, [isLocationEnabled]);

  // Modify the existing handleSearchChange
  const handleSearchChange = useCallback(async (event) => {
    const value = event.target.value;
    setSearchTerm(value);

    if (searchTimeout.current) {
      clearTimeout(searchTimeout.current);
    }

    if (value.length > 1) {
      setIsLoading(true);
      searchTimeout.current = setTimeout(() => {
        fetchAssets(value);
      }, 300);
    } else {
      setSearchResults([]);
      setIsLoading(false);
    }
  }, [isLocationEnabled, userLocation]);

  // Trigger initial assets fetch whenever expanded
  useEffect(() => {
    if (isExpanded) {
      fetchInitialAssets();
    }
  }, [isExpanded]);

  // Effect to update selectedItem if initialSelectedItem changes
  useEffect(() => {
    if (initialSelectedItem) {
      setSelectedItem(initialSelectedItem);
    }
  }, [initialSelectedItem]);

  // Filtered and searched items (API search)
  const filteredItems = useMemo(() => {
    return searchResults.filter(item => {
      const matchesFilter = activeFilter === 'All' || item.category === activeFilter;
      return matchesFilter;
    });
  }, [activeFilter, searchResults]);



  // Fetch assets from API
  const fetchAssetsBySearch = async (term) => {
    try {
      const session = await Auth.currentSession();
      const response = await API.get("api", `/assets?search=${term}`, {
        headers: {
          Authorization: `Bearer ${session.getAccessToken().getJwtToken()}`,
          'id-Token': session.getIdToken().getJwtToken()
        }
      });

      if (response && response.items) {
        const results = response.items.map(item => ({
          id: item.id,
          name: item.name,
          category: item.description || 'Uncategorized',
        }));
        setSearchResults(results);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle item selection
  const handleItemSelect = (item) => {
    setSelectedItem(item);
    setIsExpanded(false);
    onItemSelect(item);
  };

  // Clear selected item and search
  const handleClear = () => {
    setSearchTerm('');
    setSelectedItem(null);
    setIsExpanded(false);
    setSearchResults([]);
    if (onClear) {  // Call the callback if provided
      onClear();
    }
  };

  // Handle initial expand and load
  const handleInitialExpand = () => {
    setIsExpanded(true);
  };


  // Modified item rendering to include distance when available
  const renderItem = (item) => (
    <div
      key={item.id}
      onClick={() => handleItemSelect(item)}
      className={`searchable-list-item ${selectedItem?.id === item.id ? 'selected' : ''}`}
    >

      <span className='searchable-list-item-text'>{item.name}</span>
      <div className='searchable-list-item-details'>
        <span className='searchable-list-item-category-text'>{item.category}</span>
        {item.distance && (
          <span className='searchable-list-item-distance'>
            {item.distance.toFixed(1)}km away
          </span>
        )}
      </div>
    </div>
  );


  return (
    <div className="searchable-filterable-list-container">
      {isExpanded ? (
        <>
          <div className="searchable-list-input-container">
            <span className="searchable-list-search-icon">
              <SearchIcon />
            </span>
            <input
              type="text"
              placeholder="Search assets..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="searchable-list-input"
            />
  
            {searchTerm && (
              <div className="searchable-list-selected-actions-2">
                <button
                  onClick={handleClear}
                  className="searchable-list-clear-button"
                >
                  <X size={18} />
                </button>
              </div>
            )}
          </div>
  
          <div className="searchable-list-filters">
            {filters.map(filter => (
              <button
                key={filter}
                onClick={() => setActiveFilter(filter)}
                className={`searchable-list-filter-button ${activeFilter === filter ? 'active' : ''}`}
              >
                {filter}
              </button>
            ))}
            <div className="searchable-list-filters-spacer" />
            <button
              onClick={handleLocationToggle}
              className={`searchable-list-filter-button location-button ${isLocationEnabled ? 'active' : ''}`}
              disabled={isLocating}
            >
              <MapPin size={16} className="location-icon" />
              {isLocating ? 'Getting location...' : 'Nearby'}
            </button>
          </div>
  
          <div className="searchable-list-items">
            {isLoading ? (
              <SkeletonLoader />
            ) : filteredItems.length > 0 ? (
              filteredItems.map(renderItem)
            ) : (
              <div className="searchable-list-no-results">
                No items found
              </div>
            )}
          </div>
        </>
      ) : (
        <div onClick={handleInitialExpand}>
          {selectedItem ? (
            <div className="searchable-list-collapsed-selected">
              <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
                <div className='link-primary-icon'>
                  <LinkPrimaryIcon />
                </div>
                <div className='searchable-list-placeholder-selected'>
                  <span className='searchable-list-item-text'>{selectedItem.name}</span>
                  <span className='searchable-list-item-category-text'>{' ' + selectedItem.category}</span>
                </div>
              </div>
              <div className="searchable-list-selected-actions">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleClear();
                  }}
                  className="searchable-list-clear-button"
                >
                  <RemoveIcon />
                </button>
              </div>
            </div>
          ) : (
            <div className="searchable-list-collapsed-empty">
              <div className="searchable-list-placeholder-pseudo-input">
                <div style={{ display: 'flex', alignItems: 'center', gap: '15px', justifyContent: 'space-between', height: '100%' }}>
                  <div className="searchable-list-placeholder">
                    <span>{placeholder}</span>
                  </div>
                  <span className="searchable-list-chevron">
                    <ChevronUpDownIcon />
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export { SearchableFilterableList };