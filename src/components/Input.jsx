import React, { useState, useRef } from 'react';
import '../styles/Input.css';

const Input = ({
  value,
  onChange,
  onBlur,
  placeholder,
  className,
  name,
  disabled = false,
  readOnly = false,
  type = 'text',
  inputMode,
  autoCapitalize = 'sentences',
  autoComplete = 'off',
  autoCorrect = 'on',
  spellCheck = true,
  dataFormType,
  enforceProtocol = false,
  maintainFocusOnClear = false,
  isEmail = false // New prop to easily identify email fields
}) => {
  const inputRef = useRef(null);
  const [isUserDeletingProtocol, setIsUserDeletingProtocol] = useState(false);
  const [wasFocused, setWasFocused] = useState(false);

  // Apply email-specific optimizations if isEmail is true
  if (isEmail) {
    autoCapitalize = 'none';
    autoCorrect = 'off';
    spellCheck = false;
    // Keep type as 'text' but set inputMode if not already set
    inputMode = inputMode || 'email';
  }

  const handleFocus = (event) => {
    setWasFocused(true);
    if (type === 'url' && !event.target.value && enforceProtocol) {
      onChange({ target: { value: 'https://', name } });
    }
  };

  const handleChange = (event) => {
    const newValue = event.target.value;

    if (type === 'url' && enforceProtocol) {
      if (value.startsWith('https://') && !newValue.startsWith('https://')) {
        setIsUserDeletingProtocol(true);
      } else if (newValue.includes('://')) {
        setIsUserDeletingProtocol(false);
      }
    }
    
    onChange(event);
  };

  const handleBlur = (event) => {
    setWasFocused(false);
    setIsUserDeletingProtocol(false);
    if (onBlur) {
      onBlur(event);
    }
  };

  const handleClear = (event) => {
    event.preventDefault();
    const shouldMaintainFocus = maintainFocusOnClear || wasFocused;
    onChange({ target: { value: '', name } });
    if (shouldMaintainFocus) {
      inputRef.current?.focus();
    }
  };

  return (
    <div className="email-input-container">
      <input
        ref={inputRef}
        type={type}
        className={className}
        name={name}
        placeholder={placeholder}
        value={value}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        disabled={disabled}
        readOnly={readOnly}
        autoComplete={autoComplete}
        autoCapitalize={autoCapitalize}
        autoCorrect={autoCorrect}
        spellCheck={spellCheck}
        inputMode={inputMode}
        data-form-type={dataFormType}
        aria-label={placeholder || name}
      />
      {value && !readOnly && (
        <button
          type="button"
          className="clear-input-button"
          onMouseDown={handleClear}
          aria-label="Clear input"
        >
          ×
        </button>
      )}
    </div>
  );
};

// Convenient shorthand for email inputs
const EmailInput = (props) => {
  return <Input isEmail={true} {...props} />;
};

export { Input, EmailInput };