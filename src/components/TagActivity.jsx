import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Auth, API } from 'aws-amplify';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import '../styles/TagActivity.css';

// Updated cache structure to handle both tag and asset events
const eventCache = {
  data: null,
  lastFetchTime: null,
  id: null,
  type: null, // 'tag' or 'asset'
};

const CACHE_DURATION = 5 * 60 * 1000;

const LoadingSkeleton = () => (
  <div className="date-group">
    <Skeleton width={80} height={20} style={{ marginBottom: '0.4rem', marginLeft: '0.5rem' }} />
    <div className="activity-item">
      <div className="activity-header">
        <Skeleton width={100} height={20} style={{ marginRight: '10px' }} />
        <Skeleton width={60} height={20} style={{}} />
        <Skeleton width={60} height={16} style={{ marginLeft: 'auto' }} />
      </div>
      <div className="activity-details">
        <div className="detail-row">
          <Skeleton width={180} height={12} style={{ marginRight: '0.5rem' }} />
        </div>
        <div className="detail-row">
          <Skeleton width={150} height={12} style={{ marginRight: '0.5rem' }} />
        </div>
        <div className="detail-row">
          <Skeleton width={120} height={12} style={{ marginRight: '0.5rem' }} />
        </div>
        <div className="detail-row">
          <div className="url-container">
            <Skeleton width={200} height={12} />
          </div>
        </div>
      </div>
    </div>
  </div>
);

const TagActivity = ({ tagId = null, assetId = null }) => {
  // Determine the type of ID (tag or asset) and the actual ID value
  const idType = tagId ? 'tag' : 'asset';
  const id = tagId || assetId;
  
  // Check if the current request matches the cached data
  const isCachedDataValid = eventCache.id === id && eventCache.type === idType;
  
  const [events, setEvents] = useState(() =>
    isCachedDataValid ? eventCache.data : []
  );
  const [loading, setLoading] = useState(!isCachedDataValid || !eventCache.data);
  const [error, setError] = useState(null);
  const isMounted = useRef(true);

  const fetchEvents = async () => {
    try {
      const session = await Auth.currentSession();
      const headers = {
        Authorization: `Bearer ${session.getAccessToken().getJwtToken()}`,
        'id-Token': session.getIdToken().getJwtToken(),
      };

      // Choose the correct API endpoint based on whether tagId or assetId is provided
      const endpoint = tagId 
        ? `/events/${tagId}` 
        : `/assets/${assetId}/tag-scans`;

      const response = await API.get('api', endpoint, { headers });

      if (isMounted.current) {
        // Process response based on the endpoint structure
        // Fixed: Check for both items and scans in the response
        const items = response?.items || response?.scans || [];
        setEvents(items);
        
        // Update cache with new data
        eventCache.data = items;
        eventCache.lastFetchTime = Date.now();
        eventCache.id = id;
        eventCache.type = idType;
        
        setLoading(false);
      }
    } catch (err) {
      if (isMounted.current) {
        setError('Failed to load activity data');
        console.error(`Error fetching ${idType} events:`, err);
        setLoading(false);
      }
    }
  };

  const truncateUrl = (url) => {
    if (!url) return '';
    if (url.length <= 50) return url; // Adjust threshold as needed
    
    const start = url.substring(0, 25);
    const end = url.substring(url.length - 25);
    return `${start}...${end}`;
  };

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleString('en-US', {
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
    });
  };

  const getDateGroup = (timestamp) => {
    const date = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
      });
    }
  };

  const formatMessage = useMemo(
    () => (urlType, accessType) => ({
      scanType: urlType.includes('Public scan') || accessType === 'public'
        ? 'Public Tag Scan'
        : 'Tag Scan',
      accessStatus: urlType.includes('Public scan') || accessType === 'public'
        ? null
        : 'Logged In'
    }),
    []
  );

  const groupedEvents = useMemo(() => {
    if (!events.length) return {};
    
    const groups = events.reduce((acc, event) => {
      const dateGroup = getDateGroup(event.timestamp);
      if (!acc[dateGroup]) {
        acc[dateGroup] = [];
      }
      acc[dateGroup].push(event);
      return acc;
    }, {});

    Object.keys(groups).forEach(key => {
      groups[key].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    });

    return groups;
  }, [events]);

  useEffect(() => {
    isMounted.current = true;

    // Check if we need to fetch new data
    const shouldFetch =
      !eventCache.lastFetchTime ||
      eventCache.id !== id ||
      eventCache.type !== idType ||
      Date.now() - eventCache.lastFetchTime > CACHE_DURATION;

    if (shouldFetch) {
      fetchEvents();
    } else {
      setLoading(false);
      setEvents(eventCache.data);
    }

    return () => {
      isMounted.current = false;
    };
  }, [tagId, assetId, id, idType]);

  if (loading)
    return (
      <div className="tag-activity">
        {[1, 2, 3].map((i) => (
          <LoadingSkeleton key={i} />
        ))}
      </div>
    );

  if (error)
    return (
      <div className="tag-activity">
        <div className="error-container">{error}</div>
      </div>
    );

  if (events.length === 0)
    return (
      <div className="tag-activity">
        <div className="empty-state">No activity recorded yet</div>
      </div>
    );

  return (
    <>
      {Object.entries(groupedEvents).map(([dateGroup, dateEvents]) => (
        <div key={dateGroup} className="date-group">
          <h2 className="date-header">{dateGroup}</h2>
          {dateEvents.map((event, index) => (
            <div key={`${dateGroup}-${event.id || index}`} className="activity-item">
              <div className="activity-header">
                <div className="activity-access-type">
                  {formatMessage(event.urlType, event.accessType).scanType}
                  {/* Display tag ID if viewing asset activity */}
                  {assetId && event.tagId && (
                    <span className="tag-id-label"> - Tag ID: {event.tagId}</span>
                  )}
                </div>
                {formatMessage(event.urlType, event.accessType).accessStatus && (
                  <div className="activity-status">
                    {formatMessage(event.urlType, event.accessType).accessStatus}
                  </div>
                )}
                <div className="activity-timestamp">
                  {formatTime(event.timestamp)}
                </div>
              </div>
              <div className="activity-details">
                <div className="detail-row">
                  <span className="detail-label">IP:</span>
                  <span>{event.ipAddress}</span>
                </div>
                <div className="detail-row">
                  <span className="detail-label">Browser:</span>
                  <span>{event.browser} on {event.platform}</span>
                </div>
                <div className="detail-row">
                  <span className="detail-label">User:</span>
                  <span>{event.userEmail}</span>
                </div>
                <div className="detail-row">
                  <span className="detail-label">URL:</span>
                  <div className="url-container">
                    <span className="url-text">{truncateUrl(event.returnedUrl)}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ))}
    </>
  );
};

export { TagActivity };