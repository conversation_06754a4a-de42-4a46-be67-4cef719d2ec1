import React from 'react';
import { ProductImage } from '../product/ProductImage';
import { Input } from '../Input';
import Skeleton from 'react-loading-skeleton';

export function AssetPreviewPanel({
  isLoading = false,
  imageUrl = '',
  assetId = '',
  assetName = '',
  publicUrl = '',
  isPublicUrlLocked = false,
  onNameChange = () => {},
  onPublicUrlChange = () => {},
  onPublicUrlBlur = () => {},
  onImageUpload = () => {},
  isEditing = true,
  imageEnabled = true,
  showUploadButton = true,
  useRealImage = false
}) {
  return (
    <div className="thinkertag-preview-panel">
      <div className="thinkertag-preview-qr-wrapper">
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {isLoading ? (
            <div className="ph-image-skeleton">
              <Skeleton width="86px" height="86px" borderRadius={8} />
            </div>
          ) : (
            <div className="qr-code-style-wrapper qr-no-shadow">
              <ProductImage
                imageUrl={(useRealImage && imageEnabled) ? imageUrl : ''}
                name={assetName}
                isEditing={isEditing && showUploadButton}
                onImageUpload={onImageUpload}
                imageEnabled={true}
                size="86px"
              />
            </div>
          )}
        </div>
      </div>

      <div className="settings-content-wrapper">
        {/* Asset Name */}
        {isLoading ? (
          <div className="input-group">
            <label>Asset Name</label>
            <div style={{ paddingTop: '1.5px' }}>
              <Skeleton height={41.5} borderRadius={3} />
            </div>
          </div>
        ) : (
          <div className="input-group">
            <label>Asset Name</label>
            <Input
              className="input-v2"
              name="name"
              placeholder="My Asset"
              value={assetName || ''}
              onChange={onNameChange}
              autoComplete="off"
              dataFormType="other"
            />
          </div>
        )}

        <div className="divider">
          <div className="divider-right" />
        </div>

        {/* Public Redirect */}
        {isLoading ? (
          <div className="input-group">
            <label>Redirect</label>
            <div style={{ paddingTop: '1.5px' }}>
              <Skeleton height={41.5} borderRadius={3} />
            </div>
          </div>
        ) : (
          <div className="input-group">
            <label>
              Redirect
              {isPublicUrlLocked && (
                <span style={{ fontSize: '12px', color: '#666', fontWeight: 'normal', marginLeft: '5px' }}>
                  (Linked to the StoreFront)
                </span>
              )}
            </label>
            <Input
              className={`input-v2 ${isPublicUrlLocked ? 'locked-input' : ''}`}
              name="publicAction"
              placeholder="https://manage.mywebsite.com"
              value={publicUrl || ''}
              type="url"
              enforceProtocol
              onChange={onPublicUrlChange}
              onBlur={onPublicUrlBlur}
              readOnly={isPublicUrlLocked}
              style={isPublicUrlLocked ? { backgroundColor: '#f5f5f5', borderColor: '#ddd', cursor: 'not-allowed' } : {}}
            />
          </div>
        )}
      </div>
    </div>
  );
}