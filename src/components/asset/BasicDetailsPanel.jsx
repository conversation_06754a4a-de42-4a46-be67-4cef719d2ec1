import React from 'react';

export function BasicDetailsPanel({
  formData,
  serialNumber,
  isExpanded,
  onTogglePanel,
  onInputChange,
  onSerialNumberChange
}) {
  // Helper style for optional field labels
  const optionalLabelStyle = { 
    fontSize: '12px', 
    color: '#666', 
    fontWeight: 'normal', 
    marginLeft: '5px' 
  };

  return (
    <div className="settings-input-group-two">
      <div className={`toggle-div-collapsable-1-sub-1 ${isExpanded ? 'expanded' : ''}`} onClick={onTogglePanel}>
        <div className="toggle-text-container-parent">
          <div className="link-primary-icon">
            <img
              src="/information_storefront.svg"
              alt="Basic Information"
              width="16"
              height="16"
            />
          </div>
          <div className="toggle-text-container">
            <div className="settings-input-group-h4">Basic Information</div>
            <div className="settings-input-group-h4-sub">
              Core Asset details
            </div>
          </div>
        </div>
        <div className="collapse-icon-container">
          <div className="collapse-icon">
            <img
              src={isExpanded ? "/expand_up.svg" : "/expand_down.svg"}
              alt={isExpanded ? "Collapse" : "Expand"}
              className="toggle-icon"
              width="20" 
              height="20"
            />
          </div>
        </div>
      </div>

      {/* Use location-collapse-container for smooth animation, same as MapComponent */}
      <div className={`location-collapse-container ${isExpanded ? 'expanded' : ''}`}>
        <div
          style={{
            borderTop: '1px solid #E5E7EB',
            margin: '12px 0',
          }}
        />

        {/* Serial Number (with note that it will be hidden when empty) */}
        <div className="input-group">
          <label htmlFor="serial-number">
            Serial Number
            <span style={optionalLabelStyle}>
              (Optional - hidden when empty)
            </span>
          </label>
          <input
            id="serial-number"
            className="input"
            value={serialNumber || ''}
            onChange={(e) => onSerialNumberChange(e.target.value)}
            placeholder="Enter serial number"
          />
        </div>

        {/* Description - now with optional note */}
        <div className="input-group">
          <label htmlFor="asset-description">
            Asset Description
            <span style={optionalLabelStyle}>
              (Optional - hidden when empty)
            </span>
          </label>
          <textarea
            id="asset-description"
            className="input"
            name="description"
            rows="3"
            value={formData.description}
            onChange={onInputChange}
            placeholder="Enter a description of this asset..."
          />
        </div>
      </div>
    </div>
  );
}