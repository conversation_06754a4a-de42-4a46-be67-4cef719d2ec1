import React from 'react';

export default function StatusItem({ status, onEditName, onEditColor, onRemove, isDefault }) {
  return (
    <div className="status-item">
      <div className="status-item-content">
        {/* Status preview badge */}
        <div 
          className="status-badge" 
          style={{
            backgroundColor: `${status.color}20`,
            color: status.color,
            borderColor: `${status.color}40`
          }}
        >
          {status.name}
        </div>
        
        {/* Color picker with enhanced touch target */}
        <div className="color-picker-wrapper">
          <input
            type="color"
            value={status.color}
            onChange={(e) => onEditColor(status.id, e.target.value)}
            className="status-color-picker"
            title="Change status color"
            aria-label="Change status color"
          />
        </div>
        
        {/* Status name input field */}
        <input
          type="text"
          value={status.name}
          onChange={(e) => onEditName(status.id, e.target.value)}
          disabled={isDefault}
          className={`status-name-input ${isDefault ? 'status-name-readonly' : ''}`}
          title={isDefault ? "Default status names cannot be changed" : "Edit status name"}
          aria-label="Status name"
          placeholder="Status name"
        />
      </div>
      
      {/* Remove button */}
      {!isDefault && (
        <button
          className="remove-status-button"
          onClick={(e) => {
            e.stopPropagation();
            onRemove(status.id, e);
          }}
          title="Remove status"
          aria-label="Remove status"
        >
          <svg width="14" height="14" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
            <path d="M12 4L4 12M4 4L12 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      )}
    </div>
  );
}