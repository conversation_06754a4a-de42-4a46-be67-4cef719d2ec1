import React, { useState } from 'react';

export default function AddStatusForm({ statuses, onStatusChange }) {
  const [newStatusName, setNewStatusName] = useState('');
  const [newStatusColor, setNewStatusColor] = useState('#3b82f6');
  const [error, setError] = useState('');
  
  const handleAddStatus = () => {
    if (!newStatusName.trim()) {
      setError('Status name cannot be empty');
      return;
    }
    
    // Create status ID from name
    const id = newStatusName.trim().toLowerCase().replace(/\s+/g, '-');
    
    // Check if status with this name already exists
    const exists = statuses.some(
      status => status.name.toLowerCase() === newStatusName.toLowerCase() || 
                status.id === id
    );
    
    if (exists) {
      setError('A status with this name already exists');
      return;
    }
    
    setError('');
    
    // Add new status
    const updatedStatuses = [
      ...statuses, 
      { 
        id, 
        name: newStatusName.trim(), 
        color: newStatusColor 
      }
    ];
    
    // Call the parent handler
    if (onStatusChange) {
      onStatusChange('customStatuses', updatedStatuses);
    }
    
    // Reset form
    setNewStatusName('');
  };
  
  return (
    <div className="add-status-container">
      <div className="add-status-content">
        <div className="add-status-preview">
          <span className="status-preview-label">Preview:</span>
          <div 
            className="status-badge preview-badge" 
            style={{
              backgroundColor: `${newStatusColor}20`,
              color: newStatusColor,
              borderColor: `${newStatusColor}40`
            }}
          >
            {newStatusName || 'New Status'}
          </div>
        </div>
        
        {error && (
          <div className="status-error-message" role="alert">{error}</div>
        )}
        
        <div className="add-status-inputs">
          <div className="color-picker-wrapper add-form-color-picker">
            <input
              type="color"
              value={newStatusColor}
              onChange={e => setNewStatusColor(e.target.value)}
              className="status-color-picker"
              title="Choose status color"
              aria-label="Choose status color"
            />
          </div>
          
          <input
            type="text"
            value={newStatusName}
            onChange={e => {
              setNewStatusName(e.target.value);
              setError('');
            }}
            placeholder="Enter status name"
            className="status-name-input add-form-input"
            aria-label="Enter new status name"
          />
        </div>
        
        <button
          onClick={handleAddStatus}
          disabled={!newStatusName.trim()}
          className="add-status-button"
          aria-label="Add new status"
        >
          Add Status
        </button>
      </div>
    </div>
  );
}