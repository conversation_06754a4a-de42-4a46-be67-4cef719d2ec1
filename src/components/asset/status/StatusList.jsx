import React from 'react';
import StatusItem from './StatusItem';

export default function StatusList({ statuses, assetData, onStatusChange }) {
  // Helper function to determine if a status is a default one
  const isDefaultStatus = (status) => {
    return status.isDefault === true;
  };
  
  const handleEditStatusName = (statusId, newName) => {
    const status = statuses.find(s => s.id === statusId);
    
    if (status && isDefaultStatus(status)) {
      alert('Default status names cannot be changed');
      return;
    }
    
    if (!newName.trim()) {
      alert('Status name cannot be empty');
      return;
    }
    
    const updatedStatuses = statuses.map(status => 
      status.id === statusId ? { ...status, name: newName } : status
    );
    
    onStatusChange('customStatuses', updatedStatuses);
  };
  
  const handleEditStatusColor = (statusId, newColor) => {
    const updatedStatuses = statuses.map(status => 
      status.id === statusId ? { ...status, color: newColor } : status
    );
    
    onStatusChange('customStatuses', updatedStatuses);
  };
  
  const handleRemoveStatus = (statusId, e) => {
    e.stopPropagation();
    
    const status = statuses.find(s => s.id === statusId);
    
    if (status && isDefaultStatus(status)) {
      alert('Default statuses cannot be removed');
      return;
    }
    
    const isInUse = assetData.logs && assetData.logs.some(log => {
      const logStatusLower = (log.status || '').toLowerCase();
      return logStatusLower === statusId || logStatusLower === statusId.replace(/-/g, ' ');
    });
    
    if (isInUse) {
      alert('Cannot remove a status that is in use in asset logs');
      return;
    }
    
    const updatedStatuses = statuses.filter(status => status.id !== statusId);
    onStatusChange('customStatuses', updatedStatuses);
  };
  
  return (
    <div className="status-list">
      {statuses.length === 0 ? (
        <div className="status-list-empty">
          No statuses added yet. Add your first status below.
        </div>
      ) : (
        statuses.map((status) => (
          <StatusItem
            key={status.id}
            status={status}
            onEditName={handleEditStatusName}
            onEditColor={handleEditStatusColor}
            onRemove={handleRemoveStatus}
            isDefault={isDefaultStatus(status)}
          />
        ))
      )}
    </div>
  );
}