import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

export function ConnectedTags({
  tagIds = [],
  onRemoveTag,
  defaultExpanded = true
}) {
  const navigate = useNavigate();
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);
  
  // Toggle panel open/closed
  const togglePanel = () => {
    setIsExpanded(!isExpanded);
  };
  
  // Don't render anything if there are no tags
  if (!tagIds || tagIds.length === 0) {
    return null;
  }

  return (
    <div className="settings-input-group-two">
      <div 
        className={`toggle-div-collapsable-1-sub-1 ${isExpanded ? 'expanded' : ''}`}
        onClick={togglePanel}
        style={{ cursor: 'pointer' }}
      >
        <div className="toggle-text-container-parent">
          <div className="link-primary-icon">
            <img src="/link_primary.svg" alt="Tags in Asset" width="16" height="16" />
          </div>
          <div className="toggle-text-container">
            <div className="settings-input-group-h4">Connected Tags</div>
            <div className="settings-input-group-h4-sub">
              {tagIds.length} tag{tagIds.length !== 1 ? 's' : ''} connected
            </div>
          </div>
        </div>
        <div className="collapse-icon-container">
          <div className="collapse-icon">
            <img
              src={isExpanded ? "/expand_up.svg" : "/expand_down.svg"}
              alt={isExpanded ? "Collapse" : "Expand"}
              className="toggle-icon"
              width="20" 
              height="20"
            />
          </div>
        </div>
      </div>

      {/* Use location-collapse-container for smooth animation, same as MapComponent */}
      <div className={`location-collapse-container ${isExpanded ? 'expanded' : ''}`}>
        <div
          style={{
            borderTop: '1px solid #E5E7EB',
            margin: '12px 0',
          }}
        />
        <div className="tags-list-wrapper">
          <div className="tags-list-title">Tags in this Asset</div>
          {tagIds.map((tid) => (
            <div
              key={tid}
              className="group-tag-item"
              onClick={() => navigate(`/tags/${tid}`)}
            >
              <div className="group-tag-id">{tid}</div>
              
              {/* Keep the remove button functionality but with consistent styling */}
              <button
                onClick={(e) => {
                  e.stopPropagation(); // Prevent navigation when clicking remove
                  onRemoveTag(e, tid);
                }}
                className="remove-group-button"
                title="Remove Tag"
              >
                <img
                  src="/remove.svg"
                  alt="Remove Tag"
                  className="remove-group-img"
                />
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}