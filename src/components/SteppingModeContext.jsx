import React, { createContext, useState, useContext, useEffect, useMemo, useCallback } from 'react';
import { useCookies } from 'react-cookie';

// Create a context for the stepping mode state
const SteppingModeContext = createContext();

// Provider component that wraps the app and makes stepping mode available
export const SteppingModeProvider = ({ children }) => {
  const [cookies, setCookie, removeCookie] = useCookies(['stepping_mode']);
  const [isSteppingMode, setIsSteppingMode] = useState(cookies.stepping_mode === 'true');

  // Sync state with cookie when cookie changes
  useEffect(() => {
    const cookieValue = cookies.stepping_mode === 'true';
    if (isSteppingMode !== cookieValue) {
      setIsSteppingMode(cookieValue);
    }
  }, [cookies.stepping_mode, isSteppingMode]);

  // Function to toggle stepping mode - using useCallback to prevent unnecessary re-renders
  const toggleSteppingMode = useCallback(() => {
    const newValue = !isSteppingMode;
    setIsSteppingMode(newValue);

    const cookieDomain = import.meta.env.VITE_COOKIE_DOMAIN;

    if (newValue) {
      setCookie('stepping_mode', 'true', {
        path: '/',
        domain: cookieDomain,
        sameSite: 'none',
        secure: !!cookieDomain,
        maxAge: 24 * 60 * 60,
      });
    } else {
      removeCookie('stepping_mode', {
        path: '/',
        domain: cookieDomain,
        sameSite: 'none',
        secure: !!cookieDomain,
      });
    }
  }, [isSteppingMode, setCookie, removeCookie]);

  // Memoize the context value to prevent unnecessary re-renders
  const value = useMemo(() => ({
    isSteppingMode,
    toggleSteppingMode
  }), [isSteppingMode, toggleSteppingMode]);

  return (
    <SteppingModeContext.Provider value={value}>
      {children}
    </SteppingModeContext.Provider>
  );
};

// Custom hook to use the stepping mode context
export const useSteppingMode = () => {
  const context = useContext(SteppingModeContext);
  if (context === undefined) {
    throw new Error('useSteppingMode must be used within a SteppingModeProvider');
  }
  return context;
};
