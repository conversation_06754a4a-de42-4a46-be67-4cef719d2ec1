import React, { useRef, useEffect, useState } from 'react';
import { Map } from 'react-map-gl/maplibre';
import maplibregl from 'maplibre-gl';
import Supercluster from 'supercluster';
import 'maplibre-gl/dist/maplibre-gl.css';
import '../styles/LandingPageMap.css';

// Restructured DEMO_LOCATIONS to group by city
const DEMO_LOCATIONS = {
  all: [
    // Seattle
    { lat: 47.612, lng: -122.347, city: 'seattle' },
    { lat: 47.608, lng: -122.335, city: 'seattle' },
    { lat: 47.605, lng: -122.330, city: 'seattle' },
    { lat: 47.615, lng: -122.348, city: 'seattle' },
    { lat: 47.618, lng: -122.352, city: 'seattle' },
    { lat: 47.602, lng: -122.332, city: 'seattle' },
    // San Francisco
    { lat: 37.782, lng: -122.415, city: 'sanfran' },
    { lat: 37.785, lng: -122.437, city: 'sanfran' },
    { lat: 37.774, lng: -122.419, city: 'sanfran' },
    // New York
    { lat: 40.758, lng: -73.985, city: 'newyork' },
    { lat: 40.748, lng: -73.978, city: 'newyork' },
    { lat: 40.712, lng: -74.010, city: 'newyork' },
    // Los Angeles
    { lat: 34.052, lng: -118.243, city: 'losangeles' },
    { lat: 34.048, lng: -118.259, city: 'losangeles' },
    { lat: 34.043, lng: -118.267, city: 'losangeles' },
  ],
  seattle: [
    { lat: 47.612, lng: -122.347, city: 'seattle' },
    { lat: 47.608, lng: -122.335, city: 'seattle' },
    { lat: 47.605, lng: -122.330, city: 'seattle' },
    { lat: 47.615, lng: -122.348, city: 'seattle' },
    { lat: 47.618, lng: -122.352, city: 'seattle' },
    { lat: 47.602, lng: -122.332, city: 'seattle' },
  ],
  sanfran: [
    { lat: 37.782, lng: -122.415, city: 'sanfran' },
    { lat: 37.785, lng: -122.437, city: 'sanfran' },
    { lat: 37.774, lng: -122.419, city: 'sanfran' },
  ],
  newyork: [
    { lat: 40.758, lng: -73.985, city: 'newyork' },
    { lat: 40.748, lng: -73.978, city: 'newyork' },
    { lat: 40.712, lng: -74.010, city: 'newyork' },
  ],
  losangeles: [
    { lat: 34.052, lng: -118.243, city: 'losangeles' },
    { lat: 34.048, lng: -118.259, city: 'losangeles' },
    { lat: 34.043, lng: -118.267, city: 'losangeles' },
  ]
};

const MAP_CONTAINER_STYLE = {
  width: '100%',
  height: '450px', // Taller for the landing page
  borderRadius: '12px',
  overflow: 'hidden',
  boxShadow: '0 8px 24px rgba(0, 0, 0, 0.12)'
};

const MAP_STYLES = {
  standardLight: { style: 'Standard', colorScheme: 'Light' },
  standardDark: { style: 'Standard', colorScheme: 'Dark' },
  monochromeLight: { style: 'Monochrome', colorScheme: 'Light' },
  monochromeDark: { style: 'Monochrome', colorScheme: 'Dark' },
  hybrid: { style: 'Hybrid' },
  satellite: { style: 'Satellite' }
};

// Initial center points for each location
const LOCATION_CENTERS = {
  all: { lat: 39.8283, lng: -98.5795, zoom: 3 },
  seattle: { lat: 47.610, lng: -122.340, zoom: 12 },
  sanfran: { lat: 37.780, lng: -122.420, zoom: 12 },
  newyork: { lat: 40.740, lng: -73.990, zoom: 12 },
  losangeles: { lat: 34.050, lng: -118.250, zoom: 12 }
};

function LandingPageMap({ mapStyle = 'monochromeDark', selectedLocation = 'seattle' }) {
  const mapRef = useRef(null);
  const markersRef = useRef([]);
  const clusterRef = useRef(null);
  const [mapLoaded, setMapLoaded] = useState(false);

  // Get the initial view state based on selectedLocation
  const initialViewState = LOCATION_CENTERS[selectedLocation] || LOCATION_CENTERS.seattle;

  const apiKey = import.meta.env.VITE_AWS_MAPS_API_KEY;
  const region = import.meta.env.VITE_AWS_MAPS_REGION;

  const getMapStyle = (styleKey) => {
    const styleConfig = MAP_STYLES[styleKey];
    const baseUrl = `https://maps.geo.${region}.amazonaws.com/v2/styles/${styleConfig.style}/descriptor?key=${apiKey}`;
    return styleConfig.colorScheme ? `${baseUrl}&color-scheme=${styleConfig.colorScheme}` : baseUrl;
  };

  // Create a marker element for a single pin
  const createMarkerElement = () => {
    const markerElement = document.createElement('div');
    markerElement.className = 'custom-marker';
    
    // Create pin image
    const img = document.createElement('img');
    img.src = '/pin.svg';
    img.className = 'custom-marker-pin';
    img.alt = 'Location pin';
    markerElement.appendChild(img);
    
    // // Add pulse effect
    // const pulseRing = document.createElement('div');
    // pulseRing.className = 'pulse-ring';
    // markerElement.appendChild(pulseRing);
    
    // const pulseRingDelay = document.createElement('div');
    // pulseRingDelay.className = 'pulse-ring pulse-ring-delay';
    // markerElement.appendChild(pulseRingDelay);
    
    return markerElement;
  };

  // Create a cluster marker with count
  const createClusterMarker = (count) => {
    const markerElement = document.createElement('div');
    markerElement.className = 'custom-marker cluster';
    markerElement.textContent = count;
    return markerElement;
  };

  // Initialize the supercluster
  const initializeCluster = (locations) => {
    const cluster = new Supercluster({
      radius: 40,
      maxZoom: 16
    });
    
    const points = locations.map(loc => ({
      type: 'Feature',
      geometry: {
        type: 'Point',
        coordinates: [loc.lng, loc.lat]
      },
      properties: { city: loc.city }
    }));
    
    cluster.load(points);
    return cluster;
  };

  // Update markers based on the current viewport and zoom level
  const updateMarkers = () => {
    if (!mapRef.current?.getMap() || !clusterRef.current) return;
    
    const map = mapRef.current.getMap();
    
    // Clear any existing markers
    markersRef.current.forEach(marker => marker.remove());
    markersRef.current = [];
    
    const bounds = map.getBounds();
    const zoom = Math.floor(map.getZoom());
    
    const bbox = [
      bounds.getWest(),
      bounds.getSouth(),
      bounds.getEast(),
      bounds.getNorth()
    ];
    
    const clusters = clusterRef.current.getClusters(bbox, zoom);
    
    clusters.forEach(cluster => {
      const [lng, lat] = cluster.geometry.coordinates;
      
      let marker;
      // Check if it's a cluster or a single point
      if (cluster.properties.cluster) {
        const count = cluster.properties.point_count;
        const element = createClusterMarker(count);
        
        marker = new maplibregl.Marker({
          element: element,
          anchor: 'center'
        }).setLngLat([lng, lat]).addTo(map);
      } else {
        const element = createMarkerElement();
        
        marker = new maplibregl.Marker({
          element: element,
          anchor: 'bottom'
        }).setLngLat([lng, lat]).addTo(map);
      }
      
      markersRef.current.push(marker);
    });
  };

  // Update markers when the map loads or selectedLocation changes
  useEffect(() => {
    if (!mapLoaded || !mapRef.current?.getMap()) return;

    const map = mapRef.current.getMap();
    
    // Clear any existing markers
    markersRef.current.forEach(marker => marker.remove());
    markersRef.current = [];

    // Get the locations for the selected city
    const locations = DEMO_LOCATIONS[selectedLocation] || DEMO_LOCATIONS.all;
    
    // Initialize supercluster with locations
    clusterRef.current = initializeCluster(locations);
    
    // Update markers based on current view
    updateMarkers();
    
    // Add event listeners for map movement
    map.on('moveend', updateMarkers);
    map.on('zoomend', updateMarkers);

    // Fit bounds to show all markers
    if (locations.length > 0) {
      const bounds = new maplibregl.LngLatBounds();
      locations.forEach(location => {
        bounds.extend([location.lng, location.lat]);
      });
      
      map.fitBounds(bounds, {
        padding: 100,
        duration: 1000 // Animated transition when changing location
      });
    } else {
      // If no locations, center on the selected city
      const center = LOCATION_CENTERS[selectedLocation];
      map.flyTo({
        center: [center.lng, center.lat],
        zoom: center.zoom,
        duration: 1000
      });
    }

    return () => {
      // Clean up event listeners
      map.off('moveend', updateMarkers);
      map.off('zoomend', updateMarkers);
      markersRef.current.forEach(marker => marker.remove());
    };
  }, [mapLoaded, selectedLocation]);

  return (
    <div className="landing-map-wrapper">
      <Map
        ref={mapRef}
        initialViewState={{
          latitude: initialViewState.lat,
          longitude: initialViewState.lng,
          zoom: initialViewState.zoom
        }}
        mapStyle={getMapStyle(mapStyle)}
        style={MAP_CONTAINER_STYLE}
        cooperativeGestures={true}
        attributionControl={false}
        onLoad={() => setMapLoaded(true)}
      />
      <div className="map-overlay">
        <div className="overlay-badge">Live Fleet Tracking</div>
      </div>
    </div>
  );
}

export { LandingPageMap };