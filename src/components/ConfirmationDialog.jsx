import React, { memo, useCallback, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import PropTypes from 'prop-types';
import '../styles/ConfirmationDialog.css';

const ConfirmationDialog = ({ 
  isOpen, 
  title, 
  message, 
  confirmText = 'Confirm', 
  cancelText = 'Cancel', 
  onConfirm, 
  onCancel,
  isDanger = false,
  closeOnBackdropClick = true
}) => {
  const dialogRef = useRef(null);
  const confirmButtonRef = useRef(null);
  
  // Determine if this is single button mode
  const isSingleButton = !onCancel || cancelText === '';
  
  const handleConfirm = useCallback(() => {
    onConfirm();
  }, [onConfirm]);
  
  const handleCancel = useCallback(() => {
    if (onCancel) {
      onCancel();
    }
  }, [onCancel]);
  
  const handleKeyDown = useCallback((e) => {
    if (e.key === 'Escape') {
      if (isSingleButton) {
        // For single button mode, escape acts like confirm
        handleConfirm();
      } else {
        // For two button mode, escape acts like cancel
        handleCancel();
      }
    }
  }, [handleCancel, handleConfirm, isSingleButton]);

  const handleBackdropClick = useCallback((e) => {
    if (closeOnBackdropClick && e.target === e.currentTarget) {
      if (isSingleButton) {
        // For single button mode, backdrop click acts like confirm
        handleConfirm();
      } else {
        // For two button mode, backdrop click acts like cancel
        handleCancel();
      }
    }
  }, [closeOnBackdropClick, handleCancel, handleConfirm, isSingleButton]);
  
  const tabTrapHandler = useCallback((e) => {
    if (e.key === 'Tab') {
      const focusableElements = dialogRef.current?.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      
      if (focusableElements?.length) {
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];
        
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            lastElement.focus();
            e.preventDefault();
          }
        } else {
          if (document.activeElement === lastElement) {
            firstElement.focus();
            e.preventDefault();
          }
        }
      }
    }
  }, []);
  
  useEffect(() => {
    if (isOpen) {
      if (confirmButtonRef.current) {
        confirmButtonRef.current.focus();
      }
      document.addEventListener('keydown', handleKeyDown);
      
      const previousActiveElement = document.activeElement;
      
      if (dialogRef.current) {
        dialogRef.current.addEventListener('keydown', tabTrapHandler);
      }
      
      return () => {
        document.removeEventListener('keydown', handleKeyDown);
        dialogRef.current?.removeEventListener('keydown', tabTrapHandler);
        if (previousActiveElement) {
          previousActiveElement.focus();
        }
      };
    }
  }, [isOpen, handleKeyDown, tabTrapHandler]);
  
  if (!isOpen) return null;
  
  return createPortal(
    <div 
      className="confirmation-dialog-backdrop" 
      onClick={handleBackdropClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby="dialog-title"
      aria-describedby="dialog-description"
    >
      <div 
        className="confirmation-dialog"
        ref={dialogRef}
      >
        <div className="confirmation-dialog-header">
          <h3 id="dialog-title">{title}</h3>
        </div>
        <div className="confirmation-dialog-content">
          <p id="dialog-description">{message}</p>
        </div>
        <div className="confirmation-dialog-actions">
          {/* Only render cancel button if not in single button mode */}
          {!isSingleButton && (
            <button 
              type="button"
              className="confirmation-dialog-button cancel-button" 
              onClick={handleCancel}
              aria-label={cancelText}
            >
              {cancelText}
            </button>
          )}
          <button 
            type="button"
            className={`confirmation-dialog-button ${isDanger ? 'danger-button' : 'confirm-button'}`}
            onClick={handleConfirm}
            ref={confirmButtonRef}
            aria-label={confirmText}
          >
            {confirmText}
          </button>
        </div>
      </div>
    </div>,
    document.body
  );
};

ConfirmationDialog.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  title: PropTypes.string.isRequired,
  message: PropTypes.oneOfType([PropTypes.string, PropTypes.node]).isRequired,
  confirmText: PropTypes.string,
  cancelText: PropTypes.string,
  onConfirm: PropTypes.func.isRequired,
  onCancel: PropTypes.func, // Now optional
  isDanger: PropTypes.bool,
  closeOnBackdropClick: PropTypes.bool
};

export default memo(ConfirmationDialog);