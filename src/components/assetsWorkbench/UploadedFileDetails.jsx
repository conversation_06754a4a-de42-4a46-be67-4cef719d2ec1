import React, { useState, useEffect } from 'react';
import EditableCell from '../assetsWorkbench/EditableCell';
import StatusBadge from '../assetsWorkbench/StatusBadge';
import { initAssetIdsDB, geocodeAddress, validateAssetId, validateCoordinate } from '../../utils/assetUtils';
import * as XLSX from 'xlsx';

const UploadedFileDetails = ({
    file,
    setUploadedFile,
    setValidatedRows,
    setValidationStatus,
    initialData = [],
    allowAddRows = false
}) => {
    const [excelData, setExcelData] = useState(initialData);
    const [isAddressColumn, setIsAddressColumn] = useState(false);
    const [validationErrors, setValidationErrors] = useState([]);
    const [isGeocoding, setIsGeocoding] = useState(false);
    const [hasCoordinates, setHasCoordinates] = useState(false);
    const [rowStatus, setRowStatus] = useState([]);
    const [isExampleTemplate, setIsExampleTemplate] = useState(initialData.length > 1 || (initialData[0] && initialData[0].some(cell => cell !== '')));
    // Add Status column back, after ID
    const headers = ['ID', 'Status', 'Asset Name', 'Public URL', 'Private URL', 'Asset Address', 'Latitude', 'Longitude'];

    const handleGeocodeAll = async (excelData, setExcelData) => {
        const updatedData = [...excelData];

        for (let i = 0; i < updatedData.length; i++) {
            if (updatedData[i][4]) { // Column E (index 4)
                const result = await geocodeAddress(updatedData[i][4]);
                if (result) {
                    // Convert to strings with fixed decimal places to ensure proper number handling
                    updatedData[i][5] = result.latitude.toFixed(6);  // Column F
                    updatedData[i][6] = result.longitude.toFixed(6); // Column G
                }
            }
        }

        setExcelData(updatedData);
        return updatedData;
    };

    const handleAddressToggle = async (checked) => {
        setIsAddressColumn(checked);
        if (checked) {
            setIsGeocoding(true);
            const updatedData = await handleGeocodeAll(excelData, setExcelData);
            processAndValidateData(updatedData);
            setIsGeocoding(false);
        }
    };

    // Check if each asset ID exists in our IndexedDB
    const checkAssetStatuses = async (rows) => {
        try {
            const db = await initAssetIdsDB();
            const statuses = await Promise.all(
                rows.map(async (row) => {
                    const assetId = row[0];
                    if (!assetId) return 'New';

                    // We stored just the asset ID part (without UUID prefix),
                    // so we look it up directly
                    const exists = await db.get('assetIds', assetId);
                    return exists ? 'Update' : 'New';
                })
            );
            setRowStatus(statuses);
        } catch (error) {
            console.error('Error checking asset statuses:', error);
            setRowStatus(rows.map(() => 'Unknown'));
        }
    };

    const processAndValidateData = async (data, validateEmpty = false) => {
        const errors = [];
        const seenIds = new Set();

        const mappedRows = data.map((row, index) => {
            // Skip validation for empty rows unless validateEmpty is true
            const isEmpty = !row[0] && !row[1] && !row[2] && !row[3] && !row[4] && !row[5] && !row[6];

            if (!validateEmpty && isEmpty) {
                // Skip validation for empty rows
            } else {
                const assetIdValidation = validateAssetId(row[0]);
                const latValidation = validateCoordinate(row[5], 'latitude');
                const longValidation = validateCoordinate(row[6], 'longitude');

                // Add validation errors if any
                if (!assetIdValidation.valid) {
                    errors.push({
                        row: index + 2,
                        error: `Invalid Asset ID: ${assetIdValidation.error}`
                    });
                }

                if (row[0] && seenIds.has(row[0])) {
                    errors.push({
                        row: index + 2,
                        error: 'Duplicate Asset ID found in file'
                    });
                }

                if (row[0]) {
                    seenIds.add(row[0]);
                }

                if (!latValidation.valid) {
                    errors.push({
                        row: index + 2,
                        error: latValidation.error
                    });
                }

                if (!longValidation.valid) {
                    errors.push({
                        row: index + 2,
                        error: longValidation.error
                    });
                }
            }

            // Convert lat/long from string to number or null
            const latitude = row[5] ? parseFloat(row[5]) : null;
            const longitude = row[6] ? parseFloat(row[6]) : null;

            return {
                assetId: row[0],
                name: row[1],
                publicAction: row[2],
                privateAction: row[3],
                address: row[4],
                latitude: latitude,
                longitude: longitude
            };
        });

        setValidationErrors(errors);
        setValidatedRows(mappedRows);

        // Only set validation status to complete if there are no errors
        // or if the only rows with errors are empty rows (when not validating empty rows)
        const hasNonEmptyErrors = errors.length > 0;
        setValidationStatus(hasNonEmptyErrors ? 'error' : 'complete');

        // Check status for each row (New/Update)
        await checkAssetStatuses(data);
    };

    const handleCellChange = (rowIndex, columnIndex, newValue) => {
        const updatedData = [...excelData];
        updatedData[rowIndex][columnIndex] = newValue;
        setExcelData(updatedData);
        processAndValidateData(updatedData);
        // Don't update isExampleTemplate here - it should only change when source changes
    };

    const handleAddRow = () => {
        const newRow = ['', '', '', '', '', '', ''];
        const updatedData = [...excelData, newRow];
        setExcelData(updatedData);
        processAndValidateData(updatedData);
        // Don't update isExampleTemplate here - it should only change when source changes
    };

    const handleRemoveRow = (rowIndex) => {
        if (excelData.length <= 1) {
            // Don't remove the last row, just clear it
            const clearedRow = ['', '', '', '', '', '', ''];
            const updatedData = [clearedRow];
            setExcelData(updatedData);
            processAndValidateData(updatedData);
        } else {
            const updatedData = [...excelData];
            updatedData.splice(rowIndex, 1);
            setExcelData(updatedData);
            processAndValidateData(updatedData);
        }
        // Don't update isExampleTemplate here - it should only change when source changes
    };

    // Handle tab navigation between cells
    const handleTabNavigation = (rowIndex, colIndex, isShiftTab, isEnterKey = false) => {
        // Define the total number of columns (excluding Status and Actions)
        const totalCols = 7; // ID + 6 data columns

        let nextRow = rowIndex;
        let nextCol = colIndex;

        if (isEnterKey) {
            // On Enter key, move to the same column in the next row
            nextRow = rowIndex + 1;

            // If we're at the last row, add a new row
            if (nextRow >= excelData.length) {
                handleAddRow();
            }
        } else if (isShiftTab) {
            // Navigate backwards
            if (colIndex === 0) {
                // If at the first column, go to the last column of the previous row
                nextRow = rowIndex - 1;
                nextCol = totalCols - 1;
            } else {
                // Otherwise, go to the previous column
                nextCol = colIndex - 1;
            }
        } else {
            // Navigate forwards
            if (colIndex === totalCols - 1) {
                // If at the last column, go to the first column of the next row
                nextRow = rowIndex + 1;
                nextCol = 0;

                // If we're at the last row, add a new row
                if (nextRow >= excelData.length) {
                    handleAddRow();
                }
            } else {
                // Otherwise, go to the next column
                nextCol = colIndex + 1;
            }
        }

        // Make sure we're within bounds
        if (nextRow >= 0 && nextRow < excelData.length) {
            // Find the next cell and focus it
            setTimeout(() => {
                const tabIndex = nextRow * 10 + nextCol + 1;
                const nextCell = document.querySelector(`[tabindex="${tabIndex}"]`);
                if (nextCell) {
                    nextCell.focus();
                    // If it's a div (not in edit mode), simulate a click to enter edit mode
                    if (nextCell.tagName.toLowerCase() === 'div') {
                        nextCell.click();
                    }
                }
            }, 10);
        }

        // No need to track active cell
    };

    // Initialize with example data if provided
    useEffect(() => {
        if (initialData.length > 0) {
            setExcelData(initialData);
            const hasExistingCoordinates = initialData.some(row =>
                (row[5] !== '' && row[5] !== null) ||
                (row[6] !== '' && row[6] !== null)
            );
            setHasCoordinates(hasExistingCoordinates);
            processAndValidateData(initialData);
        }
    }, [initialData]);

    // Handle file upload
    useEffect(() => {
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const data = new Uint8Array(e.target.result);
                const workbook = XLSX.read(data, { type: 'array' });
                const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
                let rows = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });

                if (rows.length < 2) {
                    setValidationErrors([{
                        row: 1,
                        error: 'File must contain at least one data row in addition to the header row'
                    }]);
                    setValidationStatus('error');
                    setExcelData([]);
                    return;
                }

                const processedRows = rows.slice(1).map(row => {
                    const paddedRow = [...row];
                    while (paddedRow.length < 7) {
                        paddedRow.push('');
                    }
                    return paddedRow;
                });

                const hasExistingCoordinates = processedRows.some(row =>
                    (row[5] !== '' && row[5] !== null) ||
                    (row[6] !== '' && row[6] !== null)
                );
                setHasCoordinates(hasExistingCoordinates);
                setExcelData(processedRows);
                processAndValidateData(processedRows);
            };
            reader.readAsArrayBuffer(file);
        }
    }, [file, setValidatedRows, setValidationStatus]);

    return (
        <div className="uploaded-file-details">
            <div className="file-header">
                {validationErrors.length > 0 && (
                    <div className="validation-errors">
                        <h4>Validation Errors Found:</h4>
                        <ul>
                            {validationErrors.map((error, index) => (
                                <li key={index} className="validation-error">
                                    Row {error.row}: {error.error}
                                </li>
                            ))}
                        </ul>
                    </div>
                )}
                <div className="file-info">
                    {(file || isExampleTemplate) && (
                        <div className="file-icon">
                            <img src="/excel.svg" alt="Excel" />
                        </div>
                    )}
                    <span className="file-name">
                        {file ? file.name :
                         isExampleTemplate ? 'Example Template' : 'New Asset Sheet'}
                    </span>
                </div>
                {!hasCoordinates && (
                    <div className="address-toggle">
                        <label className="toggle-label">
                            <input
                                type="checkbox"
                                checked={isAddressColumn}
                                onChange={(e) => handleAddressToggle(e.target.checked)}
                                disabled={isGeocoding}
                            />
                            Geocode address column
                            {isGeocoding && <span className="geocoding-status">Geocoding...</span>}
                        </label>
                    </div>
                )}
                <span
                    className="material-icons-outlined close-button"
                    onClick={() => setUploadedFile(null)}
                >
                    close
                </span>
            </div>

            <div className="table-container">
                <table className="excel-table">
                    <thead>
                        <tr>
                            {headers.map((header, index) => (
                                <th key={index}>{header}</th>
                            ))}
                            {allowAddRows && <th className="actions-column">Actions</th>}
                        </tr>
                    </thead>
                    <tbody>
                        {excelData.map((row, rowIndex) => (
                            <tr key={rowIndex}>
                                {/* ID column */}
                                <td>
                                    <EditableCell
                                        value={row[0]}
                                        onChange={(newValue) => handleCellChange(rowIndex, 0, newValue)}
                                        onTab={(isShiftTab, isEnterKey) => handleTabNavigation(rowIndex, 0, isShiftTab, isEnterKey)}
                                        tabIndex={rowIndex * 10 + 1}
                                    />
                                </td>
                                {/* Status column */}
                                <td>
                                    <StatusBadge status={rowStatus[rowIndex]} />
                                </td>
                                {/* Regular data columns (skip the first one since we're handling it separately) */}
                                {row.slice(1).map((cell, cellIndex) => (
                                    <td key={cellIndex + 1}>
                                        <EditableCell
                                            value={cell}
                                            onChange={(newValue) => handleCellChange(rowIndex, cellIndex + 1, newValue)}
                                            onTab={(isShiftTab, isEnterKey) => handleTabNavigation(rowIndex, cellIndex + 1, isShiftTab, isEnterKey)}
                                            tabIndex={rowIndex * 10 + cellIndex + 2}
                                        />
                                    </td>
                                ))}
                                {/* Actions column */}
                                {allowAddRows && (
                                    <td className="row-actions">
                                        <button
                                            className="remove-row-button"
                                            onClick={() => handleRemoveRow(rowIndex)}
                                            title="Remove row"
                                        >
                                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                                <path d="M18 6L6 18M6 6l12 12"></path>
                                            </svg>
                                        </button>
                                    </td>
                                )}
                            </tr>
                        ))}
                    </tbody>
                </table>

                {allowAddRows && (
                    <div className="add-row-container">
                        <button
                            className="add-row-button"
                            onClick={handleAddRow}
                            title="Add new row"
                        >
                            <span className="add-row-icon">+</span>
                            <span className="add-row-text">Add Row</span>
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};

export default UploadedFileDetails;