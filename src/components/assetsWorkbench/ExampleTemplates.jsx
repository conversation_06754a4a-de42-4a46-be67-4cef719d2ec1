import React, { useState, useCallback, useEffect } from 'react';
import { Auth, API } from "aws-amplify";
import { useDropzone } from 'react-dropzone';
import * as XLSX from 'xlsx';
import '../../styles/ExampleTemplates.css';

export const ExampleTemplates = ({ onTryExample, onFileUpload }) => {
  const [isTemplatesExpanded, setIsTemplatesExpanded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [assetCount, setAssetCount] = useState(0);

  // Extract asset ID from the full ID by removing UUID prefix
  const extractAssetId = (fullId) => {
    if (!fullId) return '';

    // Find pattern UUID-AssetID (UUID is 36 chars including hyphens)
    const matches = fullId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}-(.*)/);
    if (matches && matches[1]) {
      return matches[1];
    }
    return fullId; // Return original if pattern not found
  };

  // Function to fetch existing assets
  const fetchExistingAssets = useCallback(async () => {
    setIsLoading(true);
    try {
      const session = await Auth.currentSession();
      const headers = {
        Authorization: `Bearer ${session.getAccessToken().getJwtToken()}`,
        'id-Token': session.getIdToken().getJwtToken(),
      };

      const response = await API.get('api', '/assets', { headers });
      const assets = response?.items || [];
      setAssetCount(assets.length);
      return assets;
    } catch (error) {
      console.error('Error fetching existing assets:', error);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Load asset count on component mount
  React.useEffect(() => {
    fetchExistingAssets();
  }, [fetchExistingAssets]);

  // Download existing assets as Excel file
  const downloadExistingAssets = async () => {
    setIsLoading(true);
    try {
      const existingAssets = await fetchExistingAssets();

      if (existingAssets.length === 0) {
        alert('No assets found to download.');
        return;
      }

      // Format data for Excel
      const formattedData = existingAssets.map(asset => ({
        ID: extractAssetId(asset.id) || '',
        'Asset Name': asset.name || '',
        'Public URL': asset.publicAction || '',
        'Private URL': asset.privateAction || '',
        'Asset Address': asset.address || '',
        'Latitude': asset.latitude || '',
        'Longitude': asset.longitude || ''
      }));

      // Create workbook and worksheet
      const worksheet = XLSX.utils.json_to_sheet(formattedData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Assets');

      // Generate Excel file and trigger download
      XLSX.writeFile(workbook, 'existing_assets.xlsx');
    } catch (error) {
      console.error('Error downloading assets:', error);
      alert('Failed to download assets. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle loading existing assets for editing
  const handleEditExistingAssets = async () => {
    setIsLoading(true);
    try {
      const existingAssets = await fetchExistingAssets();

      if (existingAssets.length === 0) {
        alert('No assets found to load.');
        return;
      }

      // Format data to match the expected format by onTryExample
      // Convert the assets into array rows (each row is an array of values)
      const formattedData = existingAssets.map(asset => [
        extractAssetId(asset.id) || '',  // Clean ID by removing UUID prefix
        asset.name || '',
        asset.publicAction || '',
        asset.privateAction || '',
        asset.address || '',
        asset.latitude !== null && asset.latitude !== undefined ? String(asset.latitude) : '',
        asset.longitude !== null && asset.longitude !== undefined ? String(asset.longitude) : ''
      ]);

      // Call onTryExample with formatted data
      onTryExample('existing', formattedData);
    } catch (error) {
      console.error('Error loading assets for editing:', error);
      alert('Failed to load assets. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const toggleTemplatesExpanded = () => {
    setIsTemplatesExpanded(!isTemplatesExpanded);
  };

  // Setup dropzone
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: onFileUpload,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls'],
      'text/csv': ['.csv']
    },
    maxSize: 3 * 1024 * 1024,
  });

  // Example template for new asset creation - keeping only the general template
  const templates = [
    {
      id: 'general',
      name: 'Service Equipment Template',
      description: 'A standard template suitable for most use cases within the service industry',
      url: '/example_templates/general_template.xlsx'
    }
  ];

  return (
    <div className="asset-workbench-sources">
      {/* Primary Section: Existing Asset Management */}
      <div className="asset-workbench-primary-section">
        <h3>Manage Existing Assets</h3>
        <div className="asset-workbench-existing-assets-card">
          <div className="asset-workbench-existing-assets-info">
            <div className="asset-workbench-existing-assets-icon">
              <img src="/assets.svg" alt="Assets" />
            </div>
            <div className="asset-workbench-existing-assets-details">
              <h4>Your Thinkertag Assets</h4>
              <p>Edit and update your existing assets in bulk</p>
              {assetCount > 0 && !isLoading && (
                <div className="asset-workbench-asset-count">{assetCount} assets available</div>
              )}
              {isLoading && <p className="asset-workbench-loading-message">Loading assets...</p>}
            </div>
          </div>
          <div className="asset-workbench-existing-assets-actions">
            <button
              className="asset-workbench-download-button"
              onClick={downloadExistingAssets}
              disabled={isLoading}
            >
              Download
            </button>
            <button
              className="asset-workbench-edit-button"
              onClick={handleEditExistingAssets}
              disabled={isLoading}
            >
              Edit Assets
            </button>
          </div>
        </div>
      </div>

      {/* Secondary Section: Import New Assets */}
      <div className="asset-workbench-secondary-section">
        <div className="asset-workbench-section-header">
          <h3>Import New Assets</h3>
          <div className="asset-workbench-templates-toggle" onClick={toggleTemplatesExpanded}>
            <span>Example template</span>
            <button
              className={`asset-workbench-expand-button ${isTemplatesExpanded ? 'expanded' : ''}`}
              aria-label={isTemplatesExpanded ? "Hide template" : "Show template"}
            >
              <span className="asset-workbench-plus-icon"></span>
            </button>
          </div>
        </div>

        {/* File Upload Dropzone */}
        <div className="asset-workbench-import-options">
          <div className="asset-workbench-dropzone-container">
            <div {...getRootProps()} className="asset-workbench-dropzone">
              <input {...getInputProps()} />
              <img src="/upload.svg" className="asset-workbench-upload-file-icon" />
              {isDragActive ? (
                <p>Drop the file here...</p>
              ) : (
                <p>Upload your excel file here</p>
              )}
            </div>
          </div>
        </div>

        {/* Templates (collapsible) */}
        <div className={`asset-workbench-template-list ${isTemplatesExpanded ? 'expanded' : ''}`}>
          {templates.map(template => (
            <div className="asset-workbench-template-card" key={template.id}>
              <div className="asset-workbench-template-icon">
                <img src="/excel.svg" alt="Excel template" />
              </div>
              <div className="asset-workbench-template-info">
                <h4>{template.name}</h4>
                <p>{template.description}</p>
              </div>
              <div className="asset-workbench-template-buttons">
                <a
                  href={template.url}
                  className="asset-workbench-download-button-examples"
                  download
                >
                  Download
                </a>
                <button
                  className="asset-workbench-try-button-examples"
                  onClick={() => onTryExample(template.id)}
                >
                  Try Now
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};