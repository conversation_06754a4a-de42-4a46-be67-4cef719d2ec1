import React, { useState, useEffect } from 'react';

const EditableCell = ({ value, onChange, onTab, tabIndex }) => {
    const [isEditing, setIsEditing] = useState(false);
    const [currentValue, setCurrentValue] = useState(value);

    useEffect(() => {
        setCurrentValue(value);
    }, [value]);

    const handleClick = () => {
        setIsEditing(true);
    };

    const handleBlur = () => {
        setIsEditing(false);
        onChange(currentValue);
    };

    const handleKeyDown = (e) => {
        if (e.key === 'Enter') {
            e.preventDefault(); // Prevent form submission
            setIsEditing(false);
            onChange(currentValue);

            // Move to the next row (same column) on Enter
            if (onTab) {
                // Use false for forward navigation, but we'll handle it differently in the parent
                setTimeout(() => onTab(false, true), 0);
            }
        } else if (e.key === 'Tab') {
            // Don't prevent default to allow natural tab behavior
            setIsEditing(false);
            onChange(currentValue);

            // Call onTab if provided
            if (onTab) {
                // Use setTimeout to ensure the onChange is processed first
                setTimeout(() => onTab(e.shiftKey, false), 0);
            }
        }
    };

    return isEditing ? (
        <input
            type="text"
            value={currentValue}
            onChange={(e) => setCurrentValue(e.target.value)}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            autoFocus
            className="cell-input"
            tabIndex={tabIndex}
        />
    ) : (
        <div
            className="cell"
            onClick={handleClick}
            tabIndex={tabIndex}
        >
            {value}
        </div>
    );
};

export default EditableCell;