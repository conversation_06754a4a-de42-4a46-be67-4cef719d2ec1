import React from 'react';
import { Link, useNavigate } from 'react-router-dom';  // Import Link from react-router-dom



const GradientSquare = ({ code }) => {

    const navigate = useNavigate(); // Define navigate using useNavigate()

    const textStyle = {
        color: 'black',
        WebkitBackgroundClip: 'text',
        backgroundClip: 'text',
        fontWeight: 'bold',
        fontSize: '15px'
    };

    const containerStyle = {
        width: '100%',
        height: '9em',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: '10px',
        border: '0.5px solid #D4D7DE',
        backgroundColor: '#fcfcfc',
    };

    return (
        <div className='grid-item' style={containerStyle} key={code} onClick={() => navigate(`/tags/${code}`)} >
            <span style={textStyle}>{code}</span>
        </div>
    );
};

export default GradientSquare;
