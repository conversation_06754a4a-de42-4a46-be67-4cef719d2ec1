import React, { memo, useCallback } from 'react';
import PropTypes from 'prop-types';
import Skeleton from 'react-loading-skeleton';

const AvatarSkeleton = memo(() => (
  <div className="panel skeleton-wrapper" role="status" aria-busy="true" aria-label="Loading user avatar">
    <div>
      <Skeleton
        circle
        width={80}
        height={80}
        baseColor="#ebebeb"
        highlightColor="#f5f5f5"
      />
    </div>
    <div className="skeleton-lines">
      <Skeleton width="100%" height={20} className="skeleton-mb" />
      <Skeleton width="100%" height={20} />
    </div>
  </div>
));

AvatarSkeleton.displayName = 'AvatarSkeleton';

function UserAvatar({
  isLoading = false,
  formState = {},
  handleAvatarClick,
  handleAvatarChange,
  handleImageLoad,
  handleImageError,
  isImageLoading = false,
  hasImageError = false,
  fileInputRef
}) {
  const handleKeyDown = useCallback((e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleAvatarClick();
    }
  }, [handleAvatarClick]);

  if (isLoading) {
    return <AvatarSkeleton />;
  }

  const avatarUrl = formState?.avatarUrl;
  const displayName = formState?.displayName || 'User';

  return (
    <div className="panel skeleton-wrapper">
      <div
        className="avatar-container"
        onClick={handleAvatarClick}
        role="button"
        aria-label="Change profile picture"
        tabIndex={0}
        onKeyDown={handleKeyDown}
        style={{ position: 'relative' }}
      >
        {avatarUrl ? (
          <>
            <img
              src={avatarUrl}
              alt={displayName}
              className="user-avatar"
              onError={handleImageError}
              onLoad={handleImageLoad}
              loading="eager"
              style={isImageLoading ? { visibility: 'hidden' } : {}}
            />
            {isImageLoading && (
              <div 
                className="avatar-loading-container" 
                aria-live="polite" 
                aria-busy="true"
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                  borderRadius: '50%',
                  zIndex: 5
                }}
              >
                <div 
                  className="avatar-loading-spinner"
                  style={{
                    width: '30px',
                    height: '30px',
                    border: '3px solid rgba(0, 0, 0, 0.1)',
                    borderRadius: '50%',
                    borderTopColor: '#3478F6',
                    animation: 'avatar-spin 1s ease-in-out infinite'
                  }}
                ></div>
              </div>
            )}
          </>
        ) : (
          <img
            src="/user_placeholder.svg"
            alt="Default profile"
            className="user-avatar"
          />
        )}
        <div className="avatar-overlay" aria-hidden="true">
          <div className="avatar-edit-icon"></div>
        </div>
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleAvatarChange}
          accept="image/jpeg,image/png,image/gif,image/webp"
          style={{ display: 'none' }}
          aria-label="Upload profile picture"
          tabIndex={-1}
        />
      </div>
      <div className="skeleton-lines">
        <Skeleton width="100%" height={20} className="skeleton-mb" />
        <Skeleton width="100%" height={20} />
      </div>
    </div>
  );
}

UserAvatar.propTypes = {
  isLoading: PropTypes.bool,
  formState: PropTypes.shape({
    avatarUrl: PropTypes.string,
    displayName: PropTypes.string,
    avatarBase64: PropTypes.string
  }),
  handleAvatarClick: PropTypes.func.isRequired,
  handleAvatarChange: PropTypes.func.isRequired,
  handleImageLoad: PropTypes.func,
  handleImageError: PropTypes.func, 
  isImageLoading: PropTypes.bool,
  hasImageError: PropTypes.bool,
  fileInputRef: PropTypes.oneOfType([
    PropTypes.func, 
    PropTypes.shape({ current: PropTypes.instanceOf(Element) })
  ]).isRequired
};

export default memo(UserAvatar);