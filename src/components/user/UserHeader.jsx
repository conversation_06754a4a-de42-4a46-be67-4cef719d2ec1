import React from 'react';
import { Link } from 'react-router-dom';
import Skeleton from 'react-loading-skeleton';

const UserHeader = ({ 
  navigationTarget, 
  handleNavigationAttempt, 
  isLoading, 
  isCurrentUserOperator 
}) => {
  const destinationText = isCurrentUserOperator ? 'Tags' : 'Users';
  
  return (
    <nav className="settings-header-container" aria-label="Main navigation">
      <Link
        to={navigationTarget}
        className="back-arrow"
        onClick={handleNavigationAttempt}
        aria-label={`Back to ${destinationText}`}
      >
        <img src="/arrow_back.svg" alt="" aria-hidden="true" className="backarrow" />
        {' '}
        {isLoading ? (
          <Skeleton width={40} height={18} inline aria-busy="true" />
        ) : (
          destinationText
        )}
      </Link>
    </nav>
  );
};

export default UserHeader;