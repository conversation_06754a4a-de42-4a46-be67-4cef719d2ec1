import React from 'react';
import PropTypes from 'prop-types';

const UserActions = ({
  isLoading,
  uiState = {},
  formState = {},
  handleSave,
  handleDelete,
  isSelfDeletion = false
}) => {
  const isSaveDisabled = isLoading || uiState?.saving || !formState?.hasUnsavedChanges;
  const isDeleteDisabled = isLoading || uiState?.deleting || formState?.isAccountOwner;

  return (
    <>
      {/* Save Changes */}
      <div className="form-group">
        <button
          type="button"
          className={`send-invite-button ${uiState?.saving ? 'saving' : ''}`}
          onClick={handleSave}
          disabled={isSaveDisabled}
          aria-busy={uiState?.saving || false}
          data-testid="save-button"
        >
          {uiState?.saving ? 'Saving...' : 'Save Changes'}
        </button>
      </div>

      {/* Delete User */}
      <div className="form-group">
        <button
          type="button"
          className="send-invite-button delete-button"
          onClick={handleDelete}
          disabled={isDeleteDisabled}
          title={formState?.isAccountOwner ? 'Account owners cannot be deleted.' : ''}
          aria-busy={uiState?.deleting || false}
          data-testid="delete-button"
        >
          {uiState?.deleting ? 'Deleting...' : isSelfDeletion ? 'Delete My Account' : 'Delete User'}
        </button>
        {formState?.isAccountOwner && (
          <div className="info-text warning-text" role="alert">
            <small>Account owners cannot be deleted.</small>
          </div>
        )}
      </div>
    </>
  );
};

UserActions.propTypes = {
  isLoading: PropTypes.bool,
  uiState: PropTypes.shape({
    saving: PropTypes.bool,
    deleting: PropTypes.bool
  }),
  formState: PropTypes.shape({
    hasUnsavedChanges: PropTypes.bool,
    isAccountOwner: PropTypes.bool
  }),
  handleSave: PropTypes.func.isRequired,
  handleDelete: PropTypes.func.isRequired,
  isSelfDeletion: PropTypes.bool
};

export default React.memo(UserActions);