import React, { memo, useMemo } from 'react';
import PropTypes from 'prop-types';
import Skeleton from 'react-loading-skeleton';
import { Input } from '../../components/Input';

const FormSkeleton = memo(() => (
  <div className="panel" aria-busy="true" role="region" aria-label="Loading user form">
    <div className="form-group">
      <label aria-hidden="true">Name</label>
      <Skeleton height={38} borderRadius={3} />
    </div>
    <div className="form-group">
      <label aria-hidden="true">Email</label>
      <Skeleton height={38} borderRadius={3} />
    </div>
    <div className="form-group">
      <label aria-hidden="true">Role</label>
      <Skeleton height={38} borderRadius={3} />
    </div>
    <div className="form-group">
      <small>
        <i>
          <Skeleton width={250} height={12} />
        </i>
      </small>
    </div>
    <div className="form-group">
      <Skeleton height={38} width="100%" borderRadius={3} />
    </div>
    <div className="form-group">
      <Skeleton height={38} width="100%" borderRadius={3} />
    </div>
  </div>
));

FormSkeleton.displayName = 'FormSkeleton';

function UserForm({
  isLoading = false,
  formState = {},
  handleDisplayNameChange,
  handleRoleChange,
  getRoleDescription,
  isCurrentUserOperator = false
}) {
  const safeDisplayName = formState?.displayName || '';
  const safeEmail = formState?.email || '';
  const safeRole = formState?.role || '';
  const isAccountOwner = Boolean(formState?.isAccountOwner);

  const roleOptions = useMemo(() => (
    <>
      {!isAccountOwner && <option value="Operator">Operator</option>}
      <option value="Manager">Manager</option>
      <option value="Admin">Admin</option>
    </>
  ), [isAccountOwner]);

  if (isLoading) {
    return <FormSkeleton />;
  }

  return (
    <div 
      className="panel" 
      role="form" 
      aria-label="User information form"
    >
      <div className="form-group">
        <label htmlFor="display-name">Name</label>
        <Input
          id="display-name"
          className="input-v2"
          value={safeDisplayName}
          onChange={handleDisplayNameChange}
          placeholder="User's full name"
          disabled={isLoading}
          aria-label="User's display name"
          data-testid="user-display-name"
        />
      </div>
      
      <div className="form-group">
        <label htmlFor="email">Email</label>
        <Input
          id="email"
          type="text"
          className="input-v2 readonly-input"
          value={safeEmail}
          placeholder="No email available"
          readOnly
          aria-label="User's email"
          data-testid="user-email"
        />
      </div>
      
      <div className="form-group">
        <label htmlFor="role">Role</label>
        {isCurrentUserOperator ? (
          <Input
            id="role"
            type="text"
            className="input-v2 readonly-input"
            value={safeRole}
            readOnly
            aria-label="User's role"
            data-testid="user-role-readonly"
          />
        ) : (
          <div className="select-wrapper">
            <select
              id="role"
              className="input-v2"
              value={safeRole}
              onChange={handleRoleChange}
              disabled={isLoading}
              aria-label="Select user role"
              data-testid="user-role-select"
            >
              {roleOptions}
            </select>
            <img
              src="/expand_down.svg"
              alt=""
              className="select-arrow-icon"
              aria-hidden="true"
            />
          </div>
        )}
      </div>

      <div className="form-group form-group-left-aligned">
        <small className="info-text">
          <span className="info-container">
            <span className="info-icon" aria-hidden="true">i</span>
            <i>{getRoleDescription}</i>
          </span>
        </small>
      </div>
    </div>
  );
}

UserForm.propTypes = {
  isLoading: PropTypes.bool,
  formState: PropTypes.shape({
    displayName: PropTypes.string,
    email: PropTypes.string,
    role: PropTypes.string,
    isAccountOwner: PropTypes.bool
  }),
  handleDisplayNameChange: PropTypes.func.isRequired,
  handleRoleChange: PropTypes.func.isRequired,
  getRoleDescription: PropTypes.oneOfType([
    PropTypes.string, 
    PropTypes.node
  ]).isRequired,
  isCurrentUserOperator: PropTypes.bool
};

export default memo(UserForm);