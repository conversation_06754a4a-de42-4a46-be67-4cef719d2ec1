import React, { memo } from 'react';
import PropTypes from 'prop-types';

function FeedbackMessage({ message }) {
  if (!message) return null;
  
  const messageText = typeof message === 'object' && message.message 
    ? message.message 
    : String(message);
  
  const isError = /error|fail|cannot\s+delete|invalid|not found|failed/i.test(messageText);
  const messageType = isError ? 'error' : 'success';
  
  return (
    <div
      className={`feedback-message ${messageType} message-mb`}
      role="alert"
      aria-live={isError ? 'assertive' : 'polite'}
    >
      {messageText}
    </div>
  );
}

FeedbackMessage.propTypes = {
  message: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.object
  ])
};

export default memo(FeedbackMessage);