import React, { useState, useMemo } from 'react';

export function ActionButtonPanel({
  buttonConfig,
  isEnabled,
  toggleSectionEnabled,
  isStorefrontEnabled,
  shouldExpandPanel,
  optionalLabelStyle,
  onButtonConfigChange,
  handleContactFormToggle,
  showValidation = false // Add this prop for validation display
}) {
  const [isExpanded, setIsExpanded] = useState(false);

  // Handle toggle expansion
  const handleToggleExpansion = () => {
    setIsExpanded(!isExpanded);
  };

  // Check if the feature is truly enabled (both settings are on)
  const isFeatureEnabled = isEnabled && isStorefrontEnabled;

  // Validation logic - only for border styling (ONLY EMAIL IS MANDATORY)
  const validation = useMemo(() => {
    if (!showValidation || !isEnabled || !isStorefrontEnabled) {
      return { isValid: true, errors: {} };
    }

    const errors = {};
    let isValid = true;

    // ONLY validate email - remove buttonText validation
    if (!buttonConfig.buttonEmail?.trim()) {
      errors.buttonEmail = true;
      isValid = false;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(buttonConfig.buttonEmail)) {
      errors.buttonEmail = true;
      isValid = false;
    }

    return { isValid, errors };
  }, [buttonConfig, isEnabled, isStorefrontEnabled, showValidation]);

  return (
    <div className="settings-input-group-two">
      <div 
        className={`toggle-div-collapsable-1-sub-1 ${isExpanded ? 'expanded' : ''}`}
        onClick={handleToggleExpansion}
        style={{ cursor: 'pointer' }}
      >
        <div className="toggle-text-container-parent">
          <div className="link-primary-icon">
            <img
              src="/storefront_bell.svg"
              alt="Service Button"
              width="16"
              height="16"
            />
          </div>
          <div className="toggle-text-container">
            <div className="settings-input-group-h4">
              Service Button
              {!isStorefrontEnabled && (
                <span style={optionalLabelStyle}>
                  (Requires StoreFront)
                </span>
              )}
            </div>
            <div className="settings-input-group-h4-sub">
              Set action button for this Asset
            </div>
          </div>
        </div>
        <div className="collapse-icon-container">
          <div className="collapse-icon">
            <img
              src={isExpanded ? "/expand_up.svg" : "/expand_down.svg"}
              alt={isExpanded ? "Collapse" : "Expand"}
              className="toggle-icon"
              width="20" 
              height="20"
            />
          </div>
        </div>
      </div>

      {/* Collapsible content section - ALWAYS shows all content when expanded */}
      <div className={`location-collapse-container ${isExpanded ? 'expanded' : ''}`}>
        {/* First divider */}
        <div
          style={{
            borderTop: '1px solid #E5E7EB',
            margin: '12px 0',
          }}
        />
        
        {/* Enable switch row */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between',
          alignItems: 'center',
          paddingBottom: '12px'
        }}>
          <div className="settings-input-group-h4-sub" style={{ fontWeight: 500 }}>
            {/* Optional: Add text here like "Enable Service Button" */}
          </div>
          <label className={`switch ${!isStorefrontEnabled ? 'disabled-switch' : ''}`}>
            <input
              type="checkbox"
              checked={isEnabled === true}
              onChange={() => toggleSectionEnabled('actionButton')}
              disabled={!isStorefrontEnabled}
            />
            <span className="slider round" />
          </label>
        </div>
        
        {/* Second divider - ALWAYS show when expanded */}
        <div
          style={{
            borderTop: '1px solid #E5E7EB',
            margin: '0 0 12px 0',
          }}
        />
        
        {/* Form fields - ALWAYS show when expanded, but disable inputs based on feature state */}
        <div className="input-group">
          <label htmlFor="button-text">
            Button Text
            {/* REMOVED: No longer mandatory */}
          </label>
          <input
            id="button-text"
            className={`input ${!isFeatureEnabled ? 'disabled-input' : ''}`}
            value={buttonConfig.buttonText}
            onChange={(e) => onButtonConfigChange('buttonText', e.target.value)}
            placeholder="e.g., Request Service"
            disabled={!isFeatureEnabled}
          />
        </div>
        
        <div className="input-group">
          <label htmlFor="button-email">
            Recipient Email
            {isFeatureEnabled && <span style={{ color: '#ef4444' }}>*</span>}
          </label>
          <input
            id="button-email"
            className={`input ${!isFeatureEnabled ? 'disabled-input' : ''} ${
              validation.errors.buttonEmail ? 'input-error' : ''
            }`}
            value={buttonConfig.buttonEmail}
            onChange={(e) => onButtonConfigChange('buttonEmail', e.target.value)}
            placeholder="e.g., <EMAIL>"
            disabled={!isFeatureEnabled}
          />
        </div>
        
        <div className="input-group">
          <label htmlFor="button-subject">Email Subject</label>
          <input
            id="button-subject"
            className={`input ${!isFeatureEnabled ? 'disabled-input' : ''}`}
            value={buttonConfig.buttonSubject}
            onChange={(e) => onButtonConfigChange('buttonSubject', e.target.value)}
            placeholder="e.g., Service Request for Asset"
            disabled={!isFeatureEnabled}
          />
        </div>
        
        <div className="input-group">
          <label htmlFor="button-message">Email Message</label>
          <textarea
            id="button-message"
            className={`input ${!isFeatureEnabled ? 'disabled-input' : ''}`}
            rows="3"
            value={buttonConfig.buttonMessage}
            onChange={(e) => onButtonConfigChange('buttonMessage', e.target.value)}
            placeholder="Message to include in the service request email"
            disabled={!isFeatureEnabled}
          />
        </div>
        
        {/* Contact Form Checkbox - ALWAYS show when expanded, but disable based on feature state */}
        <div className="input-group" style={{ marginTop: '20px' }}>
          <div style={{ display: 'flex', alignItems: 'flex-start', gap: '10px' }}>
            <input
              type="checkbox"
              id="contact-form-checkbox"
              checked={buttonConfig.showContactForm !== false}
              onChange={handleContactFormToggle}
              className={`checkbox-input ${!isFeatureEnabled ? 'disabled-checkbox' : ''}`}
              style={{ marginTop: '4px' }}
              disabled={!isFeatureEnabled}
            />
            <div>
              <label 
                htmlFor="contact-form-checkbox" 
                style={{ 
                  cursor: isFeatureEnabled ? 'pointer' : 'not-allowed', 
                  fontWeight: 'bold',
                  color: !isFeatureEnabled ? '#9ca3af' : 'inherit'
                }}
              >
                Contact Form
              </label>
              <div className="settings-input-group-h4-sub">
                Allow users input and contact details
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}