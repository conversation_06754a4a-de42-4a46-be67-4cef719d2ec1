import React, { useState } from 'react';

export function AboutUsPanel({
  aboutUsData,
  isEnabled,
  toggleSectionEnabled,
  isStorefrontEnabled,
  shouldExpandPanel,
  optionalLabelStyle,
  onAboutUsChange
}) {
  const [isExpanded, setIsExpanded] = useState(false);

  // Handle toggle expansion
  const handleToggleExpansion = () => {
    setIsExpanded(!isExpanded);
  };

  // Check if the feature is truly enabled (both settings are on)
  const isFeatureEnabled = isEnabled && isStorefrontEnabled;

  return (
    <div className="settings-input-group-two">
      <div 
        className={`toggle-div-collapsable-1-sub-1 ${isExpanded ? 'expanded' : ''}`}
        onClick={handleToggleExpansion}
        style={{ cursor: 'pointer' }}
      >
        <div className="toggle-text-container-parent">
          <div className="link-primary-icon">
            <img
              src="/storefront_about_us.svg"
              alt="About Us"
              width="16"
              height="16"
            />
          </div>
          <div className="toggle-text-container">
            <div className="settings-input-group-h4">
              About Us
              {!isStorefrontEnabled && (
                <span style={optionalLabelStyle}>
                  (Requires StoreFront)
                </span>
              )}
            </div>
            <div className="settings-input-group-h4-sub">
              Company information
            </div>
          </div>
        </div>
        <div className="collapse-icon-container">
          <div className="collapse-icon">
            <img
              src={isExpanded ? "/expand_up.svg" : "/expand_down.svg"}
              alt={isExpanded ? "Collapse" : "Expand"}
              className="toggle-icon"
              width="20" 
              height="20"
            />
          </div>
        </div>
      </div>

      {/* Collapsible content section - ALWAYS shows all content when expanded */}
      <div className={`location-collapse-container ${isExpanded ? 'expanded' : ''}`}>
        {/* First divider */}
        <div
          style={{
            borderTop: '1px solid #E5E7EB',
            margin: '12px 0',
          }}
        />
        
        {/* Enable switch row */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between',
          alignItems: 'center',
          paddingBottom: '12px'
        }}>
          <div className="settings-input-group-h4-sub" style={{ fontWeight: 500 }}>
            {/* Optional: Add text here like "Enable About Us Section" */}
          </div>
          <label className={`switch ${!isStorefrontEnabled ? 'disabled-switch' : ''}`}>
            <input
              type="checkbox"
              checked={isEnabled === true}
              onChange={() => toggleSectionEnabled('aboutUs')}
              disabled={!isStorefrontEnabled}
            />
            <span className="slider round" />
          </label>
        </div>
        
        {/* Second divider - ALWAYS show when expanded */}
        <div
          style={{
            borderTop: '1px solid #E5E7EB',
            margin: '0 0 12px 0',
          }}
        />
        
        {/* Form fields - ALWAYS show when expanded, but disable inputs based on feature state */}
        <div className="input-group">
          <label htmlFor="company-name">Company Name</label>
          <input
            id="company-name"
            className={`input ${!isFeatureEnabled ? 'disabled-input' : ''}`}
            value={aboutUsData.companyName}
            onChange={(e) => onAboutUsChange('companyName', e.target.value)}
            placeholder="Your company name"
            disabled={!isFeatureEnabled}
          />
        </div>
        
        <div className="input-group">
          <label htmlFor="website">Website</label>
          <input
            id="website"
            className={`input ${!isFeatureEnabled ? 'disabled-input' : ''}`}
            value={aboutUsData.website}
            onChange={(e) => onAboutUsChange('website', e.target.value)}
            placeholder="https://example.com"
            disabled={!isFeatureEnabled}
          />
        </div>
        
        <div className="input-group">
          <label htmlFor="phone-number">Phone Number</label>
          <input
            id="phone-number"
            className={`input ${!isFeatureEnabled ? 'disabled-input' : ''}`}
            value={aboutUsData.phoneNumber}
            onChange={(e) => onAboutUsChange('phoneNumber', e.target.value)}
            placeholder="+****************"
            disabled={!isFeatureEnabled}
          />
        </div>
        
        <div className="input-group">
          <label htmlFor="contact-email">Contact Email</label>
          <input
            id="contact-email"
            className={`input ${!isFeatureEnabled ? 'disabled-input' : ''}`}
            value={aboutUsData.contactEmail}
            onChange={(e) => onAboutUsChange('contactEmail', e.target.value)}
            placeholder="<EMAIL>"
            disabled={!isFeatureEnabled}
          />
        </div>
      </div>
    </div>
  );
}