import React, { useState } from 'react';

export function ProductImagePanel({
  imageUrl,
  imagePreviewUrl,
  onImageUpload,
  isImageUploading,
  isEnabled,
  toggleSectionEnabled,
  isStorefrontEnabled,
  shouldExpandPanel,
  optionalLabelStyle
}) {
  const [isExpanded, setIsExpanded] = useState(false);

  // Handle toggle expansion
  const handleToggleExpansion = () => {
    setIsExpanded(!isExpanded);
  };

  // Check if the feature is truly enabled (both settings are on)
  const isFeatureEnabled = isEnabled && isStorefrontEnabled;

  return (
    <div className="settings-input-group-two">
      <div 
        className={`toggle-div-collapsable-1-sub-1 ${isExpanded ? 'expanded' : ''}`}
        onClick={handleToggleExpansion}
        style={{ cursor: 'pointer' }}
      >
        <div className="toggle-text-container-parent">
          <div className="link-primary-icon">
            <img
              src="/storefront_image.svg"
              alt="Product Image"
              width="16"
              height="16"
            />
          </div>
          <div className="toggle-text-container">
            <div className="settings-input-group-h4">
              Asset Image
              {!isStorefrontEnabled && (
                <span style={optionalLabelStyle}>
                  (Requires StoreFront)
                </span>
              )}
            </div>
            <div className="settings-input-group-h4-sub">
              Configure your Asset image
            </div>
          </div>
        </div>
        <div className="collapse-icon-container">
          <div className="collapse-icon">
            <img
              src={isExpanded ? "/expand_up.svg" : "/expand_down.svg"}
              alt={isExpanded ? "Collapse" : "Expand"}
              className="toggle-icon"
              width="20" 
              height="20"
            />
          </div>
        </div>
      </div>

      {/* Collapsible content section - ALWAYS shows all content when expanded */}
      <div className={`location-collapse-container ${isExpanded ? 'expanded' : ''}`}>
        {/* First divider */}
        <div
          style={{
            borderTop: '1px solid #E5E7EB',
            margin: '12px 0',
          }}
        />
        
        {/* Enable switch row */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between',
          alignItems: 'center',
          paddingBottom: '12px'
        }}>
          <div className="settings-input-group-h4-sub" style={{ fontWeight: 500 }}>
            {/* Optional: Add text here like "Enable Asset Image" */}
          </div>
          <label className={`switch ${!isStorefrontEnabled ? 'disabled-switch' : ''}`}>
            <input
              type="checkbox"
              checked={isEnabled === true}
              onChange={() => toggleSectionEnabled('image')}
              disabled={!isStorefrontEnabled}
            />
            <span className="slider round" />
          </label>
        </div>
        
        {/* Second divider - ALWAYS show when expanded */}
        <div
          style={{
            borderTop: '1px solid #E5E7EB',
            margin: '0 0 12px 0',
          }}
        />
        
        {/* Image upload section - ALWAYS show when expanded, but disable based on feature state */}
        <div className="input-group">
          <div className={`image-upload-container ${!isFeatureEnabled ? 'disabled-upload-container' : ''}`}>
            <div className="image-preview">
              {isImageUploading ? (
                <div className="image-loading-container">
                  <div className="image-loading-spinner"></div>
                  <div className="image-loading-text">Uploading...</div>
                </div>
              ) : (imagePreviewUrl || imageUrl) ? (
                <img 
                  src={imagePreviewUrl || imageUrl} 
                  alt="Product" 
                  className="preview-image"
                />
              ) : (
                <div className="no-image">No image</div>
              )}
            </div>
            <input
              type="file"
              id="image-upload"
              className="file-input"
              accept="image/*"
              onChange={(e) => {
                if (e.target.files[0] && isFeatureEnabled) {
                  onImageUpload(e.target.files[0]);
                }
              }}
              disabled={isImageUploading || !isFeatureEnabled}
            />
            <label 
              htmlFor="image-upload" 
              className={`upload-button ${(isImageUploading || !isFeatureEnabled) ? 'disabled' : ''}`}
              style={(isImageUploading || !isFeatureEnabled) ? { opacity: 0.6, cursor: 'not-allowed' } : {}}
            >
              {!isFeatureEnabled ? 'Feature Disabled' : isImageUploading ? 'Uploading...' : imageUrl ? 'Change Image' : 'Upload Image'}
            </label>
          </div>
        </div>
      </div>
    </div>
  );
}