import React, { useState, useEffect } from 'react';
import StatusList from '../asset/status/StatusList';
import AddStatusForm from '../asset/status/AddStatusForm';
import '../../styles/StatusManagement.css';

export default function StatusManagement({ 
  assetData, 
  statuses, 
  onStatusChange,
  storefrontEnabled = true 
}) {
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    if (!storefrontEnabled) {
      setIsExpanded(false);
    }
  }, [storefrontEnabled]);

  return (
    <div className="settings-input-group-two status-management-container">
      <div 
        className={`toggle-div-collapsable-1-sub-1 ${isExpanded ? 'expanded' : ''} ${!storefrontEnabled ? 'disabled-feature' : ''}`}
        onClick={() => storefrontEnabled && setIsExpanded(!isExpanded)}
      >
        <div className="toggle-text-container-parent">
          <div className="link-primary-icon">
            <img src="/storefront_statuses.svg" alt="Status Management" width="16" height="16" />
          </div>
          <div className="toggle-text-container">
            <div className="settings-input-group-h4">
              Status Management
              {!storefrontEnabled && (
                <span className="feature-dependency-note">
                  (Requires StoreFront)
                </span>
              )}
            </div>
            <div className="settings-input-group-h4-sub">
              Customize status options
            </div>
          </div>
        </div>
        <div className="collapse-icon-container">
          <div className="collapse-icon">
            <img
              src={isExpanded ? "/expand_up.svg" : "/expand_down.svg"}
              alt={isExpanded ? "Collapse" : "Expand"}
              className="toggle-icon"
              width="20" 
              height="20"
            />
          </div>
        </div>
      </div>

      <div className={`location-collapse-container ${isExpanded ? 'expanded' : ''}`}>
        <div
          style={{
            borderTop: '1px solid #E5E7EB',
            margin: '12px 0',
          }}
        />
        <div className="status-layout-container">
          <div className="status-scroll-section">
            <h3 className="status-section-header">Current Statuses</h3>
            <StatusList 
              statuses={statuses} 
              assetData={assetData} 
              onStatusChange={onStatusChange} 
            />
          </div>
          
          <div className="status-add-section">
            <h3 className="status-section-header">Add New Status</h3>
            <AddStatusForm 
              statuses={statuses} 
              onStatusChange={onStatusChange} 
            />
          </div>
        </div>
      </div>
    </div>
  );
}