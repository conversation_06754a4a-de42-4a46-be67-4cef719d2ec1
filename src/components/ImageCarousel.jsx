import React, { useState, useEffect } from "react";
import '../styles/ImageCarousel.css';

const ImageCarousel = ({ images }) => {
  const [current, setCurrent] = useState(0);

  useEffect(() => {
    const next = (current + 1) % images.length;
    const id = setTimeout(() => setCurrent(next), 5000);
    return () => clearTimeout(id);
  }, [current, images]);

  return (
    <div className="carousel">
      {images.map((image, index) => (
        <div 
          key={image.src} 
          className={`carousel-image ${index === current ? "active" : ""}`}
        >
          <img src={image.src} alt=""/>
          {index === current && <div className="carousel-caption">{image.text}</div>}
        </div>
      ))}
      {/* <div className="carousel-indicators">
        {images.map((_, index) => (
          <div
            key={index}
            className={`carousel-indicator ${index === current ? "active" : ""}`}
            onClick={() => setCurrent(index)}
          ></div>
        ))}
      </div> */}
    </div>
  );
};


export default ImageCarousel;
