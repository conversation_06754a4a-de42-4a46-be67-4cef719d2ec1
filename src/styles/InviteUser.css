.invite-user-container {
  max-width: 700px;
  margin: 0 auto;
  padding: 20px;
}

.settings-panel-body-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 20px;
}

.panel {
  background-color: #f9f9f9;
  border-radius: 10px;
  padding: 10px;
  padding-top: 20px;
  padding-bottom: 15px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  text-align: left;
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.disabled-input {
  background-color: #f0f0f0 !important;
  cursor: not-allowed;
  color: #666;
}

/* Send invite button */
.send-invite-button {
  width: 100%;
  padding: 12px;
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.send-invite-button:hover {
  background-color: #3367d6;
}

.send-invite-button:disabled {
  background-color: #a4c1f4;
  cursor: not-allowed;
}

/* Feedback message styling */
.feedback-message {
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 4px;
  text-align: center;
}

.feedback-message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.feedback-message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.tag-list-top-margin {
  margin-top: 20px;
}

.skeleton-item-spacing {
  margin-top: 4px;
}

.form-group-left-aligned {
  text-align: left;
}

.info-text {
  display: block;
  text-align: left;
}

.info-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.info-icon {
  display: inline-flex;
  margin-right: 5px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 1px solid #777;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  font-weight: bold;
  color: #777;
  flex-shrink: 0;
}

.select-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.select-wrapper select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 100%;
  padding-right: 40px;
}

.select-arrow-icon {
  position: absolute;
  right: 8px;
  top: calc(3px + (43px / 2));
  transform: translateY(-50%);
  pointer-events: none;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Mobile optimization */
@media (max-width: 768px) {
  .panel {
    padding: 15px;
  }
}