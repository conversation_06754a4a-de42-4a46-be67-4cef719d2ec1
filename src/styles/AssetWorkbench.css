/* Scoped CSS for AssetWorkbench component */

/* Container styles */
.asset-workbench {
  padding-bottom: 80px; /* Space for fixed footer */
  min-height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* Progress Banner styles */
.asset-workbench-progress-banner {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 12px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #edf2ff;
  border-bottom: 1px solid #d4e3fc;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  animation: slide-down 0.3s ease-out forwards;
  transform-origin: top;
}

.asset-workbench-progress-banner.success {
  background-color: #e6f7ee;
  border-bottom: 1px solid #d4f3e6;
}

.asset-workbench-progress-banner.error {
  background-color: #fff3f3;
  border-bottom: 1px solid #ffcdd2;
}

@keyframes slide-down {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(0);
  }
}

.asset-workbench-progress-content {
  display: flex;
  align-items: center;
  flex-grow: 1;
  gap: 12px;
}

.asset-workbench-progress-message {
  font-size: 14px;
  font-weight: 500;
  color: #3062d4;
}

.asset-workbench-progress-banner.success .asset-workbench-progress-message {
  color: #0c9d58;
}

.asset-workbench-progress-banner.error .asset-workbench-progress-message {
  color: #d32f2f;
}

.asset-workbench-progress-bar-container {
  flex-grow: 1;
  max-width: 200px;
  height: 8px;
  background-color: #d4e3fc;
  border-radius: 4px;
  overflow: hidden;
}

.asset-workbench-progress-bar {
  height: 100%;
  background-color: #3062d4;
  border-radius: 4px;
  transition: width 0.3s ease-in-out;
}

.asset-workbench-progress-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.asset-workbench-progress-icon.success {
  color: #0c9d58;
}

.asset-workbench-progress-icon.error {
  color: #d32f2f;
}

.asset-workbench-progress-close {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 12px;
  transition: background-color 0.2s ease;
}

.asset-workbench-progress-close:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Panel styling */
.asset-workbench-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.asset-workbench-panel-body {
  text-align: left;
  display: flex;
  flex-direction: column;
  padding: 0 20px 80px 20px; /* Horizontal padding and bottom padding */
}

/* Header styling */

.asset-workbench-panel-body-title {
  margin-top: 20px;
  margin-bottom: 10px;
}

.asset-workbench-toggle-title-body {
  color: #4e545d;
  font-size: 14px;
  line-height: 1.4;
  margin: 0;
  text-align: left;
}

.asset-workbench-header-block-group {
  margin-top: 20px;
  margin-bottom: 16px;
}

.asset-workbench-header-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.asset-workbench-header-block-group h1 {
  font-size: 24px;
  color: #333;
  margin: 0 0 8px 0;
  text-align: left;
}

.asset-workbench-split-line {
  border-top: 1px solid #e0e0e0;
  margin: 20px 0;
}

/* Editor section styling */
.asset-workbench-editor-section {
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  padding: 20px;
  margin-top: 20px;
}

.asset-workbench-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.asset-workbench-editor-header h2 {
  font-size: 18px;
  color: #333;
  margin: 0;
}

.asset-workbench-editor-actions {
  display: flex;
  gap: 8px;
}

.asset-workbench-back-button,
.asset-workbench-source-button {
  background-color: #ffffff;
  color: #3062d4;
  border: 1px solid #3062d4;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.asset-workbench-back-button:hover,
.asset-workbench-source-button:hover {
  background-color: #edf2ff;
}

.source-icon {
  margin-right: 2px;
}

/* Responsive styles */
@media (max-width: 900px) {
  .asset-workbench-panel-body {
    padding-bottom: 60px; /* Extra padding on smaller screens */
  }

  .asset-workbench-dropzone {
    margin: 10px 0; /* Reduce margin to save vertical space */
    padding: 15px; /* Slightly more compact dropzone */
  }

  .asset-workbench-progress-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .asset-workbench-progress-bar-container {
    max-width: 100%;
    width: 100%;
  }
}

/* Dropzone styling */
.asset-workbench-dropzone {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border-width: 2px;
  border-radius: 2px;
  border-color: #3062d4;
  border-style: dashed;
  color: #4e545d;
  outline: none;
  transition: border .24s ease-in-out;
  cursor: pointer;
  margin: 20px 0;
  background-color: #edf2ff;
  min-height: 140px; /* Ensure minimum height */
  justify-content: center; /* Center content vertically */
}

.asset-workbench-dropzone:hover {
  border-color: #3a9edf;
}

.asset-workbench-upload-file-icon {
  height: 35px;
  display: block; /* Prevent any extra space below image */
}

/* Loading template indicator */
.asset-workbench-loading-template {
  text-align: center;
  padding: 20px;
  color: #666;
}

/* Fixed footer styling */
.asset-workbench-fixed-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-top: 1px solid #e0e0e0;
  padding: 16px 24px;
  z-index: 100;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
}

.asset-workbench-fixed-footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.asset-workbench-fixed-footer-file {
  color: #666;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.asset-workbench-fixed-footer-button {
  background-color: #3062d4;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: background-color 0.2s ease;
}

.asset-workbench-fixed-footer-button:hover {
  background-color: #2754b9;
}

.asset-workbench-fixed-footer-button:disabled {
  background-color: #e0e0e0;
  cursor: not-allowed;
}

/* Acknowledgement checkbox styling */
.asset-workbench-update-acknowledge-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #4e545d;
  margin-top: 6px;
}

.asset-workbench-update-acknowledge-label input[type="checkbox"] {
  margin: 0;
  cursor: pointer;
}

.asset-workbench-update-acknowledge-label span {
  font-style: italic;
}

/* Uploaded file details styling */
.asset-workbench .uploaded-file-details {
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  margin-top: 20px;
  padding: 12px 16px;
}

/* File header styling */
.asset-workbench .file-header {
  display: flex;
  justify-content: space-between;
  align-items: center; /* Ensure vertical alignment */
  margin-bottom: 12px;
}

.asset-workbench .file-name {
  font-size: 13px;
}

.asset-workbench .file-info {
  display: flex;
  align-items: center;
  gap: 8px; /* Reduced from 15px for tighter spacing */
}

.asset-workbench .file-icon {
  display: flex; /* Added to ensure icon aligns properly */
  align-items: center;
}

.asset-workbench .file-icon img {
  width: 24px;
  height: 24px;
  display: block; /* Prevent any extra space below image */
}

.asset-workbench .close-button {
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex; /* Added to ensure icon aligns properly */
  align-items: center;
  justify-content: center;
  height: 24px; /* Match the height of the file icon */
}

.asset-workbench .close-button:hover {
  background-color: #f5f5f5;
}

/* Address toggle styling */
.asset-workbench .address-toggle {
  margin-right: 20px;
}

.asset-workbench .toggle-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #4e545d;
}

.asset-workbench .toggle-label input[type="checkbox"] {
  margin: 0;
}

.asset-workbench .geocoding-status {
  margin-left: 8px;
  color: #3062d4;
  font-size: 12px;
  font-style: italic;
}

.asset-workbench .toggle-label input[type="checkbox"]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Table styling */
.asset-workbench .table-container {
  text-align: left;
  overflow-x: auto; /* Allow horizontal scrolling for tables on small screens */
  margin-bottom: 20px; /* Reduced space to prevent pushing content off screen */
}

.asset-workbench .excel-table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  margin-top: 10px;
  font-size: 12px;
}

.asset-workbench .excel-table th,
.asset-workbench .excel-table td {
  border: 1px solid #dee2e6;
  text-align: left;  /* Explicitly set for both th and td */
}

.asset-workbench .excel-table th {
  background-color: #f8f9fa;
  font-weight: 500;
  color: #495057;
  padding: 6px 8px;
  height: 36px;
}

.asset-workbench .excel-table td {
  max-width: 0;  /* This forces truncation in combination with overflow hidden */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0; /* Remove td padding */
  position: relative; /* For absolute positioning if needed */
  height: 29px; /* Fixed height for all cells */
  vertical-align: middle; /* Vertically center all content */
}

.asset-workbench .excel-table tr {
  height: 29px; /* Fixed height for all rows */
}

.asset-workbench .excel-table tr:hover {
  background-color: #f8f9fa;
}

/* Column widths */
.asset-workbench .excel-table th:nth-child(1),
.asset-workbench .excel-table td:nth-child(1) {
  width: 15%; /* ID column */
}

.asset-workbench .excel-table th:nth-child(2),
.asset-workbench .excel-table td:nth-child(2) {
  width: 10%; /* Status column */
}

.asset-workbench .excel-table th:nth-child(3),
.asset-workbench .excel-table td:nth-child(3) {
  width: 15%; /* Asset Name */
}

.asset-workbench .excel-table th:nth-child(4),
.asset-workbench .excel-table td:nth-child(4),
.asset-workbench .excel-table th:nth-child(5),
.asset-workbench .excel-table td:nth-child(5) {
  width: 15%; /* Public and Private URLs */
}

.asset-workbench .excel-table th:nth-child(6),
.asset-workbench .excel-table td:nth-child(6) {
  width: 15%; /* Asset Description */
}

.asset-workbench .excel-table th:nth-child(7),
.asset-workbench .excel-table td:nth-child(7),
.asset-workbench .excel-table th:nth-child(8),
.asset-workbench .excel-table td:nth-child(8) {
  width: 10%; /* Latitude and Longitude */
}

/* Status badge styling */
.asset-workbench .status-badge-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  min-width: unset;
}

.asset-workbench .status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
  line-height: 1.2;
  text-align: center;
  white-space: nowrap;
  height: 16px;
}

.asset-workbench .status-badge.status-new {
  background-color: #e6f7ee;
  color: #0c9d58;
  border: 1px solid #d4f3e6;
}

.asset-workbench .status-badge.status-update {
  background-color: #e8f0fe;
  color: #3062d4;
  border: 1px solid #d4e3fc;
}

/* Editable cell styling */
.asset-workbench .cell {
  padding: 6px 8px;
  cursor: text;
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center; /* Vertically center text */
  box-sizing: border-box;
  line-height: 1.2;
}

.asset-workbench .cell-input {
  width: 100%;
  height: 100%;
  padding: 6px 8px;
  font: inherit;
  color: inherit;
  background: #edf2ff;
  border: none;
  outline: none;
  display: block;
  margin: 0; /* Remove default input margins */
  box-sizing: border-box; /* Include padding in width calculation */
  line-height: 1.2;
}

/* Add row button styling */
.asset-workbench .add-row-container {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.asset-workbench .add-row-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background-color: #edf2ff;
  color: #3062d4;
  border: 1px dashed #3062d4;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.asset-workbench .add-row-button:hover {
  background-color: #d4e3fc;
}

.asset-workbench .add-row-icon {
  font-size: 18px;
  font-weight: bold;
}

.asset-workbench .add-row-text {
  font-size: 14px;
  font-weight: 500;
}

/* Remove row button styling */
.asset-workbench .actions-column {
  width: 60px;
  text-align: center;
}

.asset-workbench .row-actions {
  text-align: center;
}

.asset-workbench .remove-row-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
  background-color: transparent;
  color: #888;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0;
  opacity: 0.6;
}

.asset-workbench .remove-row-button:hover {
  background-color: #f1f1f1;
  color: #d32f2f;
  opacity: 1;
}

.asset-workbench .remove-row-icon {
  font-size: 16px;
  font-weight: bold;
  line-height: 1;
}

/* Validation errors */
.asset-workbench .validation-errors {
  margin: 10px 0;
  padding: 12px;
  background-color: #fff3f3;
  border: 1px solid #ffcdd2;
  border-radius: 4px;
}

.asset-workbench .validation-errors h4 {
  color: #d32f2f;
  margin: 0 0 8px 0;
  font-size: 14px;
}

.asset-workbench .validation-error {
  color: #d32f2f;
  font-size: 12px;
  margin: 4px 0;
}

/* Error row highlighting */
.asset-workbench .excel-table tr.has-error {
  background-color: #fff3f3;
}

.asset-workbench .excel-table tr.has-error:hover {
  background-color: #ffe9e9;
}

/* Better responsiveness for small screens */
@media (max-width: 640px) {
  .asset-workbench .asset-workbench-fixed-footer-content {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .asset-workbench .asset-workbench-fixed-footer-button {
    width: 100%;
  }

  /* Improve vertical spacing for smaller screens */
  .asset-workbench .asset-workbench-panel-body-title {
    margin-top: 16px;
    margin-bottom: 8px;
  }
}