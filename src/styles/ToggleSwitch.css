.toggle-switch-container {
  display: flex;
  justify-content: center;
  padding-bottom: 0px;
}

.toggle-switch {
  position: relative;
  display: flex;
  background-color: #ededed;
  border-radius: 8px 8px 0 0;
  padding: 6px;
  width: 100%;
}

.toggle-option {
  flex: 1;
  text-align: center;
  padding: 10px;
  border: none;
  background: none;
  cursor: pointer;
  position: relative;
  z-index: 1;
  transition: color 0.3s ease;
  -webkit-tap-highlight-color: transparent; /* Removes tap highlight on mobile */
  user-select: none; /* Prevents text selection */
}

/* Prevent visual changes on click */
.toggle-option:active {
  background: none;
  outline: none;
  transform: none;
}

.sliders {
  position: absolute;
  top: 5px;
  left: 0;
  width: calc((100% - 10px) / 3);
  height: calc(100% - 10px);
  background-color: white;
  border-radius: 6px;
  transition: transform 0.2s ease;
  will-change: transform;
}

.toggle-switch button.toggle-option {
  flex: 1;
  text-align: center;
  padding: 10px;
  border: none;
  background: none;
  cursor: pointer;
  position: relative;
  z-index: 1;
  transition: color 0.3s ease;
  font-family: inherit;
  font-size: 14px;
  font-weight: 500;
  color: #000000;
  outline: none;
  margin: 0;
  -webkit-tap-highlight-color: transparent; /* Removes tap highlight on mobile */
}

/* Prevent focus outline */
.toggle-switch button.toggle-option:focus {
  outline: none;
}

/* Prevent any visual changes on click */
.toggle-switch button.toggle-option:active {
  background: none;
  outline: none;
  transform: none;
}