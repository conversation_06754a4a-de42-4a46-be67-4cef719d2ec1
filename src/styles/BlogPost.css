/* BlogPost.css - Styling for blog post component */

/* Main container with blog prefix to avoid CSS leakage */
.blog {
  font-family: 'E<PERSON> Garamond', Georgia, serif;
  color: #121212; /* Darker text color for better contrast, closer to true black */
  line-height: 1.6;
  text-align: left;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.blog .blog-post-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 40px 20px;
}

/* Banner image styles */
.blog .blog-post-banner {
  margin: 0 0 40px;
  overflow: hidden;
  max-height: 450px;
  position: relative;
}

.blog .blog-post-banner-image {
  width: 100%;
  height: 450px;
  object-fit: cover;
  display: block;
}

/* Header styles */
.blog .blog-post-header {
  margin-bottom: 40px;
}

.blog .blog-post-title {
  font-family: 'Playfair Display', serif;
  font-size: 2.8rem;
  font-weight: 800;
  margin-bottom: 25px;
  line-height: 1.2;
  color: var(--primary-color);
  letter-spacing: -0.02em;
}

.blog .blog-post-meta {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  position: relative;
  justify-content: space-between;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

.blog .blog-post-author-image-container {
  margin-right: 15px;
}

.blog .blog-post-author-image {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.blog .blog-post-author-info {
  display: flex;
  flex-direction: column;
  min-width: 0;
  overflow: hidden;
}

.blog .blog-post-meta-left {
  display: flex;
  align-items: center;
  min-width: 0; /* Allow flex item to shrink below content size */
  flex: 1;
}

.blog .blog-post-meta-buttons {
  display: flex;
  flex-direction: row;
  gap: 16px;
  align-items: center;
}

.blog .blog-post-author-name {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.blog .blog-post-date {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  font-size: 0.9rem;
  color: var(--secondary-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Audio player container */
.blog .blog-post-audio-player {
  margin-bottom: 30px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

/* Loading and error states */
.blog .blog-post-loading,
.blog .blog-post-error {
  padding: 40px 20px;
  text-align: center;
}

.blog .blog-post-error h2 {
  color: #d32f2f;
  margin-bottom: 15px;
}

/* Content styles */
.blog .blog-post-content {
  font-size: 20px; /* Changed from 1.1rem to 20px to match The Atlantic */
  line-height: 1.6; /* Slightly tighter line height */
  color: #121212; /* Darker text color for better contrast */
  letter-spacing: -0.01em; /* Slightly tighter letter spacing */
}

/* Heading styles */
/* H1 remains Playfair Display for main title */
/* H2-H4 use EB Garamond by default, but can be changed with the HeadingFontTester */

/* Default heading styles */
.blog .blog-post-content h2 {
  font-family: 'Playfair Display', serif;
  font-size: 32px; /* Converted from rem to px */
  font-weight: 600;
  margin-top: 60px; /* Increased from 50px to create more space above */
  margin-bottom: 15px; /* Decreased from 20px to bring closer to its content */
  color: #121212;
  letter-spacing: -0.02em;
  line-height: 1.2;
  text-transform: uppercase;
  font-variant: normal;
  font-stretch: condensed;
    transform: scaleY(1.1);
    transform-origin: bottom;
}

.blog .blog-post-content h3 {
  font-family: 'Playfair Display', serif;
  font-size: 26px; /* Converted from rem to px */
  font-weight: 700;
  margin-top: 35px;
  margin-bottom: 15px;
  color: #121212;
  letter-spacing: -0.01em;
  line-height: 1.3;
  text-transform: uppercase;
  font-variant: normal;
}

.blog .blog-post-content h4 {
  font-family: 'Playfair Display', serif;
  font-size: 22px; /* Converted from rem to px */
  font-weight: 700;
  margin-top: 25px;
  margin-bottom: 10px;
  color: #121212;
  letter-spacing: -0.01em;
  line-height: 1.4;
  text-transform: uppercase;
  font-variant: normal;
}

/* Paragraph styles */
.blog .blog-post-content p {
  margin-bottom: 24px;
  text-align: left;
  font-size: 20px; /* Match content font size */
  line-height: 1.6;
  letter-spacing: -0.01em; /* Slightly tighter letter spacing */
}

/* Custom paragraph and strong text styling */
.blog .blog-post-paragraph {
  margin-bottom: 24px;
  text-align: left;
  font-size: 20px; /* Match content font size */
  line-height: 1.6;
  letter-spacing: -0.01em; /* Slightly tighter letter spacing */
}

/* Drop cap styling for the first letter of paragraphs */
.blog .blog-post-paragraph:not(.no-drop-cap):first-letter {
  font-family: 'Playfair Display', serif;
  float: left;
  font-size: 3.2rem; /* Reduced from 4.5rem to make it 2 lines tall */
  line-height: 0.85;
  padding-top: 0.05em;
  padding-right: 0.2em; /* Further reduced padding for better spacing */
  padding-left: 0;
  margin-right: 0.02em; /* Reduced by a sliver */
  margin-bottom: -0.05em;
  color: var(--primary-color, #333);
  font-weight: 700;
  text-transform: uppercase; /* Ensure the drop cap is uppercase for consistency */
  /* Add a subtle shadow for depth */
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.05);
}

/* Add a bit more spacing after paragraphs with drop caps */
.blog .blog-post-paragraph:not(.no-drop-cap) {
  margin-bottom: 28px;
}

/* Ensure text wraps properly around the drop cap */
.blog .blog-post-paragraph:not(.no-drop-cap) {
  min-height: 3.2rem; /* Reduced to match the new font size */
}

/* For browsers that support initial-letter */
@supports (initial-letter: 2) or (-webkit-initial-letter: 2) {
  .blog .blog-post-paragraph:not(.no-drop-cap):first-letter {
    float: none;
    font-size: inherit;
    line-height: inherit;
    /* Keep padding and margin consistent with non-initial-letter implementation */
    padding-top: 0.05em;
    padding-right: 0.2em; /* Further reduced padding to match base styling */
    padding-left: 0;
    margin-right: 0.02em;
    margin-bottom: -0.05em;
    -webkit-initial-letter: 2; /* Changed from 3 to 2 */
    initial-letter: 2; /* Changed from 3 to 2 */
  }

  /* Reset min-height when using initial-letter */
  .blog .blog-post-paragraph:not(.no-drop-cap) {
    min-height: auto;
  }

  /* Ensure consistent styling for larger screens with initial-letter support */
  @media (min-width: 769px) {
    .blog .blog-post-paragraph:not(.no-drop-cap):first-letter {
      padding-top: 0.05em;
      padding-right: 0.2em; /* Further reduced padding for larger screens */
      padding-left: 0;
      margin-right: 0.02em;
      margin-bottom: -0.05em;
    }
  }

  /* Ensure consistent styling for smaller screens with initial-letter support */
  @media (max-width: 768px) {
    .blog .blog-post-paragraph:not(.no-drop-cap):first-letter {
      padding-top: 0.1em;
      padding-right: 0.16em;
      margin-right: 0.02em;
    }
  }
}

/* Responsive adjustments for drop caps */
@media (max-width: 768px) {
  .blog .blog-post-paragraph:not(.no-drop-cap):first-letter {
    font-size: 2.8rem; /* Reduced for smaller screens */
    padding-top: 0.1em;
    padding-right: 0.16em; /* Reduced by a sliver for smaller screens */
    margin-right: 0.02em; /* Reduced by a sliver for smaller screens */
  }

  .blog .blog-post-paragraph:not(.no-drop-cap) {
    min-height: 2.8rem; /* Adjusted to match font size */
  }
}

/* Ensure consistent drop cap styling for larger screens */
@media (min-width: 769px) {
  .blog .blog-post-paragraph:not(.no-drop-cap):first-letter {
    font-size: 3.2rem;
    padding-top: 0.05em;
    padding-right: 0.2em; /* Further reduced padding for larger screens */
    padding-left: 0;
    margin-right: 0.02em;
    margin-bottom: -0.05em;
  }

  .blog .blog-post-paragraph:not(.no-drop-cap) {
    min-height: 3.2rem;
  }
}

/* Styling for standalone strong elements that act as section headers */
.blog .blog-post-strong-text {
  display: block;
  margin-top: 20px;
  margin-bottom: 12px;
  font-weight: 700;
}

/* Default styling for regular inline strong text */
.blog .blog-post-content strong {
  font-weight: 700;
  display: inline; /* Ensure inline display for regular bold text */
}

/* Special styling for "Practical Application" sections */
.blog .blog-post-practical-application {
  color: var(--primary-color);
  font-size: 1.15rem;
  margin-top: 24px;
  margin-bottom: 16px;
  display: block;
}

/* Add spacing after strong text that's followed by a paragraph */
.blog .blog-post-content strong + p {
  margin-top: 16px;
}

/* Add specific spacing after Practical Application sections */
.blog .blog-post-practical-application + p {
  margin-top: 16px;
  padding-left: 16px;
  border-left: 3px solid #f0f0f0;
}

/* Ensure proper spacing between paragraphs */
.blog .blog-post-content p + p {
  margin-top: 24px;
}

/* Reduce spacing between h2 headings and the paragraphs that follow them */
.blog .blog-post-content h2 + p {
  margin-top: 8px; /* Reduced top margin to bring paragraph closer to h2 */
}

/* List styles */
.blog .blog-post-content ul,
.blog .blog-post-content ol {
  margin-bottom: 20px;
  padding-left: 25px;
}

.blog .blog-post-content li {
  margin-bottom: 10px;
}

/* Blockquote styles */
.blog .blog-post-content blockquote {
  border-left: 4px solid var(--accent-color);
  padding-left: 20px;
  margin-left: 0;
  margin-right: 0;
  margin-bottom: 20px;
  font-style: italic;
  color: var(--secondary-color);
}

/* Code block styles */
.blog .blog-post-content pre {
  background-color: #f8f8f8;
  border-radius: 5px;
  padding: 15px;
  overflow-x: auto;
  margin-bottom: 20px;
  font-family: 'Courier New', Courier, monospace;
}

.blog .blog-post-content code {
  font-family: 'Courier New', Courier, monospace;
  background-color: #f0f0f0;
  padding: 2px 5px;
  border-radius: 3px;
  font-size: 0.9em;
}

.blog .blog-post-content pre code {
  background-color: transparent;
  padding: 0;
}

/* Image styles */
.blog .blog-post-content img,
.blog .blog-post-content-image {
  max-width: 100%;
  height: auto;
  border-radius: 5px;
  margin-bottom: 20px;
  display: block;
}

/* Table styles */
.blog .blog-post-content table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.blog .blog-post-content th,
.blog .blog-post-content td {
  border: 1px solid #ddd;
  padding: 12px;
  text-align: left;
}

.blog .blog-post-content th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.blog .blog-post-content tr:nth-child(even) {
  background-color: #f9f9f9;
}

/* Ensure horizontal rules are consistently styled */
/* Ensure horizontal rules are consistently styled */
.blog .blog-post-divider,
.blog .blog-post-content hr {
  border: 0;
  height: 1px;
  background-color: #e0e0e0;
  margin: 40px 0;
  width: 100%;
}

/* Link styles */
.blog .blog-post-content a {
  color: var(--accent-color);
  text-decoration: none;
  transition: color 0.2s ease;
}

.blog .blog-post-content a:hover {
  color: var(--accent-highlight);
  text-decoration: underline;
}

/* Responsive styles */
@media (max-width: 768px) {
    .blog .blog-post-container {
    padding: 20px 15px;
  }

  /* Make banner edge-to-edge on smaller screens */
  .blog .blog-post-banner {
    margin: 0 -15px 30px;
  }

  .blog .blog-post-title {
    font-size: 2rem;
  }

  .blog .blog-post-content {
    font-size: 18px; /* Slightly smaller on mobile but still readable */
  }

  .blog .blog-post-paragraph {
    font-size: 18px; /* Match content font size */
  }

  .blog .blog-post-content h2 {
    font-size: 28px;
    margin-top: 50px; /* Adjusted for smaller screens but maintaining proportions */
    margin-bottom: 12px; /* Adjusted for smaller screens but maintaining proportions */
  }

  .blog .blog-post-content h3 {
    font-size: 24px;
  }

  .blog .blog-post-content h4 {
    font-size: 20px;
  }

  /* Maintain reduced spacing between h2 headings and paragraphs on smaller screens */
  .blog .blog-post-content h2 + p {
    margin-top: 6px; /* Slightly smaller on mobile to maintain proportions */
  }
}



/* Blog post action buttons */
.blog .blog-post-action-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  padding: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0.7;
}

.blog .blog-post-action-button img {
  width: 24px;
  height: 24px;
}

.blog .blog-post-action-button:hover {
  opacity: 1;
  transform: translateY(-1px);
}

.blog .blog-post-action-button:active {
  transform: translateY(0);
}

/* Toast notification for copy confirmation */
.blog .copy-toast {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 0.9rem;
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

.blog .copy-toast.visible {
  opacity: 1;
}



/* Additional adjustments for very small screens */
@media (max-width: 480px) {
  .blog .blog-post-banner {
    margin-top: 0;
  }

  .blog .blog-post-banner-image {
    height: 300px; /* Reduced height for very small screens */
  }

  .blog .blog-post-author-name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .blog .blog-post-date {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .blog .blog-post-meta {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
  }

  .blog .blog-post-meta-left {
    flex: 1;
    min-width: 0; /* Allow flex item to shrink below content size */
  }

  .blog .blog-post-author-info {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .blog .blog-post-meta-buttons {
    flex-shrink: 0;
    gap: 8px;
  }
}

/* Blog post reactions component */
.blog .blog-reactions {
  margin-top: 50px;
  margin-bottom: 50px;
}

/* Related posts container */
.blog .blog-related-posts-container {
  margin-top: 30px;
  margin-bottom: 50px;
  width: 100%;
  max-width: 800px;
}
