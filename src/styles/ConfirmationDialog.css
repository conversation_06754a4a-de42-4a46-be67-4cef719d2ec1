.confirmation-dialog-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.2s ease-out;
  }
  
  .confirmation-dialog {
    background: white;
    border-radius: 10px;
    padding: 24px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: scaleIn 0.2s ease-out;
  }
  
  .confirmation-dialog-header h3 {
    margin-top: 0;
    color: #333;
    font-size: 18px;
  }
  
  .confirmation-dialog-content {
    margin: 16px 0;
  }
  
  .confirmation-dialog-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
  
  .confirmation-dialog-button {
    padding: 10px 16px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    border: none;
  }
  
  .cancel-button {
    background-color: #f5f5f5;
    color: #333;
  }
  
  .confirm-button {
    background-color: #4285f4;
    color: white;
  }
  
  .danger-button {
    background-color: #f44336;
    color: white;
  }
  
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes scaleIn {
    from { transform: scale(0.9); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
  }
  
  /* Mobile optimization */
  @media (max-width: 768px) {
    .confirmation-dialog {
      width: 85%;
      padding: 20px;
    }
  }