/* Original styles */

.document-upload-button {
  display: inline-block;
  background-color: #3478F6;
  color: white;
  padding: 10px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  transition: background-color 0.2s;
}

.document-upload-button:hover {
  background-color: #2c64c9;
}

.document-upload-button.uploading {
  background-color: #9E9E9E;
  cursor: default;
}

.document-error-message {
  color: #d32f2f;
  margin-top: 8px;
  font-size: 13px;
}

.document-list {
  margin-top: 15px;
  max-height: 600px;  /* Increased to accommodate expanded items */
  overflow-y: auto;
}

.document-list-item {
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
  overflow: hidden;
  cursor: pointer;
}

.document-list-item.expanded {
  cursor: default;
}

.document-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
}

.document-info {
  flex: 1;
}

.document-name {
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 2px;
  word-break: break-word;
}

.document-date {
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 5px;
}

.document-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.document-action-button {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.document-action-button:hover {
  background-color: #e0e0e0;
}

.document-action-button.remove:hover {
  background-color: #ffebee;
}

/* New styles for access level dropdown */
.document-access-level {
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  background-color: white;
  font-size: 12px;
  cursor: pointer;
  transition: border-color 0.2s;
  outline: none;
}

.document-access-level:hover {
  border-color: #3478F6;
}

.document-access-level:focus {
  border-color: #3478F6;
  box-shadow: 0 0 0 2px rgba(52, 120, 246, 0.2);
}

/* Document preview container styles */
.document-preview-container {
  border-top: 1px solid #e0e0e0;
  padding: 10px;
}

/* A4 proportions container (1:1.414 ratio) */
.document-preview {
  width: 100%;
  max-width: 420px;
  height: 594px; /* A4 proportion based on max-width (420 * 1.414) */
  margin: 0 auto;
  background-color: white;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.preview-pdf {
  width: 100%;
  height: 100%;
  border: none;
}

.document-preview-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  font-size: 14px;
  color: #666;
}

.document-preview-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  font-size: 14px;
  color: #d32f2f;
}

.preview-unsupported {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 20px;
  text-align: center;
}

.preview-download-button {
  background-color: #3478F6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  margin-top: 15px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.preview-download-button:hover {
  background-color: #2c64c9;
}

.disabled-switch {
  opacity: 0.6;
  cursor: not-allowed;
}

.disabled-switch .slider {
  background-color: #ccc !important;
}

.disabled-switch input:checked + .slider {
  background-color: #aaa !important;
}

/* New styles for viewer mode */
.settings-input-group-two.viewer-mode {
  margin-bottom: 16px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.viewer-mode-header {
  cursor: pointer;
  transition: background-color 0.2s;
}

.viewer-mode-header:hover {
  background-color: #f5f5f5;
}

.expand-collapse-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.document-actions.viewer-mode {
  /* Only show the view button in viewer mode */
  justify-content: flex-end;
}

.no-documents-message {
  padding: 15px 0;
  color: #666;
  font-size: 14px;
  text-align: center;
  border-top: 1px solid #e0e0e0;
}