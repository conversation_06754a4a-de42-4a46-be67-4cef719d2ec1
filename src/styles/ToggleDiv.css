/* ToggleDiv.css */

.toggle-div {
    margin-bottom: 25px;
    border-radius: 10px;
    cursor: pointer;
    background-color: #f9f9f9;
    color: #888888;
    transition: all 0.3s ease;
    /* border: 1px solid #dedede; */
}

.toggle-div.active {
    background-color: #ffffff;
    color: #303030;
    border: 3px solid #1a73e8;
}

.toggle-icon {
    font-size: 40px !important;
    margin: 15px;
    color: #747474;
}

.toggle-title-text {
    color: #000;
    font-size: 16px;
    font-weight: 500;
    padding-bottom: 3px;
}

.toggle-title-body {
    font-weight: 400;
}
.url-input {
    margin-bottom: 0px;
    margin-top: 0px;
}

.toggle-div-text-and-icon {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.spacer {
    flex-grow: 1;
}

.toggle-div-main {
    border-bottom: 1px solid #dedede;
    padding: 10px;
}

.button-div-main {
    border-bottom: 1px solid #dedede;
    padding-left: 25px;
    padding-right: 25px;
    padding-top: 10px;
    padding-bottom: 10px;
}

.toggle-div-collapsable-1 {
    padding-left: 25px;
    padding-right: 25px;
    padding-top: 20px;
}

.toggle-div-collapsable-1-sub {
    border-bottom: 1px solid #dedede;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 20px;
}

.toggle-div-collapsable-2 {
    text-align: left;
    padding-top: 10px;
    padding-left: 25px;
    padding-right: 25px;
    padding-bottom: 25px;
}

.label {
    padding: 7px;
}

.status-text {
    background-color: #DDF9EA;
    padding: 3px;
    padding-left: 6px;
    padding-right: 6px;
    border-radius: 6px;
    font-size: 13px;
    color: #0CA53D;
    font-weight: 700;
}

.toggle-div-collapsable-1,
.toggle-div-collapsable-2 {
    max-height: 500px;
    overflow: hidden;
    transition: max-height 0.5s ease-in-out, padding 0.5s ease-in-out;
}

.toggle-div-collapsable-1.collapsed,
.toggle-div-collapsable-2.collapsed {
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
}

.switch {
    position: relative;
    display: inline-block;
    width: 42px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
}

input:checked + .slider {
    background-color: #0ca53d;
}

input:focus + .slider {
    box-shadow: 0 0 1px #0ca53d;
}

input:checked + .slider:before {
    transform: translateX(18px);
}

.slider.round {
    border-radius: 24px;
}

.slider.round:before {
    border-radius: 50%;
}

.toggle-div-collapsable-2.simple-text {
    padding-top: 25px;
}

.toggle-div-collapsable-2.collapsed.simple-text {
    padding-top: 0;
}

.toggle-div-main.collapsed {
    border-bottom: none;
}

.button-div-main.collapsed {
    border-bottom: none;
}

.action-button-button-div {
    padding: 10px 15px;
    background: #f1f1f1;
    border: none;
    border-radius: 5px;
    color: #333;
    font-size: 14px;
    cursor: pointer;
    min-width: 125px;
}
