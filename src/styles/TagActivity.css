.tag-activity {
  min-height: 100%;
  text-align: left;
}


.date-group {
  margin-bottom: 1.5rem;
}

.date-header {
  font-size: 18px;
  font-weight: 700;
  color: #343434;
  margin-bottom: 0.4rem;
  padding-left: 0.5rem;
  text-align: left;
}

.activity-access-type {
  font-size: 15px;
  font-weight: 500;
  color: #343434;
  margin-right: 10px;
}

.activity-status {
  font-size: 12px;
  font-weight: 400;
  padding: 3px 8px;
  border-radius: 5px;
  background-color: #e3e3e3;
  color: #171717;
}

.activity-item {
  background-color: #F9F9F9;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
}

.activity-item:last-child {
  margin-bottom: 0;
}

.activity-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.activity-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.15rem;
}

.activity-timestamp {
  margin-left: auto;
  font-size: 0.75rem;
  color: #6b7280;
}

.activity-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
  border-radius: 0.375rem;
  font-weight: 500;
}

.badge-authenticated {
  background-color: #ecfdf5;
  color: #047857;
}

.badge-public {
  background-color: #f3f4f6;
  color: #4b5563;
}

.activity-details {
  font-size: 0.75rem;
  color: #374151;
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 0.25rem;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  color: #6b7280;
  margin-right: 0.5rem;
  font-weight: bold;
}

.detail-value {
  color: #111827;
  word-break: break-all;
}

.url-container {
  display: flex;
  align-items: center;
  max-width: calc(100% - 50px); /* Adjust based on your label width */
  overflow: hidden;
}

.url-text {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  direction: ltr; /* Changed from rtl to ltr */
  text-align: left;
}

.detail-value.truncate {
  max-width: calc(100% - 3.5rem);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.error-container {
  background-color: #fef2f2;
  color: #b91c1c;
  padding: 1rem;
  border-radius: 0.5rem;
  text-align: center;
}

.empty-state {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
  font-size: 0.875rem;
}