.qr-code-container {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  font-family: Arial, sans-serif;
  margin: 10px;
}

.qr-code-white-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  position: relative;
  background-color: white;
  padding: var(--padding);
  padding-top: var(--paddingTop);
  padding-bottom: var(--paddingBottom);
  border-radius: var(--borderRadius);
  overflow: hidden;
}

.qr-code-style-wrapper {
  position: relative;
  padding: var(--outerPadding);
  border-radius: var(--outerBorderRadius);
  overflow: hidden;
  background: var(--theme-gradient);
  box-shadow: 0px 4px 9px 4px var(--theme-shadow);
  /* min-height: 100px;
  max-height: 100px;
  min-width: 100px;
  max-width: 100px; */
}

.qr-code-style-wrapper.qr-no-shadow {
  box-shadow: none;
}

.qr-code-style-wrapper.qr-no-sheen .sheen {
  display: none;
}

.qr-codes {
  background-color: white;
  width: var(--qrWidth);
  height: var(--qrHeight);
  overflow: hidden;
  display: inline-block;
}

.qr-codes canvas {
  width: 100% !important;
  height: 100% !important;
}

.qr-code-value {
  font-family: roboto-mono, monospace;
  font-size: var(--fontSize);
  font-weight: 900;
  margin-top: 4.5px;
  margin-bottom: 0px;
  line-height: 1;
}

/* Theme Styles */
.qr-theme-gold {
  --theme-gradient: linear-gradient(312deg, #FEA301, #fae301);
  --theme-shadow: rgba(241, 169, 60, 0.2);
}

.qr-theme-bordeaux {
  --theme-gradient: linear-gradient(312deg, #cc3b35, #e84e47);
  --theme-shadow: rgba(204, 59, 53, 0.1);
}

.qr-theme-QleanAir {
  --theme-gradient: linear-gradient(132deg, #173D4A, #81C6B5);
  --theme-shadow: rgba(13, 43, 29, 0.1);
}

.qr-theme-QleanAir {
  --theme-gradient: linear-gradient(132deg, #173D4A, #81C6B5);
  --theme-shadow: rgba(13, 43, 29, 0.1);
}
.qr-theme-flagshipII {
  --theme-gradient: linear-gradient(132deg, #D799FC, #FAC5FC, #F65DE0, #994FDF, #6134E4);
  --theme-shadow: rgba(246, 93, 224, 0.1);
}
.qr-theme-indoor {
  --theme-gradient: linear-gradient(132deg, #7ABD49, #8ACB4F, #9EDB57);
  --theme-shadow: rgba(123, 190, 74, 0.1);
}

  .qr-theme-elegant {
    --theme-gradient: #AEC3B0;
    --theme-shadow: rgba(13, 43, 29, 0.1);
}

.qr-theme-skeleton {
  --theme-gradient: linear-gradient(132deg, #cbcbcb, #c1c1c1);
  --theme-shadow: rgba(123, 190, 74, 0.1);
}
.qr-theme-instagram {
  --theme-gradient: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  --theme-shadow: rgba(220, 39, 67, 0.1);
}
.qr-theme-whatsapp {
  --theme-gradient: linear-gradient(225deg, #25D366, #128C7E);
  --theme-shadow: rgba(37, 211, 102, 0.1);
}

/* Sheen Animation */
.sheen {
  position: absolute;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    -45deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.6) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: sheen 8s infinite;
  transform: rotate(-45deg);
  pointer-events: none;
}

@keyframes sheen {
  0% {
    transform: translate(-150%, -150%) rotate(-45deg);
  }
  37.5% {
    transform: translate(100%, 100%) rotate(-45deg);
  }
  100% {
    transform: translate(100%, 100%) rotate(-45deg);
  }
}