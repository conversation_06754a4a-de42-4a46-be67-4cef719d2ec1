.indicator {
    background: #ffffff;
    height: 4px;
    flex-grow: 1;
    margin-right: 4px;
    transition: opacity 0.2s;
}
  
.indicator:last-child {
    margin-right: 0;
}
  
.indicator.active {
    opacity: 0.5;
}

.carousel-indicators {
    display: flex;
    gap: 10px;
    position: absolute;
    bottom: 20px;
    left: 20px; 
    right: 20px;
    justify-content: space-between;
    padding: 0 20px;  /* This line adds padding to the left and right */
}
  
.carousel-indicator {
    flex: 1;
    height: 4px !important;
    background: white;
    opacity: 0.5;
    cursor: pointer;
    transition: opacity 0.3s;
    border-radius: 0 !important; /* remove rounded corners */
}

.carousel-indicator.active {
    opacity: 1;
}
