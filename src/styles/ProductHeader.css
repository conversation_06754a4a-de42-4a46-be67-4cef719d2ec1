/* 
 * PRODUCT HEADER COMPONENT STYLING
 * All selectors are prefixed with 'ph-' to avoid conflicts
 */

/* Layout and structure */
.ph-header-container {
  margin-bottom: 16px;
}

.ph-header {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}

@media (min-width: 768px) {
  .ph-header {
    flex-direction: row;
    align-items: flex-start;
  }
}

.ph-panel-section {
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 10px;
  text-align: left;
  background: #F9F9F9;
}

/* Image container */
.ph-image-container {
  width: 100%;
  max-width: 200px;
  margin: 0 auto;
}

@media (min-width: 768px) {
  .ph-image-container {
    width: 160px;
    margin: 0;
    flex-shrink: 0;
  }
}

.ph-info-container {
  flex-grow: 1;
  width: 100%;
}

/* Product image card and wrapper */
.ph-image-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  background-color: #f9f9f9;
  position: relative;
  transition: all 0.3s ease;
}

.ph-image-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.ph-image-skeleton {
  position: relative;
  width: 100%;
  padding-bottom: 100%; /* 1:1 Aspect ratio */
}

.ph-image-wrapper {
  position: relative;
  width: 100%;
  padding-bottom: 100%; /* 1:1 Aspect ratio */
  background-color: #f0f0f0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.ph-image-wrapper.ph-placeholder {
  background-color: #eaeaea;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ph-image-wrapper.ph-loading {
  background-color: #f0f0f0;
}

.ph-image-wrapper.ph-error {
  background-color: #f9f9f9;
}

.ph-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
}

.ph-image-wrapper.ph-error .ph-image {
  opacity: 0;
}

.ph-image-wrapper.ph-loading .ph-image {
  opacity: 0.5;
}

/* Loading indicator */
.ph-loading-indicator {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.ph-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3478F6;
  animation: ph-spin 1s ease-in-out infinite;
}

@keyframes ph-spin {
  to {
    transform: rotate(360deg);
  }
}

/* Placeholder image */
.ph-placeholder-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  font-size: 3rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Upload overlay */
.ph-upload-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 20px 12px 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: flex-end;
}

.ph-image-wrapper:hover .ph-upload-overlay {
  opacity: 1;
}

.ph-upload-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #3478F6;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.ph-upload-button:hover {
  background: #2962ff;
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.25);
}

/* Input styling */
.ph-readonly-input {
  background-color: #f9f9f9;
  color: #333;
  cursor: default;
  border-color: #e5e7eb;
}

.ph-readonly-textarea {
  background-color: #f9f9f9;
  color: #333;
  cursor: default;
  border-color: #e5e7eb;
  resize: none;
}

.ph-input-group {
  margin-bottom: 16px;
}

.ph-input-with-icon {
  position: relative;
}

.ph-input-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #6B7280;
  z-index: 1;
}

.ph-input-with-icon .input-v2 {
  padding-left: 36px;
}

.ph-required {
  color: #f43f5e;
  margin-left: 4px;
}

.ph-error-message {
  color: #f43f5e;
  font-size: 12px;
  margin-top: 4px;
}

/* Toggle and collapsible sections */
.ph-toggle-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  margin-bottom: 8px;
}

.ph-toggle-header.ph-expanded {
  border-bottom: 1px solid #dedede;
  padding-bottom: 10px;
  margin-bottom: 15px;
}

.ph-toggle-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.ph-toggle-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 12px; /* Added space for larger icons */
}

.ph-toggle-text {
  flex: 1;
}

.ph-toggle-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 6px;
}

.ph-toggle-subtitle {
  color: #5f5f5f;
  font-size: 13px;
  font-weight: 500;
}

.ph-collapse-container {
  display: flex;
  align-items: center;
}

.ph-collapsible-content {
  overflow: hidden;
  max-height: 0;
  opacity: 0;
  transition: all 0.3s ease-out;
}

.ph-collapsible-content.ph-expanded {
  max-height: 800px;
  opacity: 1;
}

.ph-section-content {
  animation: ph-fadeIn 0.3s ease;
}

@keyframes ph-fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Service button section */
.ph-service-section {
  margin-top: 16px;
}

/* Service Form */
.ph-service-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

@media (min-width: 768px) {
  .ph-service-form {
    flex-direction: row;
  }
}

.ph-form-left,
.ph-form-right {
  flex: 1;
}

.ph-message-group {
  height: 100%;
}

.ph-message-group textarea {
  height: calc(100% - 25px);
  min-height: 100px;
  resize: vertical;
}

/* Button preview */
.ph-button-preview {
  margin: 24px 0 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.ph-preview-title {
  font-size: 14px;
  color: #6B7280;
  margin-bottom: 12px;
  align-self: flex-start;
}

.ph-preview-button {
  background-color: #3478F6;
  color: white;
  border: none;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  min-width: 200px;
  justify-content: center;
}

.ph-preview-button:disabled {
  background-color: #9CA3AF;
  cursor: not-allowed;
}

.ph-preview-warning {
  font-size: 12px;
  color: #f59e0b;
  margin-top: 8px;
  text-align: center;
}

/* Service button container */
.ph-service-button-container {
  padding: 12px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.ph-service-button {
  background-color: #3478F6;
  color: white;
  border: none;
  padding: 14px 28px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(52, 120, 246, 0.2);
  min-width: 220px;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.ph-service-button:hover:not(:disabled) {
  background-color: #2563EB;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(52, 120, 246, 0.3);
}

.ph-service-button:active:not(:disabled) {
  transform: translateY(1px);
  box-shadow: 0 1px 5px rgba(52, 120, 246, 0.2);
}

.ph-service-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(52, 120, 246, 0.4), 0 2px 10px rgba(52, 120, 246, 0.2);
}

.ph-service-button:disabled {
  cursor: not-allowed;
  opacity: 0.8;
}

.ph-service-button.ph-loading {
  background-color: #4b83e5;
  cursor: not-allowed;
}

.ph-button-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.ph-button-spinner {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: #ffffff;
  border-radius: 50%;
  animation: ph-spin 0.8s linear infinite;
}

.ph-button-text {
  flex-grow: 1;
  text-align: center;
}

.ph-button-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  margin-left: -20px;
  transition: all 0.3s ease;
}

.ph-button-arrow.ph-visible {
  opacity: 1;
  margin-left: 0;
}

.ph-service-error-message {
  color: #ef4444;
  font-size: 14px;
  margin-bottom: 8px;
  text-align: center;
  padding: 8px 12px;
  background-color: rgba(239, 68, 68, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(239, 68, 68, 0.2);
  width: 100%;
  max-width: 350px;
  animation: ph-fade-in 0.3s ease;
}

@keyframes ph-fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Switch */
.ph-switch-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  margin-top: 16px;
  border-top: 1px solid #E5E7EB;
}

.ph-switch-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
}

.ph-switch-subtitle {
  color: #5f5f5f;
  font-size: 13px;
  font-weight: 500;
}

.ph-switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.ph-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.ph-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #E5E7EB;
  transition: .3s;
}

.ph-slider.ph-round {
  border-radius: 24px;
}

.ph-slider.ph-round:before {
  border-radius: 50%;
}

.ph-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .3s;
}

.ph-switch input:checked + .ph-slider {
  background-color: #3478F6;
}

.ph-switch input:disabled + .ph-slider {
  background-color: #9CA3AF;
  cursor: not-allowed;
}

.ph-switch input:focus + .ph-slider {
  box-shadow: 0 0 1px #3478F6;
}

.ph-switch input:checked + .ph-slider:before {
  transform: translateX(24px);
}

/* Image loading styles */
.image-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  aspect-ratio: 1/1;
}

.image-loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3478F6;
  animation: ph-spin 1s ease-in-out infinite;
  margin-bottom: 12px;
}

.image-loading-text {
  color: #6B7280;
  font-size: 14px;
  font-weight: 500;
}

/* Disabled style for upload button during upload */
.upload-button.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Image preview container - make it square */
.image-preview {
  width: 100%;
  max-width: 300px;
  height: 300px; /* Make height equal to max-width for 1:1 ratio */
  aspect-ratio: 1/1; /* Modern approach to enforce square ratio */
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  margin: 0 auto;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover; /* Ensures image fills the square properly */
}

.no-image {
  color: #9CA3AF;
  font-size: 14px;
}

.file-input {
  position: absolute;
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  overflow: hidden;
  z-index: -1;
}

.upload-button {
  display: inline-block !important;
  background-color: #3478F6 !important;
  color: white !important; /* Force white color */
  border: none !important;
  border-radius: 6px !important;
  padding: 10px 16px !important;
  font-size: 14px !important;
  font-weight: 700 !important;
  cursor: pointer !important;
  transition: background-color 0.2s !important;
  text-align: center !important;
}

/* Also target any child elements */
.upload-button * {
  color: white !important;
}

.upload-button:hover:not(.disabled) {
  background-color: #2563EB;
}

.image-upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

/* ToggleSwitch styling - Exact copy of the original component styling with ph- prefix */
.ph-toggle-switch-container {
  display: flex;
  justify-content: center;
  padding-bottom: 0px;
}

.ph-toggle-switch {
  position: relative;
  display: flex;
  background-color: #ededed;
  border-radius: 8px 8px 0 0;
  padding: 6px;
  width: 100%;
}

/* Apply special rounded border when needed */
.ph-toggle-switch.ph-rounded {
  border-radius: 8px;
}

.ph-toggle-option {
  flex: 1;
  text-align: center;
  padding: 10px;
  border: none;
  background: none;
  cursor: pointer;
  position: relative;
  z-index: 1;
  transition: color 0.3s ease;
  -webkit-tap-highlight-color: transparent; /* Removes tap highlight on mobile */
  user-select: none; /* Prevents text selection */
  font-family: inherit;
  font-size: 14px;
  font-weight: 500;
  color: #000000;
  outline: none;
  margin: 0;
}

/* Prevent visual changes on click */
.ph-toggle-option:active {
  background: none;
  outline: none;
  transform: none;
}

/* Prevent focus outline */
.ph-toggle-option:focus {
  outline: none;
}

/* Active state styling */
.ph-toggle-option.active {
  color: #000000;
  font-weight: 600;
}

/* Slider - this is the moving element */
.ph-slider {
  position: absolute;
  top: 5px;
  left: 5px;
  height: calc(100% - 10px);
  background-color: white;
  border-radius: 6px;
  transition: transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  will-change: transform;
}

/* Handle positioning for 2-option toggle */
.ph-toggle-switch:has(.ph-toggle-option:nth-child(2).active) .ph-slider {
  transform: translateX(100%);
}

/* Handle positioning for 3-option toggle */
.ph-toggle-switch:has(.ph-toggle-option:nth-child(3).active) .ph-slider {
  transform: translateX(200%);
}

/* Responsive styling for mobile */
@media (max-width: 480px) {
  .ph-toggle-option {
    padding: 8px 5px;
    font-size: 13px;
  }
  
  .ph-toggle-switch {
    padding: 5px;
  }
  
  .ph-slider {
    top: 4px;
    height: calc(100% - 8px);
  }
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  .ph-slider {
    transition: transform 0.1s ease-out;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .ph-toggle-switch {
    background-color: #2d2d2d;
  }
  
  .ph-slider {
    background-color: #3d3d3d;
  }
  
  .ph-toggle-option {
    color: #e0e0e0;
  }
  
  .ph-toggle-option.active {
    color: #ffffff;
  }
}
.document-content-container {
  max-height: 0 !important;
  overflow: hidden !important;
  opacity: 0 !important;
  transition: all 0.3s ease-out !important;
}

.document-content-container.expanded {
  max-height: 2000px !important; /* Large enough to accommodate content */
  opacity: 1 !important;
}

.input, .input::placeholder {
  font-family: inherit;
}

.access-level-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.access-level-text {
  flex: 1;
}

.access-level-select {
  width: auto;
  min-width: 120px;
}

/* Mobile view styling */
@media (max-width: 576px) {
  .access-level-container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .access-level-select {
    margin-top: 10px;
    width: 100%;
    min-width: 100%;
  }
}

.select-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  min-width: 120px;
}

.select-wrapper select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 100%;
  padding-right: 35px;
}

.select-arrow-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  width: 24px;
  height: 24px;
}

@media (max-width: 576px) {
  .select-wrapper {
    width: 100%;
  }
}

.toggle-icon {
  width: 20px;
  height: 20px;
  transition: transform 0.2s ease;
}

.collapse-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}