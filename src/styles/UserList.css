.user-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    text-align: left;
  }
  
  .user-list-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    border-radius: 5px;
    background-color: #f5f5f5;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }
  
  .user-list-item:hover {
    background-color: #f0f0f0;
  }
  
  .user-avatar-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0;
    flex-shrink: 0;
  }
  
.user-list-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #77777799;
    background-color: #f9f9f9;
    z-index: 2;
    max-width: none;
    min-width: 60px;
    min-height: 60px;
    box-sizing: content-box;
    flex-shrink: 0;
}
  
  .user-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-left: 15px;
    overflow: hidden;
  }
  
  .user-info > div {
    font-weight: 500;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .user-info > p {
    margin: 5px 0 0;
    font-size: 0.9em;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .user-role {
    font-family: jetbrains-mono, monospace;
    width: 67px;
    font-size: 12px;
    font-weight: bold;
    color: #000000;
    background-color: #d9d9d9;
    padding: 5px 5px;
    margin-right: 12px;
    border-radius: 3px;
    text-align: center;
  }