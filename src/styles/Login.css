.login-container {
    display: flex;
    height: 100vh;
    padding-bottom: 2em;
    padding-left: 2em;
    padding-right: 2em;
    overflow: auto;
  }
  
  .image-carousel {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden; /* Prevent images from overflowing */
    position: relative; /* Added to position the image absolutely */
  }
  
  .login-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding-top:  16em;
    align-items: center;
  }

  .login-panel label {
    text-align: left !important;
    display: block;
    margin-bottom: 0.5em;
    color: #333; /* adjust color as needed */
    font-weight: bold; /* adjust weight as needed */
  }
  
  .login-input {
    box-sizing: border-box;
    width: 80%;
    padding: 12px;
    margin-bottom: 20px;
    /* border-radius: 5px; */
    border: 1px solid #c6c6c6;
    font-size: 16px;
  }
  
  .login-button {
    width: 80%;
    padding: 15px;
    margin-top: 10px;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
  }
  
  .welcome-back{
    text-align: left;
    font-family: 'Playfair Display', serif;
    font-size: 2em;
    padding-bottom: 1em;
  }

  .login-group {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 80%; /* adjust as needed */
  }
  
  .login-group label {
    margin-bottom: 5px;
    color: #333;
    font-weight: bold;
  }
  
  .login-input, .login-button {
    width: 100%;
  }


  @media screen and (max-width: 767px) {
    .login-group {
      width: 100%;
      align-items: unset
    }

    .login-panel {
      padding-top: 7em;
    }

    .welcome-back {
      text-align: center;
      font-size: 2em;
    }
  }

  .form {
    width: 100%
  }

  .forgot-password-link-container {
    text-align: right;
    margin-top: 8px;
  }

  .forgot-password-link {
    font-weight: 500;
    color: #2563eb;
    text-decoration: none;
    transition: color 0.2s ease, text-decoration 0.2s ease;
  }

  .forgot-password-link:hover,
  .forgot-password-link:focus {
    text-decoration: underline;
    color: #1d4ed8;
    outline: none;
  }

  .forgot-password-link:focus-visible {
    outline: 2px solid #2563eb;
    outline-offset: 2px;
  }
  
