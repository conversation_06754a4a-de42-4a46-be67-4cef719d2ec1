.status-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    transition: all 0.2s ease;
  }
  
  .status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    border: 1px solid transparent;
  }
  .status-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    transition: all 0.2s ease;
  }
  
  .status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    border: 1px solid transparent;
  }
  
  /* New styles for improved status form */
  .status-form-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    background-color: #f9fafb;
    border-radius: 6px;
    padding: 16px;
    border: 1px solid #e5e7eb;
  }
  
  .status-preview-section {
    margin-bottom: 8px;
  }
  
  .status-form-inputs {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;
  }
  
  .color-input-container {
    position: relative;
  }
  
  .status-color-input {
    width: 32px;
    height: 32px;
    padding: 0;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    cursor: pointer;
    appearance: none;
    background-color: transparent;
    transition: transform 0.2s ease;
  }
  
  .status-color-input:hover {
    transform: scale(1.05);
  }
  
  .status-name-input {
    flex: 1;
    height: 38px;
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    font-size: 14px;
  }
  
  .add-status-button {
    height: 38px;
    padding: 0 16px;
    background-color: #3b82f6;
    color: white;
    border: none;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }
  
  .add-status-button:hover {
    background-color: #2563eb;
  }
  
  .add-status-button:disabled {
    background-color: #93c5fd;
    cursor: not-allowed;
  }
  
  .status-form-error {
    color: #ef4444;
    font-size: 12px;
    margin-top: 4px;
  }

.sf-clickable-panel {
  cursor: pointer;
  transition: background-color 0.2s;
}

.sf-clickable-panel:hover {
  background-color: #f9fafb;
}

.sf-status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  border: none;
}

.sf-status-badge.sf-enabled {
  background-color: #d1fae5;
  color: #047857;
}

.sf-status-badge.sf-disabled {
  background-color: #f3f4f6;
  color: #6b7280;
}

.sf-flex-center-align {
  display: flex;
  align-items: center;
}

.qr-code-style-wrapper.qr-no-shadow {
  background: transparent !important;
  box-shadow: none !important;
  overflow: visible;
}

/* Ensure the ProductImage container matches the parent container's border-radius */
.qr-code-style-wrapper .ph-image-wrapper {
  border-radius: 8px;
  overflow: hidden;
}

/* Fix the placeholder to match the exact border radius */
.qr-code-style-wrapper .ph-placeholder-avatar {
  border-radius: 8px;
}

/* Add these styles to your main CSS file or create a new ValidationStyles.css */

/* Error styling for inputs */
.input-error {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

.input-error:focus {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2) !important;
}

/* Error message styling */
.error-message {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
}

.error-message::before {
  content: "⚠";
  font-size: 10px;
}

/* Disabled input styling improvements */
.disabled-input {
  background-color: #f9fafb !important;
  border-color: #e5e7eb !important;
  color: #9ca3af !important;
  cursor: not-allowed !important;
}

.disabled-checkbox {
  opacity: 0.5;
  cursor: not-allowed !important;
}

.disabled-switch {
  opacity: 0.5;
}

.disabled-switch input {
  cursor: not-allowed !important;
}

.disabled-upload-container {
  opacity: 0.6;
  pointer-events: none;
}