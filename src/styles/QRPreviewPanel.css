/* This file collects CSS styles related to Thinkertags in organised designs */

.thinkertag-preview-panel {
    padding: 10px;
    margin-bottom: 15px;
    border-radius: 10px;
    background: #f5f5f5;
    margin-top: 40px;
  }

  .thinkertag-preview-panel-claim {
    padding: 10px;
    margin-bottom: 15px;
    border-radius: 10px;
    background: #f9f9f9;
    margin-top: 40px;
  }

  .thinkertag-preview-qr-wrapper {
    position: relative;
    margin-top: -60px;
    min-height: 130px;
}

.settings-content-wrapper {
    display: flex;
    /* gap: 20px; */
    flex-direction: column;
    text-align: left;
}

.divider {
    position: relative;
    height: 20px;
    margin-top: 7px;
    margin-bottom: 3px;
  }
  
  /* Dashed line */
  .divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background-image: linear-gradient(to right, #ddd 50%, transparent 50%);
    background-size: 16px 1px;  /* This controls the interval - first number is the total width of one dash + one gap */
    background-repeat: repeat-x;
  }
  
  /* Left semi-circle cutout */
  .divider::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: -10px;
    width: 12px;
    background: #FFF;
    border-radius: 0 20px 20px 0;
  }
  
  /* We need a new element for the right cutout since we can only have 2 pseudo-elements */
  .divider-right {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    right: -10px;
    width: 12px;
    background: #FFF;
    border-radius: 20px 0 0 20px;
  }