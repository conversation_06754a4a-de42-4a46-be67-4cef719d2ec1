.map-wrapper {
    position: relative;
    border-radius: 4px;
    overflow: hidden;
  }
  
  .map-error {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    color: #666;
    font-size: 14px;
  }
  
  .custom-marker {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
  }
  
  .custom-marker-pin {
    width: 50px;
    height: 50px;
    cursor: pointer;
    position: relative;
    z-index: 2;
  }
  
  .pulse-ring {
    position: absolute;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.8);
    /* Center the ring properly */
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(1);
    animation: sploosh 4s cubic-bezier(0.165, 0.84, 0.44, 1) infinite;
    opacity: 0;
  }

  .pulse-ring:nth-child(1) {
    animation-delay: 0s; /* First ring starts immediately */
  }

  .pulse-ring:nth-child(2) {
    animation-delay: 1s;
    animation-duration: 4s;
  }
  
  /* Remove default marker styling */
  .maplibregl-marker {
    background-image: none !important;
    background-color: transparent !important;
    border: none !important;
  }
  
  @keyframes sploosh {
    0% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 0.9; /* Become visible at the start of its animation */
    }
    80% {
      transform: translate(-50%, -50%) scale(3);
      opacity: 0;
    }
    100% {
      transform: translate(-50%, -50%) scale(3);
      opacity: 0;
    }
  }
  