.settings-container {
  height: 100vh;
  padding-top: 20px;
  display: flex;
  flex-direction: column;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.rotating-text-div {
 z-index: -1;
  position: absolute;
  color: #747474;
  animation: rotate 40s linear infinite;
}

.settings-header {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
  
}

.fleet-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #000000;
  font-size: 16px;
  justify-content: flex-start;
}

.arrow-icon {
  font-size: 20px;
  margin-right: 5px;
}


.settings-panel-body-bottom-claim-button {
  background-color: #CFE0FF;
  color: #3478F6;
  padding: 11px 35px;
  border-radius: 24px;
  font-weight: 600;
  font-size: 14px;
}

.claim-message {
  font-size: 12px;
  margin-top: 25px;
  margin-bottom: 20px;
}

.claim-button-label{
display: flex;
align-items: center;
}

.claim-button-label-text{
  padding-right: 5px;
  }

.arrow-forward-svg {
  width: 16px; /* Or your preferred size */
  height: 22px;
  background-image: url('/arrow_forward.svg');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  display: inline-block;
}

.settings-header-titles {
  font-family: The Bold Font;
  /* margin-top: 100px; */
  font-size: 28px;
  font-weight: normal;
  padding-left: 30px;
  padding-right: 30px;
  color: #343434;

}

.settings-panel {
  flex-grow: 1;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
}

.settings-panel-claim {
  flex-grow: 1;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center
}



  .claim-tag-qr-code {
    display: flex;
    align-items: center;
  }
  
  .settings-qr-wrapper {
    position: relative;
    padding: 8px;
    padding-bottom: 4px;
    background: white;
    border-radius: 7px; /* Adjusted border radius */
  }

  .settings-qr-wrapper-gold {
    position: relative;
    padding: 14px;
    padding-bottom: 10px;
    background: white;
    border-radius: 10px; /* Adjusted border radius */
  }
  
  .settings-qr-glow-wrapper {
    padding: 3px;
    border-radius: 10px; /* Adjusted border radius */
    background: linear-gradient(308deg, #b672fe, #f279d2);
  }

  .settings-qr-glow-wrapper-gold {
    padding: 4px;
    border-radius: 14px; /* Adjusted border radius */
    background: linear-gradient(312deg, #FEA301, #fae301);
  }

  .round-backdrop-clone{
    display: flex;
    background: linear-gradient(312deg, #f9f9f930, #f7f7f77d);
    border-radius: 10px;
    height: 142px;
    width: 142px;
    margin-top: 10px;
    border-style: solid;
    border-color: #eaeaea;
    border-width: 0.5px;
    z-index: -2;
  }

  .round-backdrop-container{
    display: flex;
    justify-content: center;
    width: 100%;    
    margin-top: 55px;   
    margin-bottom: 55px;   
  
  }

  .round-backdrop-clone:first-child {
  margin-right: 65px;
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}

.round-backdrop-clone:last-child {
  margin-left: 65px;
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}


.select-box {
  padding: 10px;
  border-radius: 5px;
  color: #fff;
  font-size: 14px;
  margin-bottom: 5px;
}

.select-box.blue {
  background: #007BFF;
}

.settings-panel-body-action-buttons {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.action-button {
  padding: 10px 15px;
  background: #f1f1f1;
  border: none;
  border-radius: 5px;
  color: #333;
  font-size: 14px;
  cursor: pointer;
}

.action-button:hover {
  background: #e0e0e0;
}

/* .settings-footer {
  bottom: 0px;  
  background-color: white;
  display: flex;
  padding-top: 10px;
  padding-bottom: 10px;
  position: sticky;
} */

.settings-panel-body-bottom-save-button {
  padding: 15px 30px;
  margin-right: 7px;
  width: 70%;
  font-weight: 600;
  font-size: 13px;
}


.close-window-button {
  margin: 20px 20px 0 0;
}

.close-window-button img {
  width: 34px;
  height: 34px;
}

.material-symbols-rounded  {
  padding-top: 4px;
  padding-left: 1px;
}

  
  .toggle-icon {
    font-size: 40px !important;
    margin: 15px;
    color: #747474;
}
  
  .toggle-title-text {
    color: #000;
    font-size: 16px;
    font-weight: 500; /* Semi-bold text */
    padding-bottom: 3px;
  }

  .toggle-title-body {
    font-weight: 400; /* Semi-bold text */
    text-align: left;
  }

  .url-input {
    margin-bottom: 0px;
    margin-top: 0px;

  }

  .settings-panel-body-text{
    font-size: 15px;
    margin-bottom: 10px;
    text-align: left;
  }


  .settings-header-block-group{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
  }

  .hide-logo-container {
    display: none;
  }

  .material-icons-outlined {
    font-size: 20px;
    color: #3d3d3d;
  }

  .backarrow {
    height: 20px;
    color: #8A8A8E;
  }

  .settings-url-input-group {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 15px;
    width: 100%;
  }

.label {
  padding: 7px;
}

.status-text {
  background-color: #DDF9EA;
  padding: 3px;
  padding-left: 6px;
  padding-right: 6px;
  border-radius: 6px;
  font-size: 13px;
  color: #0CA53D;
  font-weight: 700;
}

.data-status-text {
  background-color: #DDF9EA;
  padding: 3px;
  padding-left: 6px;
  padding-right: 6px;
  border-radius: 6px;
  font-size: 13px;
  color: #0CA53D;
  font-weight: 700;
}


.switch {
  position: relative;
  display: inline-block;
  width: 42px; /* Reduced by 30% */
  height: 24px; /* Reduced by 30% */
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px; /* Reduced by 30% */
  width: 18px; /* Reduced by 30% */
  left: 3px; /* Reduced by 30% */
  bottom: 3px; /* Reduced by 30% */
  background-color: white;
  transition: 0.4s;
}

input:checked + .slider {
  background-color: #0ca53d;
}

input:focus + .slider {
  box-shadow: 0 0 1px #0ca53d;
}

input:checked + .slider:before {
  transform: translateX(18px); /* Reduced by 30% */
}

.slider.round {
  border-radius: 24px; /* Reduced by 30% */
}

.slider.round:before {
  border-radius: 50%;
}

.settings-header-body {
  font-weight: 300;
  font-size: 2vh;
  padding: 25px;
}


.bold {
  font-weight: 700;
}



.particle {
  position: absolute;
  border-radius: 50%; /* Makes it round */
}

.particle-cross {
  position: relative;
  background-color: transparent;
}

.particle-cross::before, .particle-cross::after {
  content: "";
  position: absolute;
  background-color: currentColor;  /* Use the color of the particle */
  border-radius: 3px;  /* Adjust as per your preference */
  width: 30%;  /* Width of the bars */
  height: 100%;  /* Height of the bars */
  left: 50%;  /* Center horizontally */
  top: 50%;  /* Center vertically */
  
}

.particle-cross::before {
  transform: rotate(90deg) translate(-20%, 0%);  /* Rotate by 90 degrees and adjust for center positioning */
}

.particle-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}



.particle-bordered {
  position: relative;
  border: 3px solid;
  background-color: transparent;
  border-radius: 50%; /* Make the bordered particles round */
}



.particle, .particle-bordered, .particle-cross {
  box-sizing: border-box; /* Ensures borders don't change the total size of the particle */
  animation: explode 5s ease-in-out infinite; /* The animation that makes the particle disappear */
}

@keyframes explode {
  0% {
    transform: translate(0, 0);
    opacity: 1;
  }

  50% {
    transform: translate(calc(var(--direction-x) * 30px), calc(var(--direction-y) * 30px));
    opacity: 1;
  }

  100% {
    opacity: 1;
  }
}



.status-text-claim {
  position: absolute;
  right: 8px;
  margin-top: 11px;
  margin-left: 10px;
  background-color: #DDF9EA;
  padding: 4px;
  padding-left: 6px;
  padding-right: 6px;
  border-radius: 6px;
  font-size: 13px;
  color: #0CA53D;
  font-weight: 700;
  width: 34px;
}


.settings-input-wrapper {
  position: relative; /* Parent needs to be positioned relative */
  text-align: left;
}

.settings-auto-group{
  margin-bottom: 25px;
  border-radius: 4px;
  background-color: #fcfcfc;
  transition: all 0.3s ease;
  border: 1px solid #dedede;
  text-align: left;
  padding: 25px;
  padding-top: 15px;
}

hr {
  margin-bottom: 60px;
  border-top: 1px solid #bbb;
  width: 100%
}

.fine-print {
  font-size: 11px;
  color: #2c2c2c;
}


.panel-indicator {
  display: flex;
  justify-content: center; /* Center the indicators */
  margin-bottom: 10px;
  margin-top: 10px;
}

.loading-animation {
  width: 30px;
  align-self: center;
}