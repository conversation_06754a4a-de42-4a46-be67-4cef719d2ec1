@import url('https://fonts.googleapis.com/css2?family=Figtree:ital,wght@0,300..900;1,300..900&display=swap');

.logo-container {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  min-height: 3vh;
  padding: 2vh;
}

.logo-container .logo {
  grid-column: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.logo-container .sign-in-out-button {
  grid-column: 3;
  justify-self: end;
}

.logo-container img {
  width: 9em;
}


.logo-text {
  font-family: 'The Bold Font';
  font-weight: 900; /* Extra bold */
  color: #0D2B1D; /* Color */
  font-size: 34px; /* Adjust the size as needed */
  font-weight: normal;
}

.isometric-navbar {
  width: 100%;
  overflow-x: auto;
  background-color: #fff;
  padding: 30px 0px 10px 0px;
  position: relative;
}

.isometric-navbar::-webkit-scrollbar {
  display: none;
}

.nav-container {
  /* display: inline-grid; */
  display: inline-flex;
  grid-template-rows: 60px 60px;
  grid-auto-flow: column;
  grid-gap: 0px 10px;
  padding: 0 15px;
}

.nav-item {
  background-color: #f5f5f5;
  border-radius: 10px;
  border: 0.25px solid #ebebeb;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 10px;
  cursor: pointer;
}

.tag-nav-item {
  margin-left: 0.5px;
  margin-right: 0.5px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}


.two-two {
  grid-row: span 2;
  width: 110px;
  height: 110px;
}

.two-one {
  width: 110px;
  height: 50px;
}

.one-one {
  width: 50px;
  height: 50px;
}

.row-2 {
  grid-row-start: 2;
}

.col-start-1 { grid-column-start: 1; }
.col-start-2 { grid-column-start: 2; }
.col-start-3 { grid-column-start: 3; }
.col-start-4 { grid-column-start: 4; }
.col-start-5 { grid-column-start: 5; }

.log-out {
  font-weight: bold;
  font-size: 14px;
}

.account-balance {
  font-weight: bold;
  font-size: 18px;
}

.pause-button {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.icon-wrapper {
  flex-shrink: 0;
  width: 30px;
  height: 30px;
  margin-right: 5px;
  margin-left: 5px;
}

.icon-wrapper-groups img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}


.icon-wrapper-location img {
  padding-left: 2px;
  padding-top: 2px;
  height: 23px;
}

.text-wrapper-groups {
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: left;
  margin-left: 7px;
}

.pause-icon {
  width: 100%;
  height: 100%;
  background-image: url(/pause_active.svg);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.pause_disabled-icon {
  width: 100%;
  height: 100%;
  background-image: url(/pause_disabled.svg);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.toggles-icon {
  width: 100%;
  height: 100%;
  background-image: url(/toggle.svg);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.text-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: left;
  margin-right: 5px;
}

.top-text, .bottom-text {
  display: block;
  line-height: 1.2;
}

.top-text {
  font-size: 12px;
  font-weight: bold;
}

.bottom-text {
  font-size: 10px;
  font-weight: normal;
}

.white-wrapper {
  background-color: #fff;
  border-radius: 10px;
  border: 0.25px solid #ebebeb;
  height: 78%;
  width: 88%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.sign-in-button {
  background-color: transparent;
  color: #0071e3;
  border: 1px solid #0071e3;
  padding: 6px 14px;
  border-radius: 980px;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sign-in-button:hover {
  background-color: rgba(0, 113, 227, 0.05);
  color: #0077ED;
}