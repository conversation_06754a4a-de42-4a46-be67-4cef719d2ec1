/* Common styles for containers */
.searchable-filterable-list-container {
  display: flex;
  flex-direction: column;
  /* margin-bottom: 20px; */
}

/* Input styles */
.searchable-list-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.searchable-list-chevron {
  display: flex;
  color: #666;
  right: 35px;
  padding-right: 8px;
}

.searchable-list-search-icon {
  position: absolute;
  left: 12px;
  top: 53%;
  transform: translateY(-50%);
  color: #666;
  display: flex;
  align-items: center;
}

.searchable-list-input {
  box-sizing: border-box;
  font-size: 16px;
  width: 100%;
  border: 1px solid #C3C3C3;
  background: #ffffff;
  border-radius: 3px;
  padding: 10px 10px 10px 45px;
  height: 3em;
  margin-bottom: 0px;
  transition: background-color 0.3s ease;
  margin-top: 3px;
}

/* Common input field styles */

.searchable-list-collapsed-selected,
.searchable-list-collapsed-empty {
  justify-content: space-between;
  box-sizing: border-box;
  width: 100%;
  border-radius: 10px;
  font-size: 16px;
  background-color: #f9f9f9;
  transition: background-color 0.3s ease;
  margin-top: 3px;
 
}

.searchable-list-collapsed-selected,
.searchable-list-collapsed-empty {
  height: 66px;
  padding: 15px;
  padding-right: 25px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between; /* This pushes the clear button to the right */
  margin-bottom: 15px;
}


.searchable-list-collapsed-empty {
  padding: 15px;
  padding-bottom: 11.5px;
}

.searchable-list-input:focus {
  outline: none;
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

/* Filter section */
.searchable-list-filters {
  display: flex;
  overflow-x: auto;
  margin: 15px 0;
  gap: 8px;
}

.searchable-list-filter-button {
  background-color: #f9f9f9;
  color: #888888;
  border: 1px solid #dedede;
  border-radius: 20px;
  padding: 8px 15px;
  font-size: 14px;
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.3s ease;
}

.searchable-list-filter-button.active {
  background-color: #ffffff;
  color: #303030;
  border-color: #1a73e8;
}

/* List items */
.searchable-list-items {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 20px;
}

.searchable-list-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  gap: 5px;
  padding: 12px;
  margin-bottom: 8px;
  background-color: #f9f9f9;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

/* Hover states */
.searchable-list-item:hover,
.searchable-list-collapsed-selected:hover {
  background-color: #f5f5f5;
}

/* Action buttons */
.searchable-list-selected-actions,
.searchable-list-selected-actions-2 {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  right: 40px;
}

.searchable-list-selected-actions-2 {
  position: absolute;
  right: 10px;
  top: 54%;
  transform: translateY(-50%);
  display: flex;
  gap: 8px;
  z-index: 1;
}

.searchable-list-clear-button {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.searchable-list-clear-button img {
  width: 25px;
  height: 25px;
}

.link-primary-icon {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.link-primary-icon img {
  width: 35px;
  height: 35px;
}



/* Placeholder styles */
.searchable-list-placeholder,
.searchable-list-placeholder-selected {
  gap: 2px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.searchable-list-placeholder {
  color: #888888;
  padding-left: 10px;
}

.searchable-list-placeholder-pseudo-input {
  background-color: #ffffff;
  height: 40px;
  width: 100%;
  border-radius: 3px;
  border: 1px solid #C3C3C3;
  font-size: 16px;

}

.searchable-list-no-results {
  text-align: center;
  color: #888888;
  padding: 20px;
}

.searchable-list-item-text {
  
  /* font-size: 13px; */
  font-weight: 600;
}

.searchable-list-item-category-text {
  color: #5f5f5f;
  font-size: 13px;
  font-weight: 400;
}

.location-button {
  display: flex;
  align-items: center;
  gap: 6px;
}

.location-button .location-icon {
  margin-right: 2px;
}

.location-button.active {
  background-color: #e8f0fe;
  color: #1a73e8;
  border-color: #1a73e8;
}

.location-button:disabled {
  opacity: 0.7;
  cursor: wait;
}

.searchable-list-item-details {
  display: flex;
  align-items: center;
  gap: 12px;
}

.searchable-list-item-distance {
  color: #666;
  font-size: 13px;
  padding-left: 12px;
  border-left: 1px solid #e0e0e0;
}