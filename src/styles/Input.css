.email-input-container {
    position: relative;
    display: flex;
    align-items: center;
  }
  
  .input {
    box-sizing: border-box;
    width: 100%;
    padding: 10px;
    padding-top: 11px;
    padding-bottom: 11px;
    padding-right: 40px; /* Make room for the clear button */
    margin-bottom: 20px;
    margin-top: 3px;
    border-radius: 3px;
    border: 0.5px solid #C3C3C3;
    font-size: 16px;
    /* height: 39.5px; */
    color: #363636;
  }

  /* New input style fields*/
  .input-v2 {
    box-sizing: border-box;
    width: 100%;
    padding: 10px;
    padding-top: 11px;
    padding-bottom: 11px;
    padding-right: 40px;
    margin-top: 3px;
    border-radius: 3px;
    border: 0.5px solid #C3C3C3;
    font-size: 16px;
    color: #363636;
  }

  .input-v2-error {
    box-sizing: border-box;
    width: 100%;
    padding: 10px;
    padding-top: 11px;
    padding-bottom: 11px;
    padding-right: 40px; /* Make room for the clear button */
    /* margin-bottom: 20px; */
    margin-top: 3px;
    border-radius: 3px;
    border: 2px solid #dd0000;
    font-size: 16px;
    /* height: 39.5px; */
    color: #363636;
  }

  .input-group label
  {
    color: #333;
    font-size: 13px;
    font-weight: 450;
  }
  
  .clear-input-button {
    position: absolute;
    right: 8px;
    top: calc(3px + (43px / 2)); /* Account for top margin (3px) + half of input height */
    transform: translateY(-50%);
    background: none;
    border: none;
    font-size: 30px;
    color: #8A8A8E;
    cursor: pointer;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 0; /* Helps center the × symbol */
  }
  
