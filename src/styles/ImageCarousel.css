.carousel {
    flex: 1;
    height: 100%;
    position: relative;
    overflow: hidden;
    /* border-radius: 10px */

  }

.carousel-image {
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.carousel-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  

.carousel-image.active {
    opacity: 1;
}

.image-carousel {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden; /* Prevent images from overflowing */
    position: relative; /* Added to position the image absolutely */
}

.image-carousel img {
    position: absolute; /* Image will be positioned absolutely within the .image-carousel */
    min-width: 100%; /* Minimum width of the image should be 100% */
    min-height: 100%; /* Minimum height of the image should be 100% */
    top: 50%; /* Align the image in the center vertically */
    left: 50%; /* Align the image in the center horizontally */
    transform: translate(-50%, -50%); /* Offset the image's position to truly center it */
}



@media screen and (min-width: 768px) and (max-width: 1199px) {
    .carousel-caption {
      font-size: 1.2em; /* Adjust the font size based on your preference */
    }
  }


  @media screen and (max-width: 767px) {
    .carousel {
      display: none;
    }
  }
  
