.local-canvas {
  padding: 9px;
  border: 2px solid #ddd;
  border-radius: 5px;
  background-color: #fff;
}

.qr-div{
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%; /* Make sure the div has a height */
  flex-shrink: 0; /* Prevent QR code from shrinking */
}

.title {
  color: #343434;
  display: flex;
  flex-direction: row;
  align-items: baseline;
  justify-content: space-between;
}

.title h2 {
  margin-bottom: 15px;
  font-size: 1.7em;
}


.count-container {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative; /* To allow stacking of elements */
}

.count {
  font-size: 1.7em; /* Adjust as needed */
  font-weight: bold;
  color: black;
  z-index: 1; /* Place the number on top */
}

.count-color {
  width: 160%; /* Match the width of the count */
  height: 50%; /* Adjust to cover half of the count vertically */
  background-color: #d6d6d6; /* Grey background */
  position: absolute;
  bottom: 0; /* Align the box to the bottom of the container */
  bottom: 5px;
  right: 0px;
  
  z-index: 0; /* Make sure it is behind the count */
}

.tag-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  text-align: left;
  
}

.tag-list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  /* border: 1px solid #ddd; */
  border-radius: 5px;
  background-color: #f5f5f5;
  height: 53px;
  min-width: 0; /* Allow container to shrink below content size */
}

.tag-info {
  flex: 1; /* Allow it to grow and take up available space */
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-left: 15px;
  min-width: 0; /* Allow flex item to shrink below content size */
  overflow: hidden; /* Hide overflowing content */
}

.tag-info > div {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tag-info > p {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tag-list-item h3 {
  margin: 0;
  font-size: 1em;
}

.tag-list-item p {
  margin: 5px 0 0;
  font-size: 0.9em;
  color: #666;
}

.tag-id {
   font-family: jetbrains-mono, monospace;
    width: 67px;
    font-size: 12px;
    font-weight: bold;
    color: #000000;
    background-color: #d9d9d9;
    padding: 5px 5px;
    margin-right: 12px;
    border-radius: 3px;
    text-align: center;
    flex-shrink: 0; /* Prevent tag ID from shrinking */
}

.search-container {
  padding: 8px 5px;
  margin-bottom: 10px;
  background-color: #f9f9f9;
  border-radius: 0px 0px 5px 5px;
  border-top: 0;
  border: 1px solid #ededed;
  border-top: none;
  position: relative;
}

.search-input {
  box-sizing: border-box;
  width: 100%;
  height: 3em;
  padding: 10px 10px 10px 45px; /* Adjust left padding for the icon */
  border-radius: 5px;
  border: 0.8px solid #C3C3C3;
  font-size: 16px;
  background: url('/search_glass.svg') no-repeat 15px center;
  background-size: 22px; /* Adjust icon size */
  background-color: white;
}

.group-header {
  text-align: left;
  color: #343434;
  font-size: 16px;
  margin-bottom: 0em;
}

/* New Filter Button Styles */
.filter-buttons {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 10px;
}

.filter-button {
  padding: 8px 16px;
  border-radius: 20px;
  border: none;
  font-size: 14px;
  cursor: pointer;
  background-color: #f0f0f0;
  color: #333;
  transition: all 0.2s ease;
}

.filter-button:hover {
  background-color: #e0e0e0;
}

.filter-button.active {
  background-color: rgb(59, 130, 246);
  color: white;
}