/* Font definitions */

/*
 * HEADING FONT
 * Playfair Display - Variable Font (supports multiple weights)
 */
@font-face {
  font-family: 'Playfair Display';
  src: url('/fonts/playfair-display/PlayfairDisplay-VariableFont_wght.ttf') format('woff2-variations'),
       url('/fonts/playfair-display/PlayfairDisplay-VariableFont_wght.ttf') format('woff-variations'),
       url('/fonts/playfair-display/PlayfairDisplay-VariableFont_wght.ttf') format('truetype-variations'),
       url('/fonts/playfair-display/PlayfairDisplay-VariableFont_wght.ttf') format('truetype');
  font-weight: 400 900; /* Variable font weight range from 400 (Regular) to 900 (Black) */
  font-style: normal;
  font-display: swap;
}

/* Playfair Display - Italic Variable Font */
@font-face {
  font-family: 'Playfair Display';
  src: url('/fonts/playfair-display/PlayfairDisplay-Italic-VariableFont_wght.ttf') format('woff2-variations'),
       url('/fonts/playfair-display/PlayfairDisplay-Italic-VariableFont_wght.ttf') format('woff-variations'),
       url('/fonts/playfair-display/PlayfairDisplay-Italic-VariableFont_wght.ttf') format('truetype-variations'),
       url('/fonts/playfair-display/PlayfairDisplay-Italic-VariableFont_wght.ttf') format('truetype');
  font-weight: 400 900; /* Variable font weight range from 400 (Regular) to 900 (Black) */
  font-style: italic;
  font-display: swap;
}

/* Static font fallbacks for browsers that don't support variable fonts */
/* Playfair Display - Regular */
@font-face {
  font-family: 'Playfair Display';
  src: url('/fonts/playfair-display/PlayfairDisplay-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

/* Playfair Display - Medium */
@font-face {
  font-family: 'Playfair Display';
  src: url('/fonts/playfair-display/PlayfairDisplay-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

/* Playfair Display - SemiBold */
@font-face {
  font-family: 'Playfair Display';
  src: url('/fonts/playfair-display/PlayfairDisplay-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

/* Playfair Display - Bold */
@font-face {
  font-family: 'Playfair Display';
  src: url('/fonts/playfair-display/PlayfairDisplay-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* Playfair Display - ExtraBold */
@font-face {
  font-family: 'Playfair Display';
  src: url('/fonts/playfair-display/PlayfairDisplay-ExtraBold.ttf') format('truetype');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

/* Playfair Display - Black */
@font-face {
  font-family: 'Playfair Display';
  src: url('/fonts/playfair-display/PlayfairDisplay-Black.ttf') format('truetype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

/* Playfair Display - Italic */
@font-face {
  font-family: 'Playfair Display';
  src: url('/fonts/playfair-display/PlayfairDisplay-Italic.ttf') format('truetype');
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

/* Playfair Display - Bold Italic */
@font-face {
  font-family: 'Playfair Display';
  src: url('/fonts/playfair-display/PlayfairDisplay-BoldItalic.ttf') format('truetype');
  font-weight: 700;
  font-style: italic;
  font-display: swap;
}

/*
 * BODY FONT OPTIONS
 */

/*
 * Option 1: IBM Plex Serif
 */
@font-face {
  font-family: 'IBM Plex Serif';
  src: url('/fonts/ibm-plex-serif/IBMPlexSerif-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'IBM Plex Serif';
  src: url('/fonts/ibm-plex-serif/IBMPlexSerif-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'IBM Plex Serif';
  src: url('/fonts/ibm-plex-serif/IBMPlexSerif-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'IBM Plex Serif';
  src: url('/fonts/ibm-plex-serif/IBMPlexSerif-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'IBM Plex Serif';
  src: url('/fonts/ibm-plex-serif/IBMPlexSerif-Italic.ttf') format('truetype');
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'IBM Plex Serif';
  src: url('/fonts/ibm-plex-serif/IBMPlexSerif-BoldItalic.ttf') format('truetype');
  font-weight: 700;
  font-style: italic;
  font-display: swap;
}

/*
 * Option 2: Charter
 */
@font-face {
  font-family: 'Charter';
  src: url('/fonts/charter/Charter-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Charter';
  src: url('/fonts/charter/Charter-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Charter';
  src: url('/fonts/charter/Charter-Italic.ttf') format('truetype');
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Charter';
  src: url('/fonts/charter/Charter-BoldItalic.ttf') format('truetype');
  font-weight: 700;
  font-style: italic;
  font-display: swap;
}

/*
 * Option 3: EB Garamond (Adobe Garamond Pro alternative)
 */
@font-face {
  font-family: 'EB Garamond';
  src: url('/fonts/adobe-garamond-pro/EBGaramond-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'EB Garamond';
  src: url('/fonts/adobe-garamond-pro/EBGaramond-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'EB Garamond';
  src: url('/fonts/adobe-garamond-pro/EBGaramond-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'EB Garamond';
  src: url('/fonts/adobe-garamond-pro/EBGaramond-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'EB Garamond';
  src: url('/fonts/adobe-garamond-pro/EBGaramond-Italic.ttf') format('truetype');
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'EB Garamond';
  src: url('/fonts/adobe-garamond-pro/EBGaramond-BoldItalic.ttf') format('truetype');
  font-weight: 700;
  font-style: italic;
  font-display: swap;
}

/*
 * Option 4: Georgia Pro (system font fallback)
 * No @font-face declarations needed as we'll use the system font
 */

/*
 * HEADING FONT OPTIONS
 */

/*
 * Option 1: Montserrat (Bold Sans-Serif)
 */
@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/montserrat/Montserrat-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/montserrat/Montserrat-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/montserrat/Montserrat-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/montserrat/Montserrat-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/montserrat/Montserrat-ExtraBold.ttf') format('truetype');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

/*
 * Option 2: Source Sans Pro (Clean Sans-Serif)
 */
@font-face {
  font-family: 'Source Sans Pro';
  src: url('/fonts/source-sans-pro/SourceSansPro-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Source Sans Pro';
  src: url('/fonts/source-sans-pro/SourceSansPro-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Source Sans Pro';
  src: url('/fonts/source-sans-pro/SourceSansPro-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/*
 * Option 3: Lora (Elegant Serif for Headings)
 */
@font-face {
  font-family: 'Lora';
  src: url('/fonts/lora/Lora-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Lora';
  src: url('/fonts/lora/Lora-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Lora';
  src: url('/fonts/lora/Lora-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Lora';
  src: url('/fonts/lora/Lora-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}
