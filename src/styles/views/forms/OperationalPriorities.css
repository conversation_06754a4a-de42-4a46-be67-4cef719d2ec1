/* OperationalPriorities.css - Specific styles for the operational priorities page */

.priorities-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
  }
  
  .priority-option {
    padding: var(--spacing-md);
    background-color: white;
    border: 2px solid rgba(0, 0, 0, 0.08);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-speed) ease-in-out;
  }
  
  .priority-option:hover {
    border-color: rgba(0, 102, 204, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
  }
  
  .priority-option.selected {
    border-color: var(--accent-color);
    background-color: rgba(0, 102, 204, 0.05);
  }
  
  .checkbox-container {
    display: flex;
    align-items: flex-start;
  }
  
  .priority-checkbox {
    margin-right: var(--spacing-sm);
    width: 20px;
    height: 20px;
    cursor: pointer;
    accent-color: var(--accent-color);
  }
  
  .checkbox-label {
    display: flex;
    flex-direction: column;
    cursor: pointer;
  }
  
  .priority-description {
    font-size: 0.9rem;
    color: var(--secondary-color);
    margin-top: 4px;
  }
  
  /* Apply animation to priority options */
  .priority-option {
    animation: fadeInFromLeft 0.8s ease-out forwards;
    opacity: 0;
  }
  
  /* Stagger the animations for a nicer effect */
  .priority-option:nth-child(1) { animation-delay: 0.1s; }
  .priority-option:nth-child(2) { animation-delay: 0.2s; }
  .priority-option:nth-child(3) { animation-delay: 0.3s; }
  .priority-option:nth-child(4) { animation-delay: 0.4s; }
  .priority-option:nth-child(5) { animation-delay: 0.5s; }
  .priority-option:nth-child(6) { animation-delay: 0.6s; }
  
  /* Responsive Adjustments */
  @media (min-width: 768px) {
    .priorities-container {
      grid-template-columns: 1fr 1fr;
    }
  }

  @media (max-width: 768px) {

    .enterprise-guide .options-container{
      padding-bottom: var(--spacing-xl);
  }
    
  }