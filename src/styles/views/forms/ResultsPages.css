/* ResultsPages.css - Styling for the qualification results and related pages */

/* Base styling for results pages */
.results-page {
    max-width: var(--container-width);
    margin: 0 auto;
  }
  
  .results-title {
    font-size: 2.25rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    text-align: center;
    color: var(--primary-color);
  }
  
  /* Score visualization */
  .score-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: var(--spacing-lg) 0;
    gap: var(--spacing-md);
  }
  
  .score-circle {
    width: 160px;
    height: 160px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3184ff 0%, #0066cc 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 25px rgba(0, 102, 204, 0.25);
    color: white;
    transition: all 0.5s ease;
  }
  
  .score-number {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1;
  }
  
  .score-label {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-top: 4px;
  }
  
  .qualification-badge {
    padding: 8px 20px;
    background-color: #28a745;
    color: white;
    border-radius: 20px;
    font-weight: 600;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-top: var(--spacing-sm);
    animation: badgePulse 2s infinite;
  }
  
  .qualification-badge.qualified {
    background-color: #28a745; /* Green for highly qualified */
  }
  
  .qualification-badge:not(.qualified) {
    background-color: #17a2b8; /* Blue for qualified */
  }
  
  @keyframes badgePulse {
    0% {
      box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4);
    }
    70% {
      box-shadow: 0 0 0 15px rgba(40, 167, 69, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
  }
  
  /* Results insights */
  .results-insight {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.8s ease;
  }
  
  .results-insight.fade-in {
    opacity: 1;
    transform: translateY(0);
  }
  
  .results-insight h2 {
    font-size: 1.5rem;
    text-align: center;
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
  }
  
  .insight-card {
    background-color: white;
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }
  
  .insight-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .insight-card h3 {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-sm);
    color: var(--accent-color);
  }
  
  .insight-card p {
    line-height: 1.6;
    color: var(--secondary-color);
  }
  
  .insight-card strong {
    color: var(--primary-color);
  }
  
  /* Next Steps Page */
  .next-steps-container {
    margin: var(--spacing-sm) 0;
  }
  
  .next-step-item {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    opacity: 0.7;
    transition: all 0.5s ease;
  }
  
  .next-step-item.checked {
    opacity: 1;
  }
  
  .checkmark-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 2px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-sm);
    transition: all 0.3s ease;
    flex-shrink: 0;
    margin-top: 4px;
  }
  
  .next-step-item.checked .checkmark-icon {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
    animation: checkmarkAppear 0.5s ease;
  }
  
  @keyframes checkmarkAppear {
    0% {
      transform: scale(0.5);
      opacity: 0;
    }
    70% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }
  
  .next-step-content {
    text-align: left;
    flex-grow: 1;
  }
  
  .next-step-content h3 {
    font-size: 1.1rem;
    margin-bottom: 4px;
    color: var(--primary-color);
  }
  
  .next-step-content p {
    color: var(--secondary-color);
    margin: 0;
  }
  
  /* Call to Action */
  .cta-container {
    margin-top: var(--spacing-xl);
    text-align: center;
  }
  
  .enterprise-cta-button {
    padding: 14px 32px;
    font-size: 1.1rem;
    font-weight: 600;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: var(--border-radius-cta);
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
  }
  
  .enterprise-cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
  }
  
  .enterprise-cta-button:active {
    transform: translateY(-1px);
  }
  
  .supporting-text {
    margin-top: var(--spacing-md);
    color: var(--secondary-color);
    font-size: 0.95rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }
  
  /* Confirmation page styles */
  .confirmation-content {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.8s ease;
    text-align: center;
  }
  
  .confirmation-content.visible {
    opacity: 1;
    transform: translateY(0);
  }
  
  .confirmation-icon {
    width: 70px;
    height: 70px;
    background-color: #28a745;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin: 0 auto var(--spacing-md);
    animation: confirmationPulse 2s infinite;
  }
  
  @keyframes confirmationPulse {
    0% {
      box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4);
    }
    70% {
      box-shadow: 0 0 0 20px rgba(40, 167, 69, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
  }
  
  .confirmation-message {
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--secondary-color);
    max-width: 700px;
    margin: 0 auto var(--spacing-lg);
  }
  
  .next-actions {
    text-align: left;
    background-color: rgba(0, 102, 204, 0.05);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    border-left: 4px solid var(--accent-color);
    margin-top: var(--spacing-lg);
  }
  
  .next-actions h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
  }
  
  .next-actions-list {
    color: var(--secondary-color);
    padding-left: var(--spacing-md);
  }
  
  .next-actions-list li {
    margin-bottom: 10px;
  }
  
  .next-actions-list li:last-child {
    margin-bottom: 0;
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .results-title {
      font-size: 1.8rem;
    }
    
    .score-circle {
      width: 130px;
      height: 130px;
    }
    
    .score-number {
      font-size: 2.8rem;
    }
    
    .enterprise-cta-button {
      width: 100%;
    }
  }