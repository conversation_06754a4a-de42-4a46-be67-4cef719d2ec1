/* GuidePages.css - Optimized styling for the Thinkertags qualification guide */

/* Animations */
@keyframes enterprise-guide-fadeInFromLeft {
    0% {
        opacity: 0;
        transform: translateX(+20px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes enterprise-guide-progressGrow {
    0% {
        transform: scaleX(0.8);
        opacity: 0.7;
    }
    50% {
        transform: scaleX(1.05);
    }
    100% {
        transform: scaleX(1);
        opacity: 1;
    }
}

/* Enterprise Guide Styles */
.enterprise-guide {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    color: var(--text-color);
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: white;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    max-width: var(--container-width);
    width: 100%;
    box-sizing: border-box;
    padding: var(--spacing-md);
    padding-bottom: calc(var(--bottom-section-height) + var(--spacing-md)); /* Added extra padding for fixed bottom section */
    margin: 0 auto;
    position: relative; /* Added for positioning context */
}

/* Animation delay utility classes */
.enterprise-guide .delay-100 { animation-delay: 0.1s; }
.enterprise-guide .delay-200 { animation-delay: 0.2s; }
.enterprise-guide .delay-300 { animation-delay: 0.3s; }
.enterprise-guide .delay-500 { animation-delay: 0.5s; }
.enterprise-guide .delay-600 { animation-delay: 0.6s; }

/* Typography */
.enterprise-guide h1, 
.enterprise-guide .ea-intro-heading {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1.2;
    margin-top: 0;
    letter-spacing: -0.04em;
    text-align: left;
    min-height: 64px;
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);
    animation: enterprise-guide-fadeInFromLeft 0.8s ease-out forwards;
    opacity: 0;
}

.enterprise-guide .ea-intro-heading {
    color: var(--secondary-color);
    animation-delay: 0.1s;
    margin-bottom: 0;
}

.enterprise-guide h1 {
    font-weight: 500;
    animation-delay: 0.1s;
}

.enterprise-guide .guide-subtitle {
    font-size: 1.125rem;
    color: var(--secondary-color);
    margin-top: 0;
    text-align: left;
    animation: enterprise-guide-fadeInFromLeft 0.8s ease-out forwards;
}

.enterprise-guide .intro-description {
    font-size: 1.5rem;
    line-height: 1.5;
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    text-align: left;
    opacity: 1;
}


.enterprise-guide .no-margin {
    margin: 0;
}

.enterprise-guide .accent-text {
    color: var(--accent-highlight);
    font-weight: 500;
}

.enterprise-guide .legal-text {
    margin-bottom: var(--spacing-sm);
    font-size: 0.85rem;
    color: #777777;
    margin-top: 0; /* Adjusted from var(--spacing-xs) */
    text-align: center;
    line-height: 1.4;
    animation: enterprise-guide-fadeInFromLeft 0.8s ease-out forwards;
    animation-delay: 0.6s;
    opacity: 0;
}

/* Progress Bar */
.enterprise-guide .guide-progress {
    margin-bottom: var(--spacing-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.enterprise-guide .progress-bar {
    height: 5px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
    overflow: hidden;
    flex-grow: 1;
}

.enterprise-guide .progress-fill {
    height: 100%;
    background-color: var(--accent-color);
    transition: width var(--transition-speed) ease-in-out;
    transform-origin: left center;
    animation: enterprise-guide-progressGrow 0.8s ease-out forwards;
}

/* Branding */
.enterprise-guide .guide-branding {
    margin-bottom: var(--spacing-lg);
    display: flex;
    justify-content: flex-start;
}

.enterprise-guide .guide-logo {
    height: 18px;
}

.enterprise-guide .guide-logo:hover {
    opacity: 1;
}

/* Guide Content */
.enterprise-guide .guide-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

/* Options */
.enterprise-guide .options-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    animation: enterprise-guide-fadeInFromLeft 0.8s ease-out forwards;
    animation-delay: 0.3s;
    opacity: 0;
}

/* Button Styles */
.enterprise-guide .option-button, 
.enterprise-guide .guide-next-button {
    transition: all var(--transition-speed) ease-in-out;
    cursor: pointer;
    font-weight: 500;
    font-size: 1rem;
}

.enterprise-guide .option-button {
    color: var(--primary-color);
    padding: var(--spacing-md);
    background-color: white;
    border: 2px solid rgba(0, 0, 0, 0.08);
    border-radius: var(--border-radius);
    text-align: left;
    display: flex;
    align-items: center;
    width: 100%;
}

.enterprise-guide .option-button:hover {
    border-color: rgba(0, 102, 204, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

.enterprise-guide .option-button.selected {
    border-color: var(--accent-color);
    background-color: rgba(0, 102, 204, 0.05);
}

/* Actions */
.enterprise-guide .guide-actions {
    display: flex;
    justify-content: center;
    /* width: 100%; */
    /* margin-top: var(--spacing-sm); */
    animation: enterprise-guide-fadeInFromLeft 0.8s ease-out forwards;
    animation-delay: 0.5s;
    opacity: 0;
    /* margin: 0 5vh; */
    /* padding: 0 5vh; */
}

.enterprise-guide .guide-next-button {
    display: inline-block;
    padding: 12px 24px;
    border-radius: var(--border-radius-cta);
    width: 100%;
    max-width: 90%; /* Prevent button from stretching full width on larger screens */
    background-color: rgb(49, 132, 255);
    color: white;
    text-decoration: none;
    text-align: center;
    border: none;
}

.enterprise-guide .guide-next-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.enterprise-guide .guide-next-button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.enterprise-guide .guide-bottom-section {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    padding: var(--spacing-md) var(--spacing-md);
    border-top: 1px solid rgba(0, 0, 0, 0.03);
    z-index: 10;
    box-sizing: border-box;
    width: 100%;
    animation: fadeIn 0.5s ease-out forwards;
}


/* Option Description */
.enterprise-guide .option-description {
    margin-top: 4px;
    font-size: 0.9rem;
    color: var(--secondary-color);
}

.enterprise-guide .error-message {
    color: #ef4444;
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
    text-align: left;
}

/* Responsive Adjustments */
@media (min-width: 768px) {
    .enterprise-guide .options-container {
        grid-template-columns: 1fr 1fr;
    }
    
    .enterprise-guide {
        padding: var(--spacing-lg);
        padding-bottom: calc(var(--bottom-section-height) + var(--spacing-md));
    }
}

@media (max-width: 480px) {
    .enterprise-guide {
        padding: var(--spacing-md);
        padding-bottom: calc(var(--bottom-section-height) + var(--spacing-md));
        margin: 0;
    }
    
    .enterprise-guide h1, 
    .enterprise-guide .ea-intro-heading {
        font-size: 1.75rem;
        min-height: 52px;
    }
    
    .enterprise-guide .intro-description {
        font-size: 1.2rem;
        margin-bottom: var(--spacing-lg);
    }

    .enterprise-guide .no-margin {
        margin: 0;
    }
    
    /* Ensure the bottom section doesn't take up too much space on small screens */
    .enterprise-guide .guide-bottom-section {
        padding: var(--spacing-sm);
    }
    
    .enterprise-guide .legal-text {
        font-size: 0.8rem;
    }

    .enterprise-guide .options-container{
        padding-bottom: var(--spacing-xl);
    }
}

/* Fix for iOS devices to handle the safe area */
@supports (-webkit-touch-callout: none) {
    .enterprise-guide .guide-bottom-section {
        padding-bottom: calc(var(--spacing-md) + env(safe-area-inset-bottom, 0));
    }
}

.enterprise-guide .qualification-status {
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--accent-color);
    margin-bottom: var(--spacing-md);
    animation: enterprise-guide-fadeInFromLeft 0.8s ease-out forwards;
    opacity: 0;
}

.enterprise-guide .insights-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    animation: enterprise-guide-fadeInFromLeft 0.8s ease-out forwards;
    opacity: 0;
}