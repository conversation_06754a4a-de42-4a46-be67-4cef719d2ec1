/* reset.css - Base styles and reset for Thinkertags */

.landing-page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  color: var(--text-color);
  line-height: 1.6;
  max-width: 100%;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
}

.landing-page h1,
.landing-page h2,
.landing-page h3 {
  line-height: 1.2;
  margin-top: 0;
  margin-bottom: 0;
  color: var(--primary-color);
}

.landing-page h1 {
  font-weight: 800;
  font-size: 2.8rem;
  font-weight: 900;
  letter-spacing: -0.02em;
  line-height: 1;
}

.landing-page h2 {
  font-weight: 800;
  font-size: 2.25rem;
  letter-spacing: -0.01em;
  margin-bottom: var(--spacing-sm);
}

.landing-page h3 {
  font-weight: 600;
  font-size: 1.5rem;
  letter-spacing: -0.01em;
  margin-bottom: var(--spacing-sm);
}

.landing-page p {
  text-align: center;
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  max-width: var(--container-width);
  margin-left: auto;
  margin-right: auto;

  line-height: 1.5;
  font-weight: 400;
  letter-spacing: -0.02em;
  font-size: 1.1rem;
  color: var(--secondary-color);


}

.landing-page section {
  padding: var(--spacing-xl) 0;
  max-width: var(--container-width);
  margin: 0 auto;
  text-align: left;
}

/* buttons.css - Button styles for Thinkertags */

.landing-page .primary-btn,
.landing-page .secondary-btn,
.landing-page .alternative-btn {
  display: inline-block;
  padding: 14px 28px;
  border-radius: var(--border-radius-cta);
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all var(--transition-speed) ease-in-out;
  border: none;
  text-align: center;
}

.landing-page .primary-btn {
  background-color: #008AFF;
  color: white;
}

.landing-page .primary-btn:hover {
  transform: translateY(-2px);
}

.landing-page .secondary-btn {
  background-color: transparent;
  color: var(--accent-color);
  border: 1px solid var(--accent-color);
}

.landing-page .secondary-btn:hover {
  background-color: rgba(0, 102, 204, 0.05);
  transform: translateY(-2px);
}

.landing-page .alternative-btn {
  background-color: rgb(49,132,255,1);
  color: rgb(255, 255, 255);
  border: 1px solid var(--accent-color);
}

.landing-page .alternative-btn:hover {
  transform: translateY(-2px);
}