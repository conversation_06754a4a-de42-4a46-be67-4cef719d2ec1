/* quoteCards.css - Consolidated styling for all quote cards */

/* ===== SECTION 1: QUOTE SECTION CONTAINER ===== */
.landing-page .quotes-section {
  position: relative;
  top: -80px;
}

/* ===== SECTION 2: TEXT-BASED QUOTE CARDS ===== */

/* Quote component - base positioning */
.quotes-section .thinkertags-quote-component {
  position: relative;
  left: 65%;
  display: flex;
}

/* Left-positioned quote component */
.quotes-section .thinkertags-quote-component-left {
  position: relative;
  right: 65%;
  display: flex;
  flex-direction: row-reverse;
}

/* Quote card - main container */
.thinkertags-quote-card {
  display: flex;
  align-items: center;
  background-color: #E5ECE5;
  border-radius: 11px;
  padding: 21px 28px 21px 21px;
  max-width: 285px;
  position: relative;
  margin-left: 52px;
  margin-right: 14px;
}

/* Left-positioned quote card */
.thinkertags-quote-card-left {
  display: flex;
  align-items: center;
  background-color: #E5ECE5;
  border-radius: 11px;
  padding: 21px 21px 21px 28px;
  max-width: 285px;
  position: relative;
  margin-right: 52px;
  margin-left: 14px;
}

/* Avatar container */
.thinkertags-quote-avatar-container {
  position: absolute;
  left: -38px;
  top: -28px;
  z-index: 2;
}

/* Left-positioned avatar container */
.thinkertags-quote-avatar-container-left {
  position: absolute;
  right: -38px;
  top: -28px;
  z-index: 2;
}

/* Avatar image */
.thinkertags-quote-avatar {
  width: 76px;
  height: 76px;
  border-radius: 7px;
  object-fit: cover;
}

/* Content container */
.thinkertags-quote-content {
  width: 100%;
  padding-left: 28px;
}

/* Left-positioned content container */
.thinkertags-quote-content-left {
  width: 100%;
  padding-right: 28px;
}

/* Quote text - with higher specificity to override .landing-page p */
.thinkertags-quote-card .thinkertags-quote-content .thinkertags-quote-text {
  font-family: 'Playfair Display', serif;
  font-size: 1.28rem;
  font-weight: 800;
  line-height: 1.25;
  color: #000;
  margin: 0 0 9px 0;
  letter-spacing: -0.02em;
  text-align: left;
  max-width: none;
  margin-left: 0;
  margin-right: 0;
}

/* Author text - with higher specificity to override .landing-page p */
.thinkertags-quote-card .thinkertags-quote-content .thinkertags-quote-author {
  font-size: 0.9rem;
  color: #333;
  font-weight: 400;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  margin: 0;
  text-align: left;
  max-width: none;
  margin-left: 0;
  margin-right: 0;
}

/* Left-positioned quote text - with higher specificity to override .landing-page p */
.thinkertags-quote-card-left .thinkertags-quote-content-left .thinkertags-quote-text {
  font-family: 'Playfair Display', serif;
  font-size: 1.28rem;
  font-weight: 800;
  line-height: 1.25;
  color: #000;
  margin: 0 0 9px 0;
  letter-spacing: -0.02em;
  text-align: right;
  max-width: none;
  margin-left: 0;
  margin-right: 0;
}

/* Left-positioned author text - with higher specificity to override .landing-page p */
.thinkertags-quote-card-left .thinkertags-quote-content-left .thinkertags-quote-author {
  font-size: 0.9rem;
  color: #333;
  font-weight: 400;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  margin: 0;
  text-align: right;
  max-width: none;
  margin-left: 0;
  margin-right: 0;
}

/* ===== SECTION 3: RESPONSIVE STYLES ===== */

/* Medium screens */
@media (max-width: 1200px) {
  /* Reset positioning for smaller screens */
  .landing-page .quotes-section {
    height: auto;
    margin: 30px 0;
  }

  .quotes-section .thinkertags-quote-component,
  .quotes-section .thinkertags-quote-component-left {
    position: static;
    margin: 30px auto;
    padding: 0;
    justify-content: center;
  }

  /* Reset card margins - adjusted for better spacing */
  .thinkertags-quote-card,
  .thinkertags-quote-card-left {
    margin: 36px 0 0; /* Adjusted from 34px for better spacing */
    width: 100%;
    padding: 18px 24px 20px; /* Slightly increased bottom padding */
  }

  /* Keep avatar positioning - adjusted for better spacing */
  .thinkertags-quote-avatar-container,
  .thinkertags-quote-avatar-container-left {
    left: 50%;
    right: auto;
    top: -35px; /* Adjusted from -32px for better spacing */
    transform: translateX(-38px);
  }

  /* Reduce avatar size for smaller screens */
  .thinkertags-quote-avatar {
    width: 70px; /* Reduced from 76px */
    height: 70px; /* Reduced from 76px */
  }

  /* Center content on mobile - slightly increased padding for better spacing */
  .thinkertags-quote-content,
  .thinkertags-quote-content-left {
    padding: 28px 0 0; /* Adjusted from 25px for better spacing with avatar */
    text-align: center;
  }

  /* Center text on mobile with higher specificity */
  .thinkertags-quote-card .thinkertags-quote-content .thinkertags-quote-text,
  .thinkertags-quote-card .thinkertags-quote-content .thinkertags-quote-author,
  .thinkertags-quote-card-left .thinkertags-quote-content-left .thinkertags-quote-text,
  .thinkertags-quote-card-left .thinkertags-quote-content-left .thinkertags-quote-author {
    text-align: center;
  }

  /* Reduce font size and line height for quote text on smaller screens */
  .thinkertags-quote-card .thinkertags-quote-content .thinkertags-quote-text,
  .thinkertags-quote-card-left .thinkertags-quote-content-left .thinkertags-quote-text {
    font-size: 1.15rem; /* Reduced from 1.28rem */
    line-height: 1.2; /* Reduced from 1.25 */
    margin-bottom: 7px; /* Reduced from 9px */
  }

  /* Adjust card layout */
  .thinkertags-quote-card,
  .thinkertags-quote-card-left {
    flex-direction: column;
    align-items: center;
  }
}

/* Tablet and smaller screens */
@media (max-width: 1024px), (max-width: 768px) {
  /* No specific tablet styles needed after removing image-based quote cards */
}

/* Small screens */
@media (max-width: 480px) {
  /* Add more space between buttons and quote on small screens */
  .landing-page .quotes-section {
    margin-top: 120px;
  }

  /* Text-based quote cards */
  .thinkertags-quote-avatar {
    width: 66px;
    height: 66px;
  }

  .thinkertags-quote-avatar-container,
  .thinkertags-quote-avatar-container-left {
    transform: translateX(-33px);
  }

  .thinkertags-quote-card .thinkertags-quote-content .thinkertags-quote-text,
  .thinkertags-quote-card-left .thinkertags-quote-content-left .thinkertags-quote-text {
    font-size: 1.19rem;
  }

  .thinkertags-quote-card,
  .thinkertags-quote-card-left {
    padding: 17px 24px;
    margin-left: 0;
    margin-right: 0;
  }
}
