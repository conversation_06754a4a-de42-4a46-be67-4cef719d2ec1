/* feature-cards.css - Feature cards section styling for Thinkertags */

.landing-page .article-cards {
    padding-top: var(--spacing-xl);
    padding-bottom: var(--spacing-xl);
    
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    /* Override the standard section constraints */
    max-width: 100%; /* Allow full width of viewport */
    overflow: visible; /* Ensure content can extend beyond container */
  }
  
  /* Keep the heading and subtitle centered in the standard container */
  .landing-page .article-cards h2,
  .landing-page .article-cards p {
    max-width: var(--container-width);
    padding-left: calc((100% - var(--container-width)) / 2 + var(--spacing-md));
    padding-right: var(--spacing-md);
    text-align: left;
  }

  
  .landing-page .cards-container {
    display: flex;
    gap: var(--spacing-md);
    overflow-x: auto;
    scroll-behavior: smooth;
    /* Hide scrollbar */
    -ms-overflow-style: none;
    scrollbar-width: none;
    /* New styles to ensure cards extend to the right */
    width: 100%;
    padding: var(--spacing-md) 0;
    padding-left: calc((100% - var(--container-width)) / 2 + var(--spacing-md)); /* Align first card with other content */
    padding-right: var(--spacing-md);
    margin-left: 0;
    margin-right: 0;
  }
  
  /* Make sure the last card has some right margin to allow scrolling past it */
  .landing-page .cards-container .card:last-child {
    margin-right: calc((100% - var(--container-width)) / 2); /* Add space after the last card */
  }
  
  .landing-page .cards-container::-webkit-scrollbar {
    display: none;
  }
  
  .landing-page .card {
    width: 300px;
    flex: 0 0 auto;
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    background-color: rgba(0, 0, 0, 0.02);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }
  
  .landing-page .card-image-container {
    width: 300px;
    height: 350px;
    overflow: hidden;
    margin-bottom: var(--spacing-md);
  }
  
  .landing-page .card-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--border-radius);
  }
  
  .landing-page .card .subtitle {
    color: var(--accent-color);
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
  }
  
  .landing-page .card .description {
    margin-bottom: var(--spacing-md);
  }
  
  .landing-page .card .tagline {
    color: var(--secondary-color);
  }