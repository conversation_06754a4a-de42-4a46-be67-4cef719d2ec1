/* features.css - Features section styling for Thinkertags */

.landing-page .features {

    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 0px;
    padding-bottom: var(--spacing-xl);
  }

  /* Keep the heading and subtitle centered in the standard container */
  .landing-page .features h2,
  .landing-page .features p {
    max-width: var(--container-width);
    margin-left: auto;
    margin-right: auto;
    /* padding-left: var(--spacing-md);
    padding-right: var(--spacing-md); */
    text-align: center;
  }

  .landing-page .features-title-container {
    position: relative;
    width: 100%;
    display: flex;
    justify-content: center;
  }


  .landing-page .features .how-it-looks {
    /* simple styling for the image */
    width: 60%;
    object-fit: cover;
    /* margin-bottom: var(--spacing-md); */
    min-width: 390px;
    /* padding-left: 10px; */
  }

  .landing-page .best-wrapper {
    position: relative;
    display: inline-block;
    padding-bottom: 5px; /* Add a little padding to ensure space for the doodle */
  }

  .landing-page .saturn-with-stars-doodle {
    position: absolute;
    width: 100%;
    bottom: 5px; /* Moved 10px upwards from -5px */
    left: 70px; /* Moved 10px to the left from 80px */
    z-index: 0;
    pointer-events: none;
  }

  .landing-page .features .feature {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 var(--spacing-md);

     max-width: 700px;
    object-fit: cover;
  }

  .landing-page .features .feature img {
    width: 80%;
    object-fit: cover;
    min-width: 360px;
  }


  .landing-page .features .feature .feature-text h3,
  .landing-page .features .feature .feature-text p {
    text-align: center;
  }

  .landing-page .features .feature .feature-text p {
    margin-bottom: 0;
    line-height: 1.5;
    font-weight: 400;
    letter-spacing: -0.02em;
    font-size: 1.1rem;
    color: var(--secondary-color);
  }

  .landing-page .features .feature .feature-text {
    /* position: absolute; */
    padding: var(--spacing-sm) var(--spacing-xl) 0 var(--spacing-xl);
  }

/* Responsive styles for features section */
@media (max-width: 1200px) {
  .landing-page .features {
    margin-top: 0px; /* Slightly reduced margin for medium-large screens */
  }
}

@media (max-width: 768px) {
  .landing-page .features {
    margin-top: 0px; /* Further reduced margin for tablets */
  }
}

@media (max-width: 480px) {
  .landing-page .features {
    margin-top: 0px; /* Minimal margin for mobile screens */
  }
}