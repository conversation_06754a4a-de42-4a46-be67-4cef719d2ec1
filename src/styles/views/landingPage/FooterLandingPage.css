/* footer.css - Footer styling for Thinkertags */

.landing-page .footer {
    display: flex;
    justify-content: center;
    padding: var(--spacing-md);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    margin-top: var(--spacing-xl);
    text-align: center;
  }
  
  .landing-page .footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
  }
  
  .landing-page .footer-logo {
    height: 22px;
    transition: opacity var(--transition-speed) ease-in-out;
  }
  
  .landing-page .footer-logo:hover {
    opacity: 1;
  }
  
  .landing-page .copyright {
    font-size: 0.875rem;
    color: var(--secondary-color);
    margin: 0;
  }