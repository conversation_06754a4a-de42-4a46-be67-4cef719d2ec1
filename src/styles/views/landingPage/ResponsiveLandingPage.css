/* responsive.css - Responsive media queries for Thinkertags */

/* Large screens - push content down further */
@media (min-width: 1200px) {

    .landing-page .hero-content {
      padding-bottom: calc(var(--spacing-md) + 60px); /* Much more padding at the bottom for large screens */
      padding-top: 120px; /* More top padding for large screens */
      max-height: 70%; /* Further reduced max-height for large screens */
    }

    /* Make buttons the same width on large screens */
    .landing-page .primary-btn,
    .landing-page .sign-in-button {
      min-width: 220px; /* Minimum width for large screens */
    }

    /* Specific selector for hero CTA text on large screens */
    .landing-page .hero .hero-cta-text {
      max-width: 450px; /* Slightly wider on large screens */
    }

    /* Specific selector for hero main title on large screens */
    .landing-page .hero .hero-main-title {
      max-width: 500px; /* Slightly wider on large screens */
    }
}

/* Screens 1025px and larger - adjust doodle position */
@media (min-width: 1025px) {
    /* Move doodle slightly to the right to avoid intersecting with the 'P' */
    .landing-page .hero-power-doodle {
      left: 9px; /* Move to the right to avoid intersecting with the 'P' */
    }
}

/* Medium-large screens */
@media (min-width: 769px) and (max-width: 1199px) {
    .landing-page .hero-content {
      padding-top: 100px; /* Intermediate padding for medium-large screens */
    }
}

@media (max-width: 1024px) {
    .landing-page h1 {
      font-size: 2rem;
    }

    .landing-page h2 {
      font-size: 2rem;
    }

    .landing-page .hero h1 {
      font-size: 2rem;
    }

    .landing-page .advantage-panels {
      flex-direction: column;
    }

    .landing-page .technology-options {
      flex-direction: row;
    }

    /* Saturn doodle now scales with text automatically */

    /* Hero power doodle now scales with text automatically */

    /* Quote card styles moved to QuotesLandingPage.css and QuoteCardLandingPage.css */
}

@media (max-width: 768px) {
    .landing-page h1 {
      font-size: 2.25rem;
    }

    .landing-page h2 {
      font-size: 1.75rem;
    }

    .landing-page h3 {
      font-size: 1.25rem;
    }

    .landing-page .hero h1 {
      font-size: 2rem;
    }

    .landing-page .hero p {
      font-size: 1.1rem;
    }

    /* Specific selector for hero CTA text on medium screens */
    .landing-page .hero .hero-cta-text {
      max-width: 400px; /* Narrower on medium screens to control line breaks */
    }

    /* Specific selector for hero main title on medium screens */
    .landing-page .hero .hero-main-title {
      max-width: 500px; /* Narrower on medium screens to control line breaks */

    }

    .landing-page .hero-content {
      padding-top: 30px; /* Significantly reduced for medium screens */
    }

    /* Slightly scale down the Thinkertag for medium screens */
    .landing-page .hero-thinkertag-container {
      transform: scale(0.95); /* Slightly smaller on medium screens */
    }

    /* Make buttons the same width on medium screens */
    .landing-page .primary-btn,
    .landing-page .sign-in-button {
      min-width: 200px; /* Minimum width for medium screens */
    }

    .landing-page .technology-options {
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-xl);
    }

    /* Saturn doodle now scales with text automatically */

    /* Hero power doodle now scales with text automatically */
}

@media (max-width: 480px) {
    /* Hero section adjustments for very small screens */
    .landing-page .hero {
      min-height: auto; /* Remove fixed height constraint for small screens */
      padding-top: 20px; /* Add some top padding instead */
    }

    .landing-page .hero-content {
      padding: var(--spacing-sm);
      padding-bottom: calc(var(--spacing-sm) + 20px); /* Add more padding at the bottom */
      padding-top: 20px; /* Minimal top padding for small screens */
      margin: 0 var(--spacing-sm);
      max-height: 85%; /* Slightly higher max-height for mobile */
    }

    /* Scale down the Thinkertag for small screens */
    .landing-page .hero-thinkertag-container {
      transform: scale(0.85); /* Make the Thinkertag smaller on small screens */
    }

    .landing-page .hero h1 {
      font-size: 2rem;
      margin-bottom: var(--spacing-sm);
    }

    /* Specific selector for hero main title on small screens */
    .landing-page .hero .hero-main-title {
      max-width: 100%; /* Full width on small screens */
      padding: 0 10px; /* Add some padding */
    }

    .landing-page .hero p {
      font-size: 1rem;
      margin-bottom: var(--spacing-md);
    }

    /* Specific selector for hero CTA text on small screens */
    .landing-page .hero .hero-cta-text {
      max-width: 100%; /* Full width on small screens */
      padding: 0 10px; /* Add some padding */
      font-size: 0.95rem; /* Slightly smaller font */
      line-height: 1.4; /* Tighter line height */
    }

    .landing-page .primary-btn,
    .landing-page .sign-in-button {
      padding: 12px 24px;
      font-size: 0.9rem;
      width: 100%; /* Full width buttons on mobile */
    }

    .landing-page .features .feature .feature-text {
      padding: var(--spacing-sm) var(--spacing-sm) 0 var(--spacing-sm);
    }

    /* Both doodles now scale with text automatically */

    /* Power doodle now scales with text automatically */
}

/* Extra small screens */
@media (max-width: 360px) and (max-height: 640px) {
    .landing-page .hero-content {
      padding-top: 15px; /* Minimal padding for very small screens */
    }

    /* Scale down the Thinkertag even more for extra small screens */
    .landing-page .hero-thinkertag-container {
      transform: scale(0.7); /* Make the Thinkertag even smaller on extra small screens */
    }
}