/* hero.css - Hero section styling for Thinkertags */

.landing-page .hero {
    position: relative;
    display: flex;
    padding: 0;
    margin: 0;
    max-width: none;
    width: 100%;
    overflow: hidden;
    color: var(--primary-color);
    background-color: #f9f9f9; /* Light gray background instead of white */
    transition: background-color 0.5s ease;
    flex-direction: column;
    justify-content: flex-start; /* Changed from flex-end to flex-start to push content to the top */
    align-items: center;
  }

  /* Hero video removed and replaced with Thinkertag */

  .landing-page .hero-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 1;
    margin: 0 var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    margin-top: 0; /* Changed from auto to 0 to push content to the top */
    padding: var(--spacing-md);
    padding-bottom: calc(var(--spacing-md) + 40px); /* Increased bottom padding for larger screens */
    padding-top: 80px; /* Increased for large screens, will be overridden for smaller screens */
    text-align: center;
    max-height: 75%; /* Reduced max-height to push content further down */
  }

  .landing-page .hero-thinkertag-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: calc(var(--spacing-md) + 6px); /* Adjusted bottom margin for better spacing */
    /* No scaling to match ClaimTag page */
  }

  .landing-page .hero-title-container {
    position: relative;
    width: 100%;
    display: flex;
    justify-content: center;
  }

  /* Specific styling for the main hero title */
  .landing-page .hero .hero-main-title {
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
    display: block;
  }

  .landing-page .powerful-wrapper {
    position: relative;
    display: inline-block;
    padding-bottom: 5px; /* Add a little padding to ensure space for the doodle */
  }

  .landing-page .hero-power-doodle {
    position: absolute;
    width: 100%;
    bottom: -8px;
    left: 0;
    z-index: 0;
    pointer-events: none;
  }

  .landing-page .hero p {
    margin-top: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    max-width: 600px;
  }

  /* Specific selector for hero CTA text to prevent style leakage */
  .landing-page .hero .hero-cta-text {
    max-width: 300px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.5;
    display: block;
  }

  .landing-page .hero-logo {
    /* width: 100px;
    height: 100px; */
    margin-bottom: var(--spacing-sm);
  }

  .landing-page .sign-in-button {
    background-color: transparent;
    color: #008AFF; /* Match primary-btn blue color */
    border: 1px solid #008AFF; /* Match primary-btn blue color */
    padding: 14px 28px;
    border-radius: var(--border-radius-cta);
    font-size: 1rem;
    font-weight: 500; /* Changed to 500 as requested */
    cursor: pointer;
    transition: all var(--transition-speed) ease-in-out;
    margin-top: 12px; /* Reduced from var(--spacing-md) which is 24px */
    display: inline-block;
  }

  .landing-page .sign-in-button:hover {
    background-color: rgba(0, 138, 255, 0.05); /* Match primary-btn blue color with transparency */
    color: #008AFF; /* Match primary-btn blue color */
    transform: translateY(-2px);
  }