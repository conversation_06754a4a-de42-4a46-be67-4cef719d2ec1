/* AssetDocumentView.css - Optimized */

.asset-document-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #f5f5f5;
  color: #333;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  overflow: hidden; /* Prevent double scrollbars */
}

.document-header {
  padding: 10px 15px;
  background-color: #fff;
  border-bottom: 1px solid #ddd;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.document-header-info {
  width: 100%;
}

.document-header h1 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  word-break: break-word;
  line-height: 1.2;
  text-align: center;
}

/* Action buttons overlay */
.document-actions-overlay {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 20;
}

.document-actions-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 15px;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.share-button, 
.download-button,
.action-button {
  display: inline-block;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s;
  border: none;
  cursor: pointer;
  text-align: center;
  white-space: nowrap;
  min-width: 120px;
  text-decoration: none;
}

.share-button {
  background-color: #f0f0f0;
  color: #333;
}

.share-button:hover {
  background-color: #e0e0e0;
}

.download-button {
  background-color: #0066cc;
  color: white;
}

.download-button:hover {
  background-color: #0052a3;
}

.document-viewer-container {
  flex: 1;
  overflow: auto;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  position: relative;
}

.file-viewer-wrapper {
  width: 100%;
  height: 100%;
  background-color: #fff;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

/* Image viewer specific adjustments */
.image-viewer {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: auto;
  background-color: #f9f9f9;
}

.image-viewer img {
  object-fit: contain;
  max-width: 100%;
  max-height: 100%;
}

/* Override react-file-viewer styles for better appearance */
.file-viewer-wrapper .pg-viewer-wrapper {
  overflow: auto;
  height: 100%;
}

.file-viewer-wrapper .pg-viewer {
  padding: 20px;
}

.unsupported-format {
  padding: 30px;
  text-align: center;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Loading state */
.asset-document-view.loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #0066cc;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error state */
.asset-document-view.error,
.asset-document-view.not-found {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  text-align: center;
}

.error-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #ff4444;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 15px;
}

.asset-document-view.error h2,
.asset-document-view.not-found h2 {
  margin: 0 0 8px 0;
  color: #333;
}

.asset-document-view.error p,
.asset-document-view.not-found p {
  margin: 0 0 15px 0;
  color: #666;
}

.document-error {
  padding: 30px;
  text-align: center;
  color: #666;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .document-header {
    padding: 8px 10px;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .document-header h1 {
    font-size: 1rem;
  }
  
  .document-meta {
    margin-bottom: 8px;
    font-size: 0.75rem;
  }
  
  .document-actions {
    /* width: 100%; */
    justify-content: space-between;
  }
  
  .back-button,
  .download-button {
    font-size: 0.75rem;
    padding: 5px 10px;
  }
  
  .unsupported-format {
    padding: 20px 15px;
  }
}

