/* Scoped CSS for Asset Workbench Sources component */
.asset-workbench-sources {
  margin: 20px 0;
}

/* Primary Section: Existing Asset Management */
.asset-workbench-primary-section {
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  padding: 20px;
  margin-bottom: 24px;
}

.asset-workbench-primary-section h3 {
  font-size: 18px;
  color: #333;
  margin: 0 0 16px 0;
}

.asset-workbench-existing-assets-card {
  display: flex;
  text-align: left;
  align-items: center;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 20px;
  justify-content: space-between;
}

.asset-workbench-existing-assets-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.asset-workbench-existing-assets-icon img {
  width: 40px;
  height: 40px;
}

.asset-workbench-existing-assets-details {
  flex-grow: 1;
}

.asset-workbench-existing-assets-details h4 {
  font-size: 16px;
  margin: 0 0 4px 0;
  color: #333;
}

.asset-workbench-existing-assets-details p {
  font-size: 14px;
  color: #666;
  margin: 0 0 4px 0;
}

.asset-workbench-existing-assets-actions {
  display: flex;
  gap: 12px;
}

/* Secondary Section: Import & Templates */
.asset-workbench-secondary-section {
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  padding: 20px;
}

/* Import options */
.asset-workbench-import-options {
  margin-bottom: 16px;
}

.asset-workbench-dropzone-container {
  margin-bottom: 16px;
}

.asset-workbench-dropzone {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  border: 2px dashed #3062d4;
  border-radius: 4px;
  background-color: #edf2ff;
  cursor: pointer;
  transition: border-color 0.2s ease, background-color 0.2s ease;
}

.asset-workbench-dropzone:hover {
  background-color: #e3ebff;
  border-color: #2754b9;
}

.asset-workbench-upload-file-icon {
  width: 32px;
  height: 32px;
  margin-bottom: 8px;
}

.asset-workbench-dropzone p {
  margin: 0;
  font-size: 14px;
  color: #3062d4;
}

.asset-workbench-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.asset-workbench-section-header h3 {
  font-size: 18px;
  color: #333;
  margin: 0;
}

.asset-workbench-templates-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: #3062d4;
  font-size: 14px;
  font-weight: 500;
}

/* Expand/collapse button */
.asset-workbench-expand-button {
  background: none;
  border: none;
  cursor: pointer;
  width: 24px;
  height: 24px;
  position: relative;
  padding: 0;
}

.asset-workbench-plus-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
}

.asset-workbench-plus-icon:before,
.asset-workbench-plus-icon:after {
  content: '';
  position: absolute;
  background-color: #3062d4;
  transition: transform 0.3s ease;
}

.asset-workbench-plus-icon:before {
  width: 100%;
  height: 2px;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}

.asset-workbench-plus-icon:after {
  width: 2px;
  height: 100%;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
}

.asset-workbench-expand-button.expanded .asset-workbench-plus-icon:after {
  transform: translateX(-50%) rotate(90deg);
}

/* Template list */
.asset-workbench-template-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out;
  opacity: 0;
  margin-top: 0;
}

.asset-workbench-template-list.expanded {
  max-height: 1000px; /* Arbitrary large height to allow animation */
  opacity: 1;
  margin-top: 16px;
}

/* Template cards */
.asset-workbench-template-card {
  display: flex;
  text-align: left;
  align-items: center;
  gap: 15px;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 16px;
}

.asset-workbench-template-icon img {
  width: 32px;
  height: 32px;
}

.asset-workbench-template-info {
  flex-grow: 1;
  min-width: 0;
}

.asset-workbench-template-info h4 {
  font-size: 15px;
  margin: 0 0 4px 0;
  color: #333;
}

.asset-workbench-template-info p {
  font-size: 13px;
  color: #666;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Template buttons */
.asset-workbench-template-buttons {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

/* Button styles */
.asset-workbench-download-button,
.asset-workbench-edit-button,
.asset-workbench-download-button-examples,
.asset-workbench-try-button-examples {
  text-align: center;
  min-width: 100px;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
  cursor: pointer;
  box-sizing: border-box;
}

.asset-workbench-download-button,
.asset-workbench-download-button-examples {
  background-color: #ffffff;
  color: #3062d4;
  border: 1px solid #3062d4;
  text-decoration: none;
  display: inline-block;
}

.asset-workbench-edit-button,
.asset-workbench-try-button-examples {
  background-color: #3062d4;
  color: white;
  border: 1px solid #3062d4;
}

.asset-workbench-download-button:hover,
.asset-workbench-download-button-examples:hover {
  background-color: #edf2ff;
  text-decoration: none;
  color: #3062d4;
}

.asset-workbench-edit-button:hover,
.asset-workbench-try-button-examples:hover {
  background-color: #2754b9;
}

.asset-workbench-download-button:disabled,
.asset-workbench-edit-button:disabled,
.asset-workbench-download-button-examples:disabled,
.asset-workbench-try-button-examples:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.asset-workbench-loading-message {
  font-style: italic;
  color: #888;
}

.asset-workbench-asset-count {
  font-size: 13px;
  color: #3062d4;
  font-weight: 500;
  margin-top: 4px;
}

/* Responsive styles */
@media (max-width: 900px) {
  .asset-workbench-existing-assets-card {
    flex-direction: column;
    align-items: flex-start;
  }

  .asset-workbench-existing-assets-actions {
    margin-top: 16px;
    align-self: flex-end;
  }

  .asset-workbench-template-card {
    flex-wrap: wrap;
  }

  .asset-workbench-template-info {
    width: calc(100% - 47px); /* 32px icon + 15px gap */
  }

  .asset-workbench-template-buttons {
    margin-top: 12px;
    margin-left: 47px;
    width: auto;
  }
}

@media (max-width: 640px) {
  .asset-workbench-section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .asset-workbench-existing-assets-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .asset-workbench-existing-assets-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .asset-workbench-template-card {
    flex-direction: column;
    align-items: flex-start;
  }

  .asset-workbench-template-info {
    width: 100%;
  }

  .asset-workbench-template-buttons {
    align-self: flex-end;
    margin-top: 10px;
    width: 100%;
    justify-content: flex-end;
    margin-left: 0;
  }
}