.h1 {
    font-size: 3em;
    font-weight: 500; /* makes font thicker */
    text-align: center;
    margin: 0.7em 1em 1.3em; /* top & bottom, right & left, bottom */
    color: black;
}

.gradient-text {
    /* Gradient text */
    font-weight: 700; /* makes font thicker */
    background: linear-gradient(to right, #f37fc8, #b571fe);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
}

.qr-id {
    font-size: 1.4em;
    text-align: center;
    margin: 0.5em; /* top & bottom, right & left, bottom */
    color: grey;
    margin-bottom: 3em;
}

.modal-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.qr-wrapper {
    position: relative;
    padding: 25px;
    background: white;
    border-radius: 1.6em;
}

.qr-glow-wrapper {
    display: inline-flex; /* This makes the glow wrapper only as wide as its content */
    padding: 3px;
    border-radius: 1.8em;
    background: black;
    background-size: 400% 400%;
    margin: 3em 3em 0.6em;
    animation: gradient 7s ease infinite;
}
