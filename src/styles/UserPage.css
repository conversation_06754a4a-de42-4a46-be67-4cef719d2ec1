.user-card-mb {
  margin-bottom: 20px;
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 2.5px solid #77777799;
  border-radius: 50%;
}

.skeleton-lines {
  flex: 1;
  width: 100%;
}

.skeleton-mb {
  margin-bottom: 0.5rem;
}

.message-mb {
  margin-bottom: 15px;
}

.readonly-input {
  background-color: #f5f5f5 !important;
  cursor: not-allowed;
}

.info-text {
  display: block;
  text-align: left;
}

.info-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.info-icon {
  display: inline-flex;
  margin-right: 5px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 1px solid #777;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  font-weight: bold;
  color: #777;
  flex-shrink: 0;
}

.send-invite-button {
  font-weight: 700;
}

.send-invite-button.delete-button {
  background-color: #d81b60;
}

.send-invite-button.delete-button:hover:not(:disabled) {
  background-color: #c2185b;
}

.send-invite-button.delete-button:disabled {
  background-color: #f48fb1;
  cursor: not-allowed;
}

.info-text.warning-text small {
  color: #d81b60;
  font-style: italic;
}

/* Avatar loading spinner styles */
.avatar-loading-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  z-index: 2;
}

.avatar-loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3478F6;
  animation: avatar-spin 1s ease-in-out infinite;
}

@keyframes avatar-spin {
  to {
    transform: rotate(360deg);
  }
}