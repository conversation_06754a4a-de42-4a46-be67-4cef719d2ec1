/* LoadingLogo.css */

/* Container styling */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* Use % instead of vh/vw for more consistent mobile behavior */
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  /* Add this to account for possible browser toolbars */
  padding-bottom: env(safe-area-inset-bottom);
}

/* Logo wrapper with improved mobile positioning */
.loading-logo {
  position: relative;
  width: 200px;
  height: 80px;
  /* Slight adjustment to compensate for visual perception on mobile */
  transform: translateY(-5%);
  /* Ensure there's no extra margin or padding */
  margin: 0;
  padding: 0;
}

/* Logo image */
.loading-image {
  position: relative;
  width: 100%;
  height: 100%;
  object-fit: contain;
  opacity: 0.3;
}

/* Pulse animation */
.pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 1;
  }
}

/* Fade-in animation */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Fade-out animation */
.fade-out {
  animation: fadeOut 0.3s ease-out;
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* Alternative positioning method for problematic browsers */
@supports (-webkit-touch-callout: none) {
  /* iOS-specific fix */
  .loading-screen {
      height: -webkit-fill-available;
  }
}