/* ===== Status Management Styles ===== */

/* Container Styling */
.status-management-container {
  margin-bottom: 16px;
}

.disabled-feature {
  opacity: 0.75;
  cursor: default;
}

/* Layout for two-column design */
.status-layout-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Status scroll section */
.status-scroll-section {
  flex: 1;
}

/* Status add section */
.status-add-section {
  flex: 0 0 auto;
}

/* Section headers */
.status-section-header {
  font-size: 14px;
  font-weight: 500;
  color: #4b5563;
  margin: 0 0 10px 0;
  line-height: 1.4;
}

.feature-dependency-note {
  font-size: 12px;
  color: #666;
  font-weight: normal;
  margin-left: 5px;
}

/* Status List */
.status-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
  max-height: 200px;
  overflow-y: auto;
  padding-right: 4px;
  margin-bottom: 12px;
  /* Subtle scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: #d1d5db transparent;
}

.status-list::-webkit-scrollbar {
  width: 6px;
}

.status-list::-webkit-scrollbar-track {
  background: transparent;
}

.status-list::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 6px;
}

.status-list-empty {
  padding: 10px;
  background-color: #f9fafb;
  border: 1px dashed #d1d5db;
  border-radius: 6px;
  text-align: center;
  color: #6b7280;
  font-size: 14px;
}

/* Status Item */
.status-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 10px;
  background-color: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  transition: background-color 0.15s ease, border-color 0.15s ease;
  gap: 4px;
}

.status-item:hover {
  border-color: #d1d5db;
  background-color: #f5f7fa;
}

.status-item-content {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
  gap: 6px;
}

.status-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  white-space: nowrap;
  border: 1px solid rgba(0, 0, 0, 0.1);
  min-width: 60px;
  text-align: center;
  flex-shrink: 0;
}

.preview-badge {
  min-width: 70px;
}

/* Color Picker */
.color-picker-wrapper {
  position: relative;
  width: 22px;
  height: 22px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.add-form-color-picker {
  width: 28px;
  height: 28px;
}

.status-color-picker {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  border: none;
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
}

.status-color-picker::-webkit-color-swatch-wrapper {
  padding: 0;
}

.status-color-picker::-webkit-color-swatch {
  border: none;
}

/* Status Name Input */
.status-name-input {
  flex: 1;
  min-width: 0;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 13px;
  background-color: #ffffff;
  color: #374151;
  transition: border-color 0.15s ease, box-shadow 0.15s ease;
  height: 28px;
  line-height: 1.4;
}

.add-form-input {
  height: 32px;
}

.status-name-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15);
}

.status-name-readonly {
  border-color: transparent;
  background-color: transparent;
  cursor: default;
}

.status-name-readonly:focus {
  box-shadow: none;
}

.remove-status-button {
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.15s ease, color 0.15s ease;
  height: 24px;
  width: 24px;
  flex-shrink: 0;
}

.remove-status-button:hover {
  background-color: #fee2e2;
  color: #dc2626;
}

/* Add Status Form */
.add-status-container {
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.add-status-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.add-status-preview {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-preview-label {
  font-size: 12px;
  color: #6b7280;
}

.add-status-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-error-message {
  font-size: 12px;
  color: #dc2626;
  margin-top: -4px;
}

.add-status-button {
  height: 32px;
  padding: 0 12px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  font-size: 13px;
  transition: background-color 0.15s ease;
}

.add-status-button:hover:not(:disabled) {
  background-color: #2563eb;
}

.add-status-button:disabled {
  background-color: #93c5fd;
  cursor: not-allowed;
  opacity: 0.7;
}

/* Responsive Styles */
@media (min-width: 768px) {
  .status-layout-container {
    flex-direction: row;
    align-items: flex-start;
  }
  
  .status-scroll-section {
    flex: 1;
    max-width: 60%;
  }
  
  .status-add-section {
    flex: 1;
    max-width: 40%;
  }
  
  .status-list {
    max-height: 250px;
  }
}

@media (max-width: 767px) {
  .status-list {
    max-height: 180px;
  }
  
  .add-status-inputs {
    flex: 1;
  }
  
  .status-name-input {
    flex: 1;
  }
  
  .add-status-button {
    width: 100%;
  }
}

/* Extra small devices */
@media (max-width: 375px) {
  .status-list {
    max-height: 160px;
  }
  
  .add-status-preview {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .status-item-content {
    flex-wrap: nowrap;
    overflow: hidden;
  }
}