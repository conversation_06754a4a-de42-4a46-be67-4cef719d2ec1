/* AudioPlayer.css - Styling for blog post audio player component */
/* All selectors are prefixed with .blog to ensure proper CSS isolation */

.blog .audio-player-container {
  width: 100%;
  max-width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

.blog .audio-player {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

.blog .audio-player-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.blog .audio-player-title {
  font-weight: 500;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  text-align: left;
  font-size: 0.85rem;
  color: #000;
  display: flex;
  align-items: center;
  gap: 6px;
}

.blog .audio-player-headphones-icon {
  flex-shrink: 0;

  margin-left: 4px;
}

.blog .audio-player-time {
  font-size: 0.85rem;
  color: #666;
  text-align: right;
}

.blog .audio-player-main-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.blog .audio-player-play-button {
  flex-shrink: 0;
  background-color: transparent;
  border: none;
  outline: none;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: opacity 0.2s ease;
  -webkit-tap-highlight-color: transparent; /* Remove tap highlight on mobile */
  padding: 0;
}

.blog .audio-player-play-button svg {
  width: 100%;
  height: 100%;
  display: block;
}

.blog .audio-player-play-button:hover {
  opacity: 0.8;
}

.blog .audio-player-play-button:focus {
  outline: none;
  box-shadow: none;
}

.blog .audio-player-play-button:active {
  outline: none;
  box-shadow: none;
  border: none;
}

.blog .audio-player-play-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  outline: none;
  box-shadow: none;
  border: none;
}

/* Firefox-specific style to remove the dotted outline */
.blog .audio-player-play-button::-moz-focus-inner {
  border: 0;
}

.blog .audio-player-waveform-container {
  flex-grow: 1;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 68px;
  cursor: pointer;
  border-radius: 4px;
  overflow: hidden;
}

/* Make sure the canvas is responsive and centered */
.blog .audio-player-waveform-container canvas {
  width: 100% !important;
  height: auto !important;
  display: block;
  margin: auto;
}

/* Ensure the visualizer container is centered */
.blog .audio-player-waveform-container > div:not(.audio-player-skeleton) {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Add fade-in animation for the visualizer container */
.blog .audio-player-visualizer-container {
  width: 100%;
  height: 100%;
  opacity: 1;
  animation: fadeIn 0.5s ease-in;
  transition: opacity 0.5s ease-in-out;
}

/* Hidden class for toggling visibility */
.blog .hidden {
  opacity: 0;
  pointer-events: none;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.blog .audio-player-loading {
  padding: 20px;
  text-align: center;
  color: #666;
  font-style: italic;
}

.blog .audio-player-error {
  padding: 10px;
  text-align: center;
  color: #d32f2f;
  font-style: italic;
  font-size: 0.85rem;
  margin-top: 10px;
}

.blog .audio-player-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
}

.blog .audio-player-fallback-message {
  color: #666;
  font-style: italic;
  font-size: 0.85rem;
}

.blog .audio-player-loading button {
  margin-left: 10px;
  background: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 0.8rem;
  cursor: pointer;
}

.blog .audio-player-loading button:hover {
  background: #e0e0e0;
}

/* High-resolution visualizer container */
.blog .high-res-visualizer-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Apply high-resolution rendering to the canvas */
.blog .high-res-visualizer-container canvas {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  image-rendering: pixelated;
}

/* Skeleton styling */
.blog .audio-player-skeleton {
  width: 100%;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  display: block !important; /* Override any conflicting display properties */
  transition: opacity 0.5s ease-in-out;
}

/* Ensure the skeleton container takes full width and height */
.blog .audio-player-waveform-container .audio-player-skeleton {
  width: 100% !important;
  height: 60px !important;
  position: relative;
  z-index: 2;
}

/* Ensure the react-loading-skeleton elements are visible */
.blog .audio-player-skeleton .react-loading-skeleton {
  display: block !important;
  width: 100% !important;
  height: 60px !important;
  z-index: 1;
  position: relative;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .blog .audio-player {
    padding: 12px;
  }

  .blog .audio-player-header {
    margin-bottom: 6px;
  }

  .blog .audio-player-title {
    font-size: 0.8rem;
    gap: 4px;
  }

  .blog .audio-player-headphones-icon {
    width: 12px;
    height: 12px;
  }

  .blog .audio-player-main-content {
    gap: 8px;
  }

  .blog .audio-player-play-button {
    width: 40px;
    height: 40px;
  }

  .blog .audio-player-time {
    font-size: 0.75rem;
    min-width: 70px;
  }

  .blog .audio-player-waveform-container {
    height: 60px;
  }
}
