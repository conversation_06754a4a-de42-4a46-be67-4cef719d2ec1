/* BlogReactions.css - Styling for iMessage-like blog post reactions component */

/* Wrapper to create CSS isolation */
.blogreactions-wrapper {
  /* This creates a new stacking context and isolates the component */
  position: relative;
  z-index: 1;
  margin-top: 60px;
  margin-bottom: 60px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  text-align: center;
}

/* iMessage chat container */
.imessage-chat {
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 8px;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin: 0 auto;
  opacity: 1;
  transition: opacity 0.3s ease-in-out, height 0.3s ease-in-out;
  min-height: 300px; /* Ensure a stable minimum height that can fit all content */
}

/* Hidden state before animation */
.imessage-chat.hidden {
  opacity: 0.9; /* Not fully hidden to show typing indicator */
}

/* Visible state after animation */
.imessage-chat.visible {
  opacity: 1;
}

/* Make all elements in hidden chat invisible except the first message bubble */
.imessage-chat.hidden .message-group.left .message-bubble:nth-child(2),
.imessage-chat.hidden .reaction-options-wrapper {
  opacity: 0;
}

/* Hide the second message bubble only when it should be hidden */
.message-group.left .message-bubble:nth-child(2).hidden-message {
  opacity: 0;
}

/* Hidden message class */
.hidden-message {
  opacity: 0 !important;
  visibility: hidden;
}

/* Ensure the typing indicator is always visible */
.typing-indicator {
  opacity: 1 !important;
  z-index: 5; /* Ensure it's above other elements */
  position: relative; /* Needed for z-index to work */
}

/* Message bubble with active typing indicator */
.message-bubble.typing-active {
  opacity: 1 !important;
  visibility: visible !important;
  border-bottom-left-radius: 18px !important; /* Override the default radius when typing */
}

/* Hide the tail when typing indicator is active */
.message-bubble.typing-active::before,
.message-bubble.typing-active::after {
  display: none !important;
}

/* Animation for messages appearing */
.blogreactions-wrapper .animate-in {
  opacity: 0;
  transform: scale(0.95);
  animation-fill-mode: forwards;
}

/* First message animation */
.blogreactions-wrapper .animate-in.first {
  animation: messageAppear 0.4s cubic-bezier(0.18, 0.89, 0.32, 1.28) 0.2s forwards;
}

/* Second message animation */
.blogreactions-wrapper .animate-in.second {
  animation: messageAppear 0.4s cubic-bezier(0.18, 0.89, 0.32, 1.28) 0.5s forwards;
}

/* Third element (reaction options wrapper) animation */
.blogreactions-wrapper .animate-in.third {
  animation: messageAppear 0.4s cubic-bezier(0.18, 0.89, 0.32, 1.28) 2s forwards;
}

/* Animation for user reaction */
.blogreactions-wrapper .message-group.right .animate-in {
  animation: messageAppear 0.3s cubic-bezier(0.18, 0.89, 0.32, 1.28) forwards;
}

@keyframes messageAppear {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Message groups (left and right) */
.message-group {
  display: flex;
  flex-direction: column;
  gap: 2px;
  max-width: 70%;
  position: relative;
}

/* Left messages (from Thinkertags) */
.message-group.left {
  align-self: flex-start;
  margin-left: 10px; /* Add space for the tail */
}

/* Right messages (user reactions) */
.message-group.right {
  align-self: flex-end;
  margin-right: 10px; /* Add space for the tail */
  margin-top: 15px; /* Add space between left and right messages */
}

/* Message bubbles */
.message-bubble {
  padding: 8px 16px;  /* Increased left and right padding from 12px to 16px */
  border-radius: 18px;
  font-size: 16px;
  line-height: 1.4;
  position: relative;
  display: inline-flex; /* Changed from inline-block to inline-flex */
  align-items: center; /* Center content vertically */
  max-width: 100%;
  margin-bottom: 2px;
  text-align: left; /* Ensure text is always left-aligned */
  min-height: 22px; /* Ensure consistent height */
  box-sizing: border-box; /* Ensure padding doesn't add to height */
  transition: height 0.3s ease; /* Smooth height transitions */
}

/* Special styling for short messages to make them snug */
.short-bubble {
  padding: 8px 16px;  /* Match padding with regular bubbles for consistent height */
  min-width: auto;
  width: auto;
  display: inline-flex; /* Match display with regular bubbles */
  max-width: fit-content;
}

/* Left message bubble styling (gray) */
.message-group.left .message-bubble {
  background-color: #e5e5ea;
  color: #000;
  border-bottom-left-radius: 4px;
  position: relative;
  z-index: 1;
}

/* Add curved tail to the second message bubble (What did you think?) */
.message-group.left .message-bubble:nth-child(2)::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: -10px;
  width: 20px;
  height: 20px;
  background-color: #e5e5ea;
  border-bottom-right-radius: 18px;
  border-bottom-left-radius: 4px;
  /* Remove box-shadow to prevent interference */
  z-index: -1;
}

.message-group.left .message-bubble:nth-child(2)::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: -20px;
  width: 20px;
  height: 20px;
  background-color: #f5f5f5; /* Same as chat background */
  border-bottom-right-radius: 16px;
  border-bottom-left-radius: 0;
  z-index: -1;
}

/* First message in left group has different shape */
.message-group.left .message-bubble:first-child {
  border-top-left-radius: 18px;
  border-bottom-left-radius: 18px;
  margin-bottom: 6px;
}

/* Last message in left group has different shape */
.message-group.left .message-bubble:last-child {
  border-bottom-left-radius: 18px;
  margin-bottom: 5px; /* Add space for the tail */
}

/* Right message bubble styling (blue) */
.message-group.right .message-bubble {
  background-color: #1982FC;
  color: white;
  border-bottom-right-radius: 4px;
  box-shadow: 0 1px 3px #0000000d;
  position: relative;
  z-index: 1;
}

/* Add curved tail to right message bubbles - at the bottom right */
.message-group.right .message-bubble:first-child::before {
  content: "";
  position: absolute;
  bottom: 0;
  right: -10px;
  width: 20px;
  height: 20px;
  background-color: #1982FC;
  border-bottom-left-radius: 18px;
  border-bottom-right-radius: 4px;
  /* Remove box-shadow to prevent interference */
  z-index: -1;
}

.message-group.right .message-bubble:first-child::after {
  content: "";
  position: absolute;
  bottom: 0;
  right: -20px;
  width: 20px;
  height: 20px;
  background-color: #f5f5f5; /* Same as chat background */
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 0;
  z-index: -1;
}

/* First message in right group has different shape */
.message-group.right .message-bubble:first-child {
  border-top-right-radius: 18px;
  margin-bottom: 5px; /* Add space for the tail */
}

/* Last message in right group has different shape */
.message-group.right .message-bubble:last-child {
  border-bottom-right-radius: 18px;
}

/* Animation for message bubbles - not used with scroll animation */
@keyframes popIn {
  from { opacity: 0; transform: scale(0.8); }
  to { opacity: 1; transform: scale(1); }
}

/* Typing indicator (three dots) */
.typing-indicator {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  height: 22px; /* Match the line-height of text */
  min-width: 30px; /* Ensure minimum width for the indicator */
  padding: 0 2px; /* Add a bit of padding */
  box-sizing: border-box; /* Ensure padding doesn't add to height */
}

.typing-indicator span {
  display: inline-block;
  width: 7px;
  height: 7px;
  background-color: #777;
  border-radius: 50%;
  animation: typingBounce 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingBounce {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-4px);
  }
}

/* Reaction options wrapper */
.reaction-options-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 40px auto 5px;
  position: relative;
}

/* "React" text prompt */
.reaction-prompt {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  font-size: 12px;
  color: #777;
  margin-bottom: 6px;
  font-weight: 400;
}

/* Reaction options container - mimics iMessage reaction bar */
.reaction-options-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  padding: 12px 15px;
  background-color: white;
  border-radius: 24px;
  box-shadow: 0 1px 3px #0000000d;
  position: relative;
  width: 100%;
}

/* Individual reaction option button - mimics iMessage tapback options */
.reaction-option {
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  width: 50px;
  height: 50px;
  position: relative;
}

.reaction-option:hover {
  background-color: #f0f0f0;
  transform: scale(1.1);
}

.reaction-option.selected {
  background-color: #f0f0f0;
}

.reaction-option.selected::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 5px;
  height: 5px;
  background-color: #1982FC;
  border-radius: 50%;
}

/* Emoji styling */
.reaction-emoji {
  font-size: 2rem;
  line-height: 1;
  user-select: none;
}

/* Emoji in the right message bubble */
.message-group.right .reaction-emoji {
  font-size: 2.5rem;
}

/* Responsive styles */
@media (max-width: 768px) {
  .blogreactions-wrapper {
    margin-top: 40px;
    margin-bottom: 40px;
  }

  .imessage-chat {
    padding: 16px;
  }

  .message-group {
    max-width: 80%;
  }

  .message-bubble {
    padding: 8px 12px;
    font-size: 15px;
  }

  .reaction-prompt {
    font-size: 11px;
    margin-bottom: 5px;
  }

  .reaction-options-container {
    gap: 15px;
    padding: 12px;
  }

  .reaction-option {
    width: 45px;
    height: 45px;
  }

  .reaction-emoji {
    font-size: 1.8rem;
  }
}

/* Small screens */
@media (max-width: 480px) {
  .message-group {
    max-width: 85%;
  }

  .reaction-prompt {
    font-size: 10px;
    margin-bottom: 4px;
  }

  .reaction-options-container {
    gap: 10px;
    padding: 10px;
    max-width: 250px;
  }

  .reaction-option {
    width: 40px;
    height: 40px;
  }

  .reaction-emoji {
    font-size: 1.6rem;
  }

  .message-group.right .reaction-emoji {
    font-size: 2rem;
  }
}
