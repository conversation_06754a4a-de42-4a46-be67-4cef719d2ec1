/* RelatedBlogPosts.css - Styling for related blog posts component */
/* All selectors are prefixed with .blog to ensure proper CSS isolation */

.blog .related-posts {
  margin-top: 40px;
  margin-bottom: 30px;
  width: 100%;
  box-sizing: border-box;
}

.blog .related-posts-title {
  font-family: 'Playfair Display', serif;
  font-size: 28px;
  font-weight: 500;
  margin-top: 0;
  margin-bottom: 15px;
  color: #121212;
  text-align: left;
  text-transform: uppercase;
  font-variant: normal;
  letter-spacing: 0.02em;
  line-height: 1.4;
  padding-bottom: 2px;
  border-bottom: 1px solid #e0e0e0;
  font-stretch: condensed;
  transform: scaleY(1.2);
  transform-origin: bottom;
}

.blog .related-posts-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  width: 100%;
}

.blog .related-post-card {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  text-decoration: none;
  color: inherit;
  height: 100%;
}

.blog .related-post-card:hover,
.blog .related-post-card:focus,
.blog .related-post-card:active {
  text-decoration: none;
}

.blog .related-post-image {
  width: 100%;
  position: relative;
  padding-top: 75%; /* This creates a 4:3 aspect ratio */
  overflow: hidden;
  border-radius: 0; /* Ensure sharp corners */
}

.blog .related-post-image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0; /* Ensure sharp corners */
}

.blog .related-post-content {
  padding: 10px 0;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.blog .related-post-title {
  font-family: 'Playfair Display', serif;
  font-size: 18px;
  font-weight: 800;
  margin: 0 0 5px 0;
  color: #121212;
  line-height: 1.2;
  letter-spacing: -0.02em;
  text-transform: none; /* Ensure it's not uppercase like the headings */
}

.blog .related-post-subtitle {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  font-size: 0.85rem;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

.blog .related-posts-loading,
.blog .related-posts-error {
  padding: 15px;
  text-align: center;
  color: #666;
  background-color: #f9f9f9;
}

.blog .related-posts-error {
  color: #d32f2f;
}

/* Override any border-radius from blog post content images */
.blog .related-posts .related-post-image img {
  border-radius: 0;
}

/* Remove underline on hover for related post links */
.blog .related-post-card:hover {
  text-decoration: none;
}

.blog .related-post-card:hover .related-post-title {
  text-decoration: none;
}

/* Responsive styles */
@media (max-width: 768px) {
  .blog .related-posts-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .blog .related-posts-title {
    font-size: 24px;
  }

  .blog .related-post-title {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .blog .related-posts-grid {
    grid-template-columns: 1fr;
  }

  /* Keep the 4:3 aspect ratio on mobile */
  .blog .related-post-image {
    padding-top: 75%;
  }

  /* Increase font size for titles on small screens */
  .blog .related-post-title {
    font-size: 26px; /* Significantly larger than the default size */
    line-height: 1.3; /* Adjusted line height for better readability */
    margin-bottom: 8px; /* Slightly more space between title and subtitle */
  }

  /* Adjust subtitle size for better proportion */
  .blog .related-post-subtitle {
    font-size: 1rem; /* Slightly larger subtitle */
  }
}
