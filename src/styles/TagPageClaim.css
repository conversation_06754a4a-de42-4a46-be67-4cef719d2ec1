@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');


.settings-input-group{
  border-radius: 10px;
}

.skeleton-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.skeleton-lines {
  text-align: left;
  flex-grow: 1;
  align-self: center;
}

.verification-message {
  padding-bottom: 13px;
}


.claim-page-title{
  font-family: 'The Bold Font';
  color: #343434;
  font-size: 1.7em;
  padding: 1em 0em;
}

/* Container styles */
.verification-code-container {
  display: flex;
  gap: 8px;
  justify-content: space-between;
  margin-top: 10px;
  width: 100%;
}

/* Text styles */
.verification-text {
  color: #666;
  font-size: 14px;
  margin: 8px 0;
  margin-bottom: 14px;
}

/* Input styles */
.verification-input {
  width: 42px;
  height: 70px;
  border: 1px solid #ddd;
  border-radius: 8px;
  text-align: center;
  font-size: 37px;
  font-family: "JetBrains Mono", monospace;
  font-optical-sizing: auto;
  font-style: normal;
  color: #343434;
  background: white;
  margin-bottom: 20px;
  padding: 0;
  line-height: 70px;
  outline: none;
}

/* Input states */
.verification-input:disabled {
  background-color: #f5f5f5;
  color: #666;
  cursor: not-allowed;
}

.verification-input:focus {
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

/* Responsive styles */
@media screen and (max-width: 380px) {
  .verification-code-container {
    gap: 4px;
  }
  
  .verification-input {
    width: 40px;
    height: 60px;
    font-size: 30px;
    line-height: 60px; /* Added to match new height */
  }
}