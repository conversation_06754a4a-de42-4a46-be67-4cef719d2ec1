/* variables.css - Global CSS variables for Thinkertags */
:root {
  --primary-color: #121212;
  --secondary-color: #333333;
  --accent-color: #0066cc;
  --accent-highlight: #0f4e8d;
  --light-color: #f8f8f8;
  --text-color: #060606;
  --light-text: #ffffff;
  --border-radius: 8px;
  --border-radius-cta: 30px;
  --transition-speed: 0.3s;
  --container-width: 1200px;
  --spacing-xs: 8px;
  --spacing-sm: 16px;
  --spacing-md: 24px;
  --spacing-lg: 48px;
  --spacing-xl: 80px;
}

@font-face {
  font-family: 'The Bold Font';
  src: 
  url('/fonts/THEBOLDFONT.ttf') format('truetype'),
  url('/fonts/THEBOLDFONT.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Damion';
  src:
  url('/fonts/Damion-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh; /* change from height to min-height */
  /* overflow-x: hidden; */
}

/* Remove padding for the landing page specifically */
body:has(.landing-page) .app {
  padding-left: 0 !important;
  padding-right: 0 !important;
  max-width: 100% !important;
  overflow-x: hidden !important;
}

@media screen and (min-width: 768px) {
  .app {
    padding-left: 5vw; 
    padding-right: 5vw;
  }
}

@media screen and (min-width: 1200px) {
  .app {
    padding-left: 10em; 
    padding-right: 10em;
  }
}

.h3-container {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-direction: column;
}

label {
  color: #5f5f5f;
  font-size: 13px;
  font-weight: 500;
}

svg {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  shape-rendering: crispEdges;
}