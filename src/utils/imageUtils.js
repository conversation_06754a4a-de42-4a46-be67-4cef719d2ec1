/**
 * Utility functions for image processing
 */

/**
 * Crops an image to a square (1:1 aspect ratio) or social media aspect ratio and returns a data URL
 * The function centers the crop on the image
 *
 * @param {string} imageUrl - URL of the image to crop
 * @param {number} size - Size of the output image width (default: 1200)
 * @param {string} format - Output format (default: 'image/jpeg')
 * @param {number} quality - JPEG quality (0-1, default: 0.9)
 * @param {boolean} forSocialMedia - If true, creates a 1200x630 image for social media (default: false)
 * @returns {Promise<string>} - Data URL of the cropped image
 */
export const cropImageToSquare = (imageUrl, size = 1200, format = 'image/jpeg', quality = 0.9, forSocialMedia = false) => {
  return new Promise((resolve, reject) => {
    // Create an image element to load the image
    const img = new Image();

    // Handle CORS issues
    img.crossOrigin = 'anonymous';

    // Set up load and error handlers
    img.onload = () => {
      try {
        // Create a canvas element
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // Set canvas size based on the desired output
        if (forSocialMedia) {
          // Use optimal social media aspect ratio (1.91:1 or close to 1200x630)
          canvas.width = size;
          canvas.height = Math.round(size * 0.525); // Approximately 1200x630 ratio
        } else {
          // Square image
          canvas.width = size;
          canvas.height = size;
        }

        // Calculate the crop dimensions
        let sourceX, sourceY, sourceWidth, sourceHeight;

        if (forSocialMedia) {
          // For social media, try to use the full width and crop height if needed
          const targetAspectRatio = canvas.width / canvas.height;
          const imageAspectRatio = img.width / img.height;

          if (imageAspectRatio > targetAspectRatio) {
            // Image is wider than needed, crop the sides
            sourceHeight = img.height;
            sourceWidth = img.height * targetAspectRatio;
            sourceX = (img.width - sourceWidth) / 2;
            sourceY = 0;
          } else {
            // Image is taller than needed, crop the top/bottom
            sourceWidth = img.width;
            sourceHeight = img.width / targetAspectRatio;
            sourceX = 0;
            sourceY = (img.height - sourceHeight) / 2;
          }
        } else {
          // For square images, use the original logic
          if (img.width > img.height) {
            // Landscape image
            sourceHeight = img.height;
            sourceWidth = sourceHeight;
            sourceX = (img.width - sourceWidth) / 2;
            sourceY = 0;
          } else {
            // Portrait or square image
            sourceWidth = img.width;
            sourceHeight = sourceWidth;
            sourceX = 0;
            sourceY = (img.height - sourceHeight) / 2;
          }
        }

        // Draw the cropped image on the canvas
        ctx.drawImage(
          img,
          sourceX, sourceY, sourceWidth, sourceHeight, // Source rectangle
          0, 0, canvas.width, canvas.height // Destination rectangle
        );

        // Convert canvas to data URL
        const dataUrl = canvas.toDataURL(format, quality);

        // Resolve the promise with the data URL
        resolve(dataUrl);
      } catch (error) {
        console.error('Error cropping image:', error);
        reject(error);
      }
    };

    img.onerror = (error) => {
      console.error('Error loading image for cropping:', error);
      reject(error);
    };

    // Start loading the image
    img.src = imageUrl;
  });
};

/**
 * Checks if a URL is absolute (starts with http:// or https://)
 *
 * @param {string} url - URL to check
 * @returns {boolean} - True if the URL is absolute
 */
export const isAbsoluteUrl = (url) => {
  return /^https?:\/\//i.test(url);
};

/**
 * Ensures a URL is absolute by adding the origin if necessary
 *
 * @param {string} url - URL to process
 * @returns {string} - Absolute URL
 */
export const ensureAbsoluteUrl = (url) => {
  if (!url) return '';
  return isAbsoluteUrl(url) ? url : `${window.location.origin}${url.startsWith('/') ? '' : '/'}${url}`;
};
