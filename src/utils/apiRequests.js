import { API, Auth } from 'aws-amplify';

export const createErrorHandler = (setMessage) => (error, defaultMessage = 'An error occurred') => {
  const errorMessage = error?.message || defaultMessage;
  console.error(errorMessage, error);
  setMessage(prev => ({
    ...prev,
    message: errorMessage
  }));
  return errorMessage;
};

export const makeApiRequestWithCancellation = (method, path, tokens) => {
  const controller = new AbortController();
  
  const executeRequest = async (body = null, timeoutMs = 15000, retryCount = 0) => {
    const timeoutPromise = new Promise((_, reject) => {
      const id = setTimeout(() => {
        clearTimeout(id);
        reject(new Error(`Request timeout after ${timeoutMs}ms`));
      }, timeoutMs);
    });

    if (!tokens) {
      throw new Error('No authentication tokens available');
    }

    const options = {
      headers: {
        Authorization: `Bearer ${tokens.accessToken}`,
        'id-Token': tokens.idToken
      },
      signal: controller.signal
    };
    
    if (body) {
      options.body = body;
    }

    try {
      const result = await Promise.race([
        API[method]('api', `${path}${path.includes('?') ? '&' : '?'}_t=${Date.now()}`, options),
        timeoutPromise
      ]);
      
      return result;
    } catch (error) {
      if (error.name === 'AbortError') {
        throw new Error('Request was cancelled');
      }
      
      if (error.message === 'Network Error' || error.message.includes('timeout')) {
        if (retryCount < 2) {
          const delay = 1000 * Math.pow(2, retryCount);
          console.log(`Retrying API request after ${delay}ms (attempt ${retryCount + 1})`);
          
          return new Promise(resolve => {
            setTimeout(() => {
              resolve(executeRequest(body, timeoutMs, retryCount + 1));
            }, delay);
          });
        }
      }
      
      if (error.response) {
        if (error.response.status === 401) {
          throw new Error('Authentication failed. Please log in again.');
        }
        if (error.response.status === 403) {
          throw new Error('You do not have permission to perform this action.');
        }
      }
      
      throw error;
    }
  };
  
  return {
    execute: executeRequest,
    cancel: () => controller.abort()
  };
};

export const fetchAllUserData = async (username, tokens) => {
  const userRequest = makeApiRequestWithCancellation('get', `/users/${username}`, tokens);
  
  try {
    const currentUserPromise = Auth.currentAuthenticatedUser().then(authUser => {
      const currentUserRequest = makeApiRequestWithCancellation('get', `/users/${authUser.username}`, tokens);
      return currentUserRequest.execute();
    });
    
    const [userData, currentUser] = await Promise.all([
      userRequest.execute(),
      currentUserPromise
    ]);
    
    return { userData, currentUser };
  } catch (error) {
    userRequest.cancel();
    throw error;
  }
};

export const fetchUserData = fetchAllUserData;