/**
 * Generates JSON-LD structured data for blog posts
 * @param {Object} postData - The blog post data
 * @param {string} postData.title - The post title
 * @param {string} postData.description - The post description
 * @param {string} postData.url - The canonical URL of the post
 * @param {string} postData.imageUrl - The URL of the post's main image
 * @param {string} postData.author - The post author's name
 * @param {string} postData.datePublished - The publication date (ISO format)
 * @param {string} postData.dateModified - The last modification date (ISO format)
 * @returns {Object} - The structured data object
 */
export const generateArticleStructuredData = (postData) => {
  const {
    title,
    description,
    url,
    imageUrl,
    author,
    datePublished,
    dateModified = datePublished // Default to published date if modified not provided
  } = postData;

  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: title,
    description: description,
    image: imageUrl,
    author: {
      '@type': 'Person',
      name: author
    },
    publisher: {
      '@type': 'Organization',
      name: 'Thinkertags',
      logo: {
        '@type': 'ImageObject',
        url: 'https://thinkertags.com/logo512.png'
      }
    },
    url: url,
    datePublished: datePublished,
    dateModified: dateModified
  };
};

/**
 * Generates JSON-LD structured data for the organization
 * @returns {Object} - The structured data object
 */
export const generateOrganizationStructuredData = () => {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'Thinkertags',
    url: 'https://thinkertags.com',
    logo: 'https://thinkertags.com/logo512.png',
    sameAs: [
      'https://twitter.com/thinkertags',
      'https://www.linkedin.com/company/thinkertags'
    ]
  };
};

/**
 * Injects structured data into the document head
 * @param {Object} structuredData - The structured data object
 */
export const injectStructuredData = (structuredData) => {
  // Remove any existing structured data script
  const existingScript = document.querySelector('script[type="application/ld+json"]');
  if (existingScript) {
    existingScript.remove();
  }

  // Create and inject the new script
  const script = document.createElement('script');
  script.type = 'application/ld+json';
  script.textContent = JSON.stringify(structuredData);
  document.head.appendChild(script);
};
