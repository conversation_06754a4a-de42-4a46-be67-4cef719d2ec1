export const storeFormStateInLocalStorage = (formData, userId) => {
    try {
      const key = `userform_${userId}`;
      const dataToStore = {
        displayName: formData.displayName,
        role: formData.role,
        timestamp: Date.now()
      };
      localStorage.setItem(key, JSON.stringify(dataToStore));
    } catch (error) {
      console.error('Error storing form data in localStorage:', error);
    }
  };

  export const loadFormStateFromLocalStorage = (userId) => {
    try {
      const key = `userform_${userId}`;
      const storedData = localStorage.getItem(key);
      if (!storedData) return null;
      
      const parsedData = JSON.parse(storedData);
      if (Date.now() - parsedData.timestamp < 30 * 60 * 1000) {
        return parsedData;
      }
      localStorage.removeItem(key);
      return null;
    } catch (error) {
      console.error('Error loading form data from localStorage:', error);
      return null;
    }
  };
  
  export const clearFormStateFromLocalStorage = (userId) => {
    try {
      const key = `userform_${userId}`;
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Error cleaning up localStorage:', error);
    }
  };