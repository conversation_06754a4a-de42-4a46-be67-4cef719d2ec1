# Utility Functions

This directory contains utility functions used throughout the application.

## Debug Utilities

The `debugUtils.js` file contains utilities for debugging and development purposes.

### Debug Mode

Debug mode can be enabled by adding specific URL parameters. These parameters only work in development mode and are disabled in production for security reasons.

#### Available Debug Parameters

- `debug_skeleton` - Forces skeleton loading states to remain visible, allowing you to test and style loading states without having to simulate slow network conditions.
- `debug_pageLoading` - Forces the loading state to remain active in the BlogPost component.
- `debug_show_quotes` - Shows quote cards on the landing page in development mode (always shown in production).

#### Usage

To use debug mode, simply add the parameter to the URL:

```
http://localhost:5173/research-and-insights/your-blog-post?debug_skeleton
http://localhost:5173/?debug_show_quotes
```

You can also combine multiple debug parameters:

```
http://localhost:5173/?debug_show_quotes&debug_skeleton
```

#### Implementation

To add a new debug parameter:

1. Use the `isDebugModeEnabled` function from `debugUtils.js`:

```javascript
import { isDebugModeEnabled } from '../utils/debugUtils';

// Check if a specific debug mode is enabled
const isSkeletonDebugMode = isDebugModeEnabled('debug_skeleton');
```

2. This function automatically checks if the application is running in development mode before enabling the debug feature, ensuring that debug modes are never available in production.

#### Security

Debug modes are automatically disabled in production environments to prevent any potential security issues or unintended behavior. The `isDevelopmentMode` function checks the environment and only allows debug parameters to work in development.
