import { openDB } from 'idb';
import * as XLSX from 'xlsx';

// Initialize IndexedDB for asset IDs
export const initAssetIdsDB = async () => {
  return openDB('asset-ids-db', 1, {
    upgrade(db) {
      // Create a store with 'id' as both the key path and value
      if (!db.objectStoreNames.contains('assetIds')) {
        db.createObjectStore('assetIds', { keyPath: 'id' });
      }
    }
  });
};

// Function to load example data from template files
export const loadExampleData = async (templateId) => {
  try {
    const response = await fetch(`/example_templates/${templateId}_template.xlsx`);
    const arrayBuffer = await response.arrayBuffer();
    const data = new Uint8Array(arrayBuffer);
    
    const workbook = XLSX.read(data, { type: 'array' });
    const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
    const rows = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });
    
    // Skip header row and process data rows
    return rows.slice(1).map(row => {
      const paddedRow = [...row];
      while (paddedRow.length < 7) {
        paddedRow.push('');
      }
      return paddedRow;
    });
  } catch (error) {
    console.error(`Error loading template ${templateId}:`, error);
    return [];
  }
};

export const geocodeAddress = async (address) => {
    // Rate limiting - wait 1 second between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const encodedAddress = encodeURIComponent(address);
    const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodedAddress}&limit=1`);
    const data = await response.json();
    
    if (data && data[0]) {
        return {
            latitude: parseFloat(data[0].lat),
            longitude: parseFloat(data[0].lon)
        };
    }
    return null;
};

export const validateAssetId = (id) => {
    if (!id) return { valid: false, error: 'Asset ID is required' };

    // Convert to string in case it's a number
    const idStr = String(id).trim();

    // Check if empty after trimming
    if (idStr === '') {
        return { valid: false, error: 'Asset ID cannot be empty' };
    }

    // Check for invalid characters in the ID
    // Allow: alphanumeric, hyphens, underscores
    if (!/^[a-zA-Z0-9-_]+$/.test(idStr)) {
        return {
            valid: false,
            error: 'Asset ID can only contain letters, numbers, hyphens and underscores'
        };
    }

    // Check length - allowing for both short incremental IDs and UUIDs
    if (idStr.length > 36) {
        return {
            valid: false,
            error: 'Asset ID cannot be longer than 36 characters'
        };
    }

    return { valid: true, id: idStr };
};

export const validateCoordinate = (value, type) => {
    if (!value) return { valid: true }; // Optional field
    
    const num = parseFloat(value);
    if (isNaN(num)) {
        return { valid: false, error: `Invalid ${type} coordinate` };
    }
    
    if (type === 'latitude' && (num < -90 || num > 90)) {
        return { valid: false, error: 'Latitude must be between -90 and 90' };
    }
    if (type === 'longitude' && (num < -180 || num > 180)) {
        return { valid: false, error: 'Longitude must be between -180 and 180' };
    }
    return { valid: true, value: num };
};