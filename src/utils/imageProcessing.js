const ImageProcessor = (() => {
    let canvas = null;
    let ctx = null;
    
    const getContext = () => {
      if (!ctx) {
        if (typeof window !== 'undefined' && typeof document !== 'undefined') {
          canvas = document.createElement('canvas');
          ctx = canvas.getContext('2d');
        } else {
          throw new Error('Canvas operations not supported in this environment');
        }
      }
      return { canvas, ctx };
    };

    // Helper to get file MIME type
    const getFileMimeType = (file) => {
      // Try to get MIME type from the file object
      if (file.type) {
        return file.type;
      }
      
      // Fallback to guessing from extension
      const extension = file.name?.split('.').pop()?.toLowerCase();
      if (extension) {
        switch (extension) {
          case 'png': return 'image/png';
          case 'jpg':
          case 'jpeg': return 'image/jpeg';
          case 'gif': return 'image/gif';
          case 'webp': return 'image/webp';
          default: return 'image/jpeg'; // Default to JPEG if unknown
        }
      }
      
      // Default to JPEG if we can't determine
      return 'image/jpeg';
    };

    // Helper to get file extension from mime type
    const getExtensionFromMimeType = (mimeType) => {
      if (mimeType.includes('png')) return 'png';
      if (mimeType.includes('gif')) return 'gif';
      if (mimeType.includes('webp')) return 'webp';
      return 'jpg'; // Default to jpg for jpeg or unknown types
    };
    
    return {
      processImage: (file, quality = 0.8, maxSize = 800, forceJPEG = true) => {
        return new Promise((resolve, reject) => {
          try {
            const { canvas, ctx } = getContext();
            
            const img = new Image();
            const objectUrl = URL.createObjectURL(file);
            
            // Get mime type from file
            const mimeType = getFileMimeType(file);
            // Use JPEG for most images (better compression), but keep PNG for transparency
            const outputMimeType = forceJPEG ? 'image/jpeg' : mimeType;
            const fileExtension = getExtensionFromMimeType(outputMimeType);
            
            img.onload = function() {
              try {
                let width = img.width;
                let height = img.height;
                
                if (width > height && width > maxSize) {
                  height = Math.round(height * (maxSize / width));
                  width = maxSize;
                } else if (height > maxSize) {
                  width = Math.round(width * (maxSize / height));
                  height = maxSize;
                }
                
                // Round dimensions to integers
                width = Math.round(width);
                height = Math.round(height);
                
                canvas.width = width;
                canvas.height = height;
                
                // Clear canvas
                ctx.clearRect(0, 0, width, height);
                
                // For PNG with transparency, set transparent background
                if (mimeType === 'image/png' && !forceJPEG) {
                  ctx.fillStyle = 'rgba(0, 0, 0, 0)'; // Transparent background
                  ctx.fillRect(0, 0, width, height);
                } else {
                  ctx.fillStyle = '#FFFFFF'; // White background
                  ctx.fillRect(0, 0, width, height);
                }
                
                // Draw the image
                ctx.drawImage(img, 0, 0, width, height);
                
                // Get compressed data URL using appropriate mime type
                const compressedBase64 = canvas.toDataURL(outputMimeType, quality);
                
                // Extract raw base64 data (without data URL prefix)
                const rawBase64 = compressedBase64.split(',')[1] || compressedBase64;
                
                // Clean up
                URL.revokeObjectURL(objectUrl);
                
                // Return result with metadata
                resolve({
                  base64: compressedBase64,       // Full data URL (with prefix)
                  rawBase64: rawBase64,           // Just the base64 data (for API calls)
                  mimeType: outputMimeType,       // The mime type of the output
                  fileExtension: fileExtension,   // The file extension to use
                  width,
                  height,
                  aspectRatio: width / height
                });
              } catch (error) {
                URL.revokeObjectURL(objectUrl);
                reject(error);
              }
            };
            
            img.onerror = function() {
              URL.revokeObjectURL(objectUrl);
              reject(new Error('Failed to load image'));
            };
            
            img.src = objectUrl;
          } catch (error) {
            if (error.message === 'Canvas operations not supported in this environment') {
              console.warn('Image processing not available in SSR environment');
              resolve(null);
            } else {
              reject(error);
            }
          }
        });
      },
      
      isSupported: () => {
        return typeof window !== 'undefined' && typeof document !== 'undefined';
      },
  
      clearContext: () => {
        if (canvas && ctx) {
          ctx.clearRect(0, 0, canvas.width, canvas.height);
        }
        canvas = null;
        ctx = null;
      }
    };
  })();
  
  /**
   * Process an image file for upload
   * @param {File} file - The image file to process
   * @param {number} quality - Quality level between 0 and 1
   * @param {boolean} forceJPEG - Whether to force JPEG output (default: true)
   * @returns {Promise<string>} - Base64 data URL of the processed image
   */
  export const processImageForUpload = async (file, quality = 0.7, forceJPEG = true) => {
    if (!ImageProcessor.isSupported()) {
      return null;
    }
    
    try {
      const result = await ImageProcessor.processImage(file, quality, 800, forceJPEG);
      return result?.base64 || null;
    } catch (error) {
      console.error('Image processing error:', error);
      return null;
    }
  };
  
  /**
   * Process an image file and return only the raw base64 data without prefix
   * @param {File} file - The image file to process
   * @param {number} quality - Quality level between 0 and 1
   * @param {boolean} forceJPEG - Whether to force JPEG output (default: true)
   * @returns {Promise<{rawBase64: string, fileExtension: string}>} - Raw base64 data and file extension
   */
  export const processImageForAPI = async (file, quality = 0.7, forceJPEG = true) => {
    if (!ImageProcessor.isSupported()) {
      return { rawBase64: null, fileExtension: 'jpg' };
    }
    
    try {
      const result = await ImageProcessor.processImage(file, quality, 800, forceJPEG);
      return {
        rawBase64: result?.rawBase64 || null,
        fileExtension: result?.fileExtension || 'jpg'
      };
    } catch (error) {
      console.error('Image processing error:', error);
      return { rawBase64: null, fileExtension: 'jpg' };
    }
  };
  
  export const MAX_IMAGE_SIZE = 5 * 1024 * 1024;
  export default ImageProcessor;