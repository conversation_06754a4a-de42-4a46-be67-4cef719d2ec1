/**
 * Custom renderers for ReactMarkdown
 */
import React from 'react';
import { transformImageUrl } from './blogUtils.js';

/**
 * Creates custom renderers for ReactMarkdown
 * @param {string} postId - The post ID/slug for image path resolution
 * @returns {Object} - Object with renderer components
 */
export const createMarkdownRenderers = (postId) => {
  return {
    // Ensure horizontal rules are properly rendered with consistent styling
    hr: () => <hr className="blog-post-divider" />,

    // Paragraph renderer with drop cap handling
    p: ({children, ...props}) => {
      if (!children) {
        return <p className="blog-post-paragraph no-drop-cap" {...props}></p>;
      }

      // Check for drop cap marker in string or array of children
      let hasDropCapMarker = false;
      let processedChildren = children;

      // Process string children
      if (typeof children === 'string' && children.startsWith('[dropcap]')) {
        hasDropCapMarker = true;
        processedChildren = children.substring('[dropcap]'.length);
      }
      // Process array children
      else if (Array.isArray(children) &&
               typeof children[0] === 'string' &&
               children[0].startsWith('[dropcap]')) {
        hasDropCapMarker = true;
        const newChildren = [...children];
        newChildren[0] = children[0].substring('[dropcap]'.length);
        processedChildren = newChildren;
      }

      // Apply appropriate class based on drop cap marker
      const className = hasDropCapMarker
        ? "blog-post-paragraph"
        : "blog-post-paragraph no-drop-cap";

      return <p className={className} {...props}>{processedChildren}</p>;
    },

    // Handle headings - specifically hide "Introduction" h2 headings
    h2: ({children, ...props}) => {
      const text = children && children.toString();
      return text === 'Introduction' ? null : <h2 {...props}>{children}</h2>;
    },

    // Handle h3 headings - simplified
    h3: ({children, ...props}) => <h3 {...props}>{children}</h3>,

    // Ensure proper styling for strong text
    strong: ({children, ...props}) => {
      const text = children && children.toString();

      if (text && text.includes('Practical Application')) {
        return <strong className="blog-post-practical-application" {...props}>{children}</strong>;
      }

      if (text && text.length > 0 && !text.includes(' ') && text.endsWith(':')) {
        return <strong className="blog-post-strong-text" {...props}>{children}</strong>;
      }

      return <strong {...props}>{children}</strong>;
    },

    // Handle image paths using our helper function
    img: ({src, alt, ...props}) => {
      const imageSrc = src ? transformImageUrl(src, postId) : src;

      return (
        <img
          src={imageSrc}
          alt={alt || ''}
          {...props}
          className="blog-post-content-image"
          onError={(e) => {
            console.warn(`Image not found: ${imageSrc}`);
            e.target.style.display = 'none';
          }}
        />
      );
    }
  };
};



/**
 * Creates a URL transform function for image paths
 * @param {string} postId - The post ID/slug
 * @returns {Function} - URL transform function
 */
export const createUrlTransform = (postId) => {
  return (url) => {
    if (url.match(/\.(jpeg|jpg|gif|png|webp|svg)$/i)) {
      return transformImageUrl(url, postId);
    }
    return url;
  };
};
