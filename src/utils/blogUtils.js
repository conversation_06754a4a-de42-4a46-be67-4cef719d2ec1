/**
 * Utility functions for blog post processing
 */
import { ensureAbsoluteUrl, cropImageToSquare } from './imageUtils';

/**
 * Transforms image URLs to handle different path formats
 * @param {string} url - The URL to transform
 * @param {string} postId - The post ID/slug
 * @returns {string} - The transformed URL
 */
export const transformImageUrl = (url, postId) => {
  if (!url) return url;

  // Handle different path formats
  if (url.startsWith('http')) {
    // External URL - use as is
    return url;
  } else if (url.startsWith('/images/blog/')) {
    // Old path format - convert to new format
    const filename = url.split('/').pop();
    return `/blog/${postId}/${filename}`;
  } else if (url.startsWith('/blog/')) {
    // Path starts with /blog/ but might need the slug
    if (url.includes(`/${postId}/`)) {
      // Already has the slug in the path
      return url;
    } else {
      // Add the slug to the path
      const pathParts = url.split('/');
      pathParts.splice(2, 0, postId);
      return pathParts.join('/');
    }
  } else if (!url.startsWith('/')) {
    // Relative path - assume it's in the article's folder
    return `/blog/${postId}/${url}`;
  }
  return url;
};

/**
 * Extracts metadata from markdown content
 * @param {string} markdown - The markdown content
 * @param {string} slug - The post slug
 * @returns {Object} - The extracted metadata
 */
export const extractMetadata = (markdown) => {
  const titleMatch = markdown.match(/^# (.*)/m);
  const authorMatch = markdown.match(/\*\*Author: (.*)\*\*/m);
  const dateMatch = markdown.match(/\*Published: (.*)\*/m);
  const authorImageMatch = markdown.match(/!\[author-image\]\((.*)\)/m);
  const bannerImageMatch = markdown.match(/!\[banner-image\]\((.*)\)/m);
  const linkPreviewImageMatch = markdown.match(/!\[link-preview-image\]\((.*)\)/m);

  return {
    title: titleMatch ? titleMatch[1] : 'Untitled Post',
    author: authorMatch ? authorMatch[1] : 'Unknown Author',
    date: dateMatch ? dateMatch[1] : 'Unknown Date',
    authorImagePath: authorImageMatch ? authorImageMatch[1] : null,
    bannerImagePath: bannerImageMatch ? bannerImageMatch[1] : null,
    linkPreviewImagePath: linkPreviewImageMatch ? linkPreviewImageMatch[1] : null,
  };
};

/**
 * Processes author image path
 * @param {string} path - The raw path from markdown
 * @param {string} slug - The post slug
 * @returns {string} - The processed path
 */
export const processAuthorImagePath = (path) => {
  // Default author image
  if (!path) return '/blog/carl-gustaf-ydstrom.jpeg';

  if (path.startsWith('http')) {
    return path;
  } else if (path.includes('/images/blog/')) {
    const filename = path.split('/').pop();
    return `/blog/${filename}`;
  } else {
    return path.startsWith('/blog/') ? path : `/blog/${path.replace(/^\//, '')}`;
  }
};

/**
 * Processes images for a blog post (dimensions and square artwork)
 * @param {string} imageUrl - The primary image URL
 * @param {string} fallbackUrl - The fallback image URL
 * @returns {Promise<Object>} - Object with dimensions and square artwork
 */
export const processPostImages = async (imageUrl, fallbackUrl) => {
  try {
    // Use primary image first, fall back to secondary, then default logo
    const url = imageUrl || fallbackUrl || '/logo512.png';
    const absoluteUrl = ensureAbsoluteUrl(url);

    // Get image dimensions for meta tags
    const getImageDimensions = () => {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => {
          // Ensure we have reasonable dimensions for social media previews
          // Most platforms prefer images at least 1200x630 pixels
          const width = img.width < 1200 ? 1200 : img.width;
          const height = img.height < 630 ? 630 : img.height;

          resolve({
            width: width,
            height: height,
            originalWidth: img.width,
            originalHeight: img.height
          });
        };
        img.onerror = () => reject(new Error('Failed to load image'));
        img.src = absoluteUrl;
      });
    };

    // Only create square artwork for the audio player
    // We don't need to create base64 social media artwork since we'll use direct URLs for og:image
    const [dimensions, squareImageDataUrl] = await Promise.allSettled([
      getImageDimensions(),
      cropImageToSquare(absoluteUrl, 1200) // Square image for audio player
    ]);

    return {
      dimensions: dimensions.status === 'fulfilled' ? dimensions.value : null,
      squareArtwork: squareImageDataUrl.status === 'fulfilled' ? squareImageDataUrl.value : null,
      // Don't create base64 social media artwork - we'll use direct URLs for og:image
      socialMediaArtwork: null
    };
  } catch (error) {
    console.error('Error processing images:', error);
    return { dimensions: null, squareArtwork: null, socialMediaArtwork: null };
  }
};

/**
 * Removes metadata from markdown content
 * @param {string} content - The original markdown content
 * @returns {string} - Content without metadata
 */
export const removeMetadataFromContent = (content) => {
  return content
    .replace(/^# .*$/m, '') // Remove title
    .replace(/\*Published: .*\*/m, '') // Remove date
    .replace(/\*\*Author: .*\*\*/m, '') // Remove author
    .replace(/!\[author-image\]\(.*\)/m, '') // Remove author image
    .replace(/!\[banner-image\]\(.*\)/m, '') // Remove banner image
    .replace(/!\[link-preview-image\]\(.*\)/m, '') // Remove link preview image
    .trim();
};

/**
 * Generates a description from markdown content
 * @param {string} content - The markdown content
 * @param {number} maxLength - Maximum length of the description
 * @returns {string} - The generated description
 */
export const generateDescription = (content, maxLength = 160) => {
  // First, remove specific markdown artifacts and special markers
  let processedContent = content
    .replace(/\[dropcap\]/g, '') // Remove dropcap markers
    .replace(/^## Introduction.*$/m, '') // Remove Introduction headings
    .replace(/^#+\s+/gm, '') // Remove all headings
    .replace(/\*\*/g, '') // Remove bold
    .replace(/\*/g, '') // Remove italic
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Replace links with just their text
    .replace(/!\[[^\]]*\]\([^)]+\)/g, '') // Remove images
    .replace(/\n+/g, ' ') // Replace newlines with spaces
    .replace(/\s{2,}/g, ' ') // Replace multiple spaces with a single space
    .trim();

  // Remove any remaining markdown artifacts or special formatting
  processedContent = processedContent
    .replace(/\[.*?\]/g, '') // Remove any remaining markdown-style tags
    .replace(/&quot;/g, '"') // Replace HTML entities
    .trim();

  return processedContent.length > maxLength
    ? processedContent.substring(0, maxLength - 3) + '...'
    : processedContent;
};

/**
 * Gets the preview image URL for meta tags
 * @param {Object} postMeta - The post metadata
 * @returns {string} - The absolute URL for the preview image
 */
export const getPreviewImageUrl = (postMeta) => {
  // First choice: link preview image if available (direct URL, not base64)
  if (postMeta.linkPreviewImage) {
    // Make sure the URL is absolute and properly formatted
    const absoluteUrl = ensureAbsoluteUrl(postMeta.linkPreviewImage);

    // Ensure the URL doesn't have any query parameters that might break previews
    const cleanUrl = absoluteUrl.split('?')[0];

    // Skip base64 data URLs
    if (!cleanUrl.startsWith('data:')) {
      return cleanUrl;
    }
  }

  // Second choice: banner image (direct URL, not base64)
  if (postMeta.bannerImage) {
    // Make sure the URL is absolute and properly formatted
    const absoluteUrl = ensureAbsoluteUrl(postMeta.bannerImage);

    // Ensure the URL doesn't have any query parameters that might break previews
    const cleanUrl = absoluteUrl.split('?')[0];

    // Skip base64 data URLs
    if (!cleanUrl.startsWith('data:')) {
      return cleanUrl;
    }
  }

  // Third choice: author image (direct URL, not base64)
  if (postMeta.authorImage) {
    const absoluteUrl = ensureAbsoluteUrl(postMeta.authorImage);
    if (!absoluteUrl.startsWith('data:')) {
      return absoluteUrl;
    }
  }

  // Fourth choice: social media optimized artwork (only if it's not a base64 data URL)
  if (postMeta.socialMediaArtwork && !postMeta.socialMediaArtwork.startsWith('data:')) {
    return postMeta.socialMediaArtwork;
  }

  // Fifth choice: square artwork (only if it's not a base64 data URL)
  if (postMeta.squareArtwork && !postMeta.squareArtwork.startsWith('data:')) {
    return postMeta.squareArtwork;
  }

  // Fallback: logo - ensure it's a larger version if available
  return ensureAbsoluteUrl('/logo512.png');
};
