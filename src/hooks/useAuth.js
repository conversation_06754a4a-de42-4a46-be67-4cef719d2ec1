import { useEffect, useReducer } from 'react';
import { Auth } from 'aws-amplify';

const useAuth = () => {
  const [state, dispatch] = useReducer(
    (state, action) => {
      switch (action.type) {
        case 'LOADING':
          return { ...state, isLoading: true };
        case 'SUCCESS':
          return { 
            ...state, 
            tokens: action.payload, 
            isLoading: false, 
            isLoggedIn: true, 
            error: null 
          };
        case 'ERROR':
          return { 
            ...state, 
            isLoading: false, 
            isLoggedIn: false, 
            error: action.payload 
          };
        default:
          return state;
      }
    },
    { tokens: null, isLoading: true, isLoggedIn: false, error: null }
  );

  useEffect(() => {
    let isMounted = true;
    
    const fetchTokens = async () => {
      try {
        const session = await Auth.currentSession();
        if (!session || !isMounted) return;
        
        dispatch({
          type: 'SUCCESS',
          payload: {
            accessToken: session.getAccessToken().getJwtToken(),
            idToken: session.getIdToken().getJwtToken()
          }
        });
      } catch (err) {
        if (!isMounted) return;
        
        if (process.env.NODE_ENV !== 'production') {
          console.error('Auth error:', err);
        }
        
        dispatch({ type: 'ERROR', payload: err });
      }
    };

    dispatch({ type: 'LOADING' });
    fetchTokens();

    return () => { isMounted = false; };
  }, []);

  return state;
};

export default useAuth;