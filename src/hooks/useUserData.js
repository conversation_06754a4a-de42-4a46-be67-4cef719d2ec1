import { useReducer, useEffect } from 'react';
import { produce } from 'immer';
import { fetchUserData, makeApiRequestWithCancellation } from '../utils/apiRequests';

const DEFAULT_ROLE = 'Operator';

const useUserData = (username, tokens, isLoggedIn) => {
  const [state, dispatch] = useReducer(
    produce((draft, action) => {
      switch (action.type) {
        case 'LOADING':
          draft.loading = true;
          return;
        case 'SUCCESS':
          draft.userData = action.payload.userData;
          draft.currentUserData = action.payload.currentUserData;
          draft.cognitoUsername = action.payload.cognitoUsername;
          draft.loading = false;
          draft.error = null;
          return;
        case 'ERROR':
          draft.loading = false;
          draft.error = action.payload.error;
          draft.userData = action.payload.userData;
          draft.cognitoUsername = action.payload.cognitoUsername;
          return;
        case 'RESET':
          draft.loading = false;
          draft.userData = null; 
          draft.currentUserData = null;
          draft.error = null;
          draft.cognitoUsername = null;
          return;
      }
    }),
    { userData: null, currentUserData: null, loading: true, error: null, cognitoUsername: null }
  );

  useEffect(() => {
    if (!username || !tokens || !isLoggedIn) {
      if (!username || !tokens) {
        dispatch({ type: 'LOADING' });
      }
      return;
    }
  
    let isMounted = true;
    let requestCanceller = null;
    
    const loadUserData = async () => {
      dispatch({ type: 'LOADING' });
      
      try {
        const userRequest = makeApiRequestWithCancellation('get', `/users/${username}`, tokens);
        requestCanceller = userRequest.cancel;
        
        const { userData: result, currentUser: userResponse } = await fetchUserData(username, tokens);

        if (!isMounted) return;

        let userEmail = username;
        let cognitoId = null;
        
        if (result.sub) {
          cognitoId = result.sub;
        }

        if (result.email) {
          userEmail = result.email;
        } else if (result.attributes?.email) {
          userEmail = result.attributes.email;
        } else if (result.UserAttributes) {
          const emailAttr = result.UserAttributes.find(attr => attr.Name === 'email');
          if (emailAttr) {
            userEmail = emailAttr.Value;
          }
        } else if (!userEmail && username.includes('@')) {
          userEmail = username;
        }

        if (!cognitoId && userEmail) {
          cognitoId = userEmail;
        }

        const processedData = {
          ...result,
          email: userEmail || '',
          nickname: result.nickname || '',
          role: result.role || DEFAULT_ROLE,
          isAccountOwner: result.isAccountOwner || false,
          picture: result.picture || '',
          sub: cognitoId || result.sub
        };

        dispatch({
          type: 'SUCCESS',
          payload: {
            userData: processedData,
            currentUserData: userResponse,
            cognitoUsername: cognitoId
          }
        });
      } catch (err) {
        if (!isMounted) return;
        
        if (process.env.NODE_ENV !== 'production') {
          console.error('Error fetching user data:', err);
        }

        let fallbackData = null;
        let fallbackUsername = null;
        
        if (username.includes('@')) {
          fallbackData = {
            email: username,
            nickname: '',
            role: DEFAULT_ROLE,
            isAccountOwner: false
          };
          fallbackUsername = username;
        }

        dispatch({
          type: 'ERROR',
          payload: {
            error: err,
            userData: fallbackData,
            cognitoUsername: fallbackUsername
          }
        });
      }
    };

    loadUserData();

    return () => { 
      isMounted = false;
      if (requestCanceller) {
        requestCanceller();
      }
    };
  }, [username, tokens, isLoggedIn]);

  return state;
};

export default useUserData;