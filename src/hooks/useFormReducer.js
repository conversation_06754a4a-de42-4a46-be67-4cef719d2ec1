import { produce } from 'immer';
import { useCallback, useReducer, useEffect } from 'react';
import { debounce } from 'lodash';
import { storeFormStateInLocalStorage, loadFormStateFromLocalStorage } from '../utils/localStorage';

export const DEFAULT_ROLE = 'Operator';
export const ROLE_DESCRIPTIONS = {
  Admin: 'Admins manage the account, other users, and tags.',
  Manager: 'Managers have full access and control.',
  Operator: 'Operators have access for day-to-day tasks.',
  default: 'Select a role to see description.'
};

export const initialFormState = {
  displayName: '',
  email: '',
  role: DEFAULT_ROLE,
  isAccountOwner: false,
  avatarUrl: '',
  avatarFile: null,
  avatarBase64: null,
  originalData: { nickname: '', role: DEFAULT_ROLE, picture: '' },
  cognitoId: null,
  hasUnsavedChanges: false
};

const normalizeAvatarUrl = (url) => {
  if (!url) return '';
  const cloudFrontDomain = import.meta.env.VITE_AWS_USER_UPLOAD_CLOUDFRONT;
  if (cloudFrontDomain && !url.includes(cloudFrontDomain)) {
    if (url.includes('amazonaws.com')) {
      const pathMatch = url.match(/amazonaws\.com\/([^?]+)/);
      if (pathMatch && pathMatch[1]) {
        return `https://${cloudFrontDomain}/${pathMatch[1]}`;
      }
    }
  }
  
  return url;
};

export const formReducer = produce((draft, action) => {
  switch (action.type) {
    case 'SET_FIELD':
      draft[action.field] = action.value;
      draft.hasUnsavedChanges = true;
      break;
      
    case 'SET_AVATAR_FILE':
      draft.avatarFile = action.file;
      draft.hasUnsavedChanges = true;
      break;
      
    case 'SET_AVATAR_BASE64':
      draft.avatarBase64 = action.data;
      break;
      
    case 'SET_AVATAR_URL':
      draft.avatarUrl = normalizeAvatarUrl(action.url);
      break;
      
    case 'SET_USER_DATA':
      draft.displayName = action.userData.nickname || '';
      draft.email = action.userData.email || '';
      draft.role = action.userData.role || DEFAULT_ROLE;
      draft.isAccountOwner = action.userData.isAccountOwner || false;
      draft.avatarUrl = normalizeAvatarUrl(action.userData.picture || '');
      draft.cognitoId = action.userData.sub || null;
      draft.originalData = {
        nickname: action.userData.nickname || '',
        role: action.userData.role || DEFAULT_ROLE,
        picture: normalizeAvatarUrl(action.userData.picture || '')
      };
      draft.hasUnsavedChanges = false;
      if (action.storedState) {
        draft.displayName = action.storedState.displayName;
        draft.role = action.storedState.role;
        draft.hasUnsavedChanges = true;
      }
      break;
      
    case 'RESET_UNSAVED_CHANGES':
      draft.hasUnsavedChanges = false;
      break;
      
    case 'UPDATE_FROM_SAVE':
      // Update original data with current form values
      draft.originalData.nickname = draft.displayName;
      draft.originalData.role = draft.role;
      
      // Normalize the picture URL
      const normalizedPicture = normalizeAvatarUrl(action.picture || draft.originalData.picture);
      
      // Always update the original data with the server's URL
      draft.originalData.picture = normalizedPicture;
      
      // Only clear the avatarFile and avatarBase64 if explicitly requested
      // or if keepBase64 is not set to true
      if (action.clearBase64 || !action.keepBase64) {
        draft.avatarFile = null;
        draft.avatarBase64 = null;
      }
      
      // Update the displayed URL based on whether we're keeping the base64 preview
      // If keepBase64 is true and we have a base64 preview, keep using that
      // Otherwise use the normalized URL from the server
      draft.avatarUrl = action.keepBase64 && draft.avatarBase64 
        ? draft.avatarUrl  // Keep current preview URL
        : normalizedPicture;  // Use the server's URL
      
      // Clear the unsaved changes flag
      draft.hasUnsavedChanges = false;
      break;
      
    case 'CHECK_UNSAVED_CHANGES':
      draft.hasUnsavedChanges = 
        draft.displayName !== draft.originalData.nickname ||
        draft.role !== draft.originalData.role ||
        draft.avatarFile !== null;
      break;
  }
});

const useFormReducer = (userData, userIdentifier) => {
  const [formState, dispatch] = useReducer(formReducer, initialFormState);

  const debouncedCheckChanges = useCallback(
    debounce(() => {
      dispatch({ type: 'CHECK_UNSAVED_CHANGES' });
    }, 300),
    []
  );

  const handleDisplayNameChange = useCallback((e) => {
    dispatch({ 
      type: 'SET_FIELD', 
      field: 'displayName', 
      value: e.target.value 
    });
    debouncedCheckChanges();
  }, [debouncedCheckChanges]);

  const handleRoleChange = useCallback((e) => {
    dispatch({ 
      type: 'SET_FIELD', 
      field: 'role', 
      value: e.target.value 
    });
    debouncedCheckChanges();
  }, [debouncedCheckChanges]);

  useEffect(() => {
    if (userData) {
      const storedState = userIdentifier ? loadFormStateFromLocalStorage(userIdentifier) : null;
      
      dispatch({
        type: 'SET_USER_DATA',
        userData,
        storedState
      });
    }
  }, [userData, userIdentifier]);

  useEffect(() => {
    const saveTimer = setTimeout(() => {
      if (formState.hasUnsavedChanges && userIdentifier) {
        storeFormStateInLocalStorage(formState, userIdentifier);
      }
    }, 500);
    
    return () => clearTimeout(saveTimer);
  }, [formState.displayName, formState.role, formState.hasUnsavedChanges, userIdentifier]);

  const getRoleDescription = ROLE_DESCRIPTIONS[formState.role] || ROLE_DESCRIPTIONS.default;

  return {
    formState,
    dispatch,
    handleDisplayNameChange,
    handleRoleChange,
    getRoleDescription,
    debouncedCheckChanges
  };
};

export default useFormReducer;