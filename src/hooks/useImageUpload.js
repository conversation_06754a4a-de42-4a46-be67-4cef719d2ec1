import { useCallback, useEffect, useState } from 'react';
import ImageProcessor, { processImageForUpload, MAX_IMAGE_SIZE } from '../utils/imageProcessing';

const useImageUpload = (formState, dispatch, setUiState, isMountedRef, handleError, fileInputRef) => {
  // Track if the image is loading (especially for CloudFront URLs)
  const [isImageLoading, setIsImageLoading] = useState(false);
  // Track image error state separately from loading state
  const [hasImageError, setHasImageError] = useState(false);

  const handleAvatarClick = useCallback(() => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, [fileInputRef]);

  const handleAvatarChange = useCallback(async (e) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (!file.type.match('image.*')) {
      setUiState(prev => ({ 
        ...prev, 
        message: 'Error: Please select an image file.' 
      }));
      return;
    }
    
    if (file.size > MAX_IMAGE_SIZE) {
      setUiState(prev => ({ 
        ...prev, 
        message: 'Error: Image file size must be less than 5MB.' 
      }));
      return;
    }

    // Reset error state when selecting new image
    setHasImageError(false);
    // Set loading state while processing the new image
    setIsImageLoading(true);
    dispatch({ type: 'SET_AVATAR_FILE', file });
  }, [dispatch, setUiState, fileInputRef]);

  // Track when CloudFront URLs are set and mark as loading
  useEffect(() => {
    if (!formState.avatarUrl) return;
    
    const cloudFrontDomain = import.meta.env.VITE_AWS_USER_UPLOAD_CLOUDFRONT;
    
    // Set loading for any external URL (not data URLs or object URLs)
    const isExternalUrl = !formState.avatarUrl.startsWith('data:') && 
                          !formState.avatarUrl.startsWith('blob:');
    const isCloudFrontUrl = cloudFrontDomain && formState.avatarUrl.includes(cloudFrontDomain);
    
    // Always set loading state when URL changes to an external source
    if ((isCloudFrontUrl || isExternalUrl) && !formState.avatarBase64) {
      setIsImageLoading(true);
      // Reset error state when URL changes
      setHasImageError(false);
    }
  }, [formState.avatarUrl, formState.avatarBase64]);

  useEffect(() => {
    let objectUrl = null;
    let cleanupFn = () => {};
    
    if (formState.avatarFile) {
      objectUrl = URL.createObjectURL(formState.avatarFile);
      dispatch({ type: 'SET_AVATAR_URL', url: objectUrl });
      
      const processImage = async () => {
        if (ImageProcessor.isSupported()) {
          try {
            const fastResult = await processImageForUpload(formState.avatarFile, 0.5);
            
            if (isMountedRef.current && fastResult) {
              dispatch({ type: 'SET_AVATAR_BASE64', data: fastResult });
              
              const betterResult = await processImageForUpload(formState.avatarFile, 0.8);
              if (isMountedRef.current && betterResult) {
                dispatch({ type: 'SET_AVATAR_BASE64', data: betterResult });
              }
            }
          } catch (error) {
            if (isMountedRef.current) {
              handleError(error, 'Error processing image');
            }
          } finally {
            if (isMountedRef.current) {
              setIsImageLoading(false);
            }
          }
        } else {
          if (typeof FileReader !== 'undefined') {
            const reader = new FileReader();
            reader.onload = () => {
              if (isMountedRef.current) {
                dispatch({ type: 'SET_AVATAR_BASE64', data: reader.result });
                setIsImageLoading(false);
              }
            };
            reader.readAsDataURL(formState.avatarFile);
            cleanupFn = () => reader.abort();
          }
        }
      };
      
      processImage();
    }
    
    return () => {
      cleanupFn();
      if (objectUrl) {
        URL.revokeObjectURL(objectUrl);
      }
    };
  }, [formState.avatarFile, handleError, dispatch, isMountedRef]);

  // Handle image load/error events
  const handleImageLoad = useCallback(() => {
    // Only clear loading state on successful load
    setIsImageLoading(false);
    setHasImageError(false);
  }, []);

  const handleImageError = useCallback((e) => {
    console.error('Image failed to load:', e.target.src);
    
    // Don't immediately set src - leave the broken image during loading state
    // Just mark as having an error
    setHasImageError(true);
    
    // Ensure we don't get called again for this image
    e.target.onerror = null;
    
    // Log CloudFront failures for debugging
    const cloudFrontDomain = import.meta.env.VITE_AWS_USER_UPLOAD_CLOUDFRONT;
    if (cloudFrontDomain && e.target.src.includes(cloudFrontDomain)) {
      console.warn('CloudFront CDN image loading failed');
    }

    // Keep loading state active for a longer delay to ensure spinner is visible
    setTimeout(() => {
      if (isMountedRef.current) {
        setIsImageLoading(false);
        // Only replace with placeholder after delay to allow spinner to show
        e.target.src = '/user_placeholder.svg';
      }
    }, 2000); // 2 second delay ensures spinner is visible
  }, [isMountedRef]);

  return { 
    handleAvatarClick, 
    handleAvatarChange,
    handleImageLoad,
    handleImageError,
    isImageLoading,
    hasImageError
  };
};

export default useImageUpload;