import { useState, useCallback } from 'react';
import { clearFormStateFromLocalStorage } from '../utils/localStorage';

export const useConfirmationDialog = (onConfirmAction, options = {}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [dialogData, setDialogData] = useState(null);
  
  const open = useCallback((data = null) => {
    setDialogData(data);
    setIsOpen(true);
  }, []);
  
  const close = useCallback(() => {
    setIsOpen(false);
  }, []);
  
  const handleConfirm = useCallback(() => {
    if (onConfirmAction) {
      onConfirmAction(dialogData);
    }
    close();
  }, [onConfirmAction, dialogData, close]);
  
  const handleCancel = useCallback(() => {
    if (options.onCancel) {
      options.onCancel();
    }
    close();
  }, [options, close]);
  
  return {
    isOpen,
    open,
    close,
    handleConfirm,
    handleCancel,
    dialogData
  };
};

export const useNavigationConfirmation = (navigate, navigationTarget, hasUnsavedChanges, userId) => {
  const onConfirmNavigation = useCallback(() => {
    if (userId) {
      clearFormStateFromLocalStorage(userId);
    }
    navigate(navigationTarget);
  }, [navigate, navigationTarget, userId]);

  const { isOpen, open, handleConfirm, handleCancel } = useConfirmationDialog(onConfirmNavigation);

  const handleNavigationAttempt = useCallback((e) => {
    if (hasUnsavedChanges) {
      e?.preventDefault();
      open();
    }
  }, [hasUnsavedChanges, open]);

  return {
    showNavigationConfirmation: isOpen,
    handleNavigationAttempt,
    confirmNavigation: handleConfirm,
    cancelNavigation: handleCancel
  };
};

export const useDeleteConfirmation = (options) => {
  const { 
    tokens, 
    isLoggedIn, 
    isAccountOwner, 
    userId, 
    onDelete, 
    setMessage 
  } = options;

  const [isDeleting, setIsDeleting] = useState(false);

  const onConfirmDelete = useCallback(async () => {
    setIsDeleting(true);
    try {
      await onDelete();
    } finally {
      setIsDeleting(false);
    }
  }, [onDelete]);

  const { isOpen, open, handleConfirm, handleCancel } = useConfirmationDialog(onConfirmDelete);

  const handleDelete = useCallback(() => {
    if (!tokens || !isLoggedIn) {
      setMessage(prev => ({ 
        ...prev, 
        message: 'Authentication required. Please log in again.' 
      }));
      return;
    }

    if (isAccountOwner) {
      setMessage(prev => ({ 
        ...prev, 
        message: 'Cannot delete account owner' 
      }));
      return;
    }

    open();
  }, [tokens, isLoggedIn, isAccountOwner, setMessage, open]);

  return {
    showDeleteConfirmation: isOpen,
    isDeleting,
    handleDelete,
    confirmDelete: handleConfirm,
    cancelDelete: handleCancel
  };
};

export default useConfirmationDialog;