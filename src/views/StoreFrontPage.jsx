import React, { useState, useEffect, useCallback } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { Auth, API } from 'aws-amplify';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import { processImageForUpload } from '../utils/imageProcessing';
import ConfirmationDialog from '../components/ConfirmationDialog'; // Add this import

import '../styles/TagList.css';
import '../styles/TagPage.css';
import '../styles/QRPreviewPanel.css';
import '../styles/StatusIndicators.css';

// Import components
import { StoreFrontSettings } from '../components/storefront/StoreFrontSettings';
import { ProductImagePanel } from '../components/storefront/ProductImagePanel';
import { ActionButtonPanel } from '../components/storefront/ActionButtonPanel';
import { AboutUsPanel } from '../components/storefront/AboutUsPanel';
import StatusManagement from '../components/storefront/StatusManagement';
import { CustomNotes } from '../components/storefront/CustomNotes';
import ManageAssetDocuments from '../components/storefront/ManageAssetDocuments';

export function StoreFrontPage() {
  const { assetId } = useParams();
  const navigate = useNavigate();
  
  // Core state
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showValidation, setShowValidation] = useState(false); // Add validation state
  
  // Validation dialog state
  const [showValidationDialog, setShowValidationDialog] = useState(false);
  const [validationDialogMessage, setValidationDialogMessage] = useState('');
  
  // Asset data
  const [assetData, setAssetData] = useState({
    id: '',
    name: '',
    documents: [],
    imageUrl: '',
    publicAction: '',
    privateAction: '',
  });

  // StoreFront form data
  const [formData, setFormData] = useState({
    storefrontEnabled: false,
    publicStorefrontEnabled: false,
    privateStorefrontEnabled: false,
    imageEnabled: true,
    aboutUsEnabled: false,
    documentsEnabled: true,
    notesEnabled: false,
    notes: '',
    customStatuses: [] // Added for status management
  });

  // Panel expansion state
  const [openPanels, setOpenPanels] = useState({
    image: true,
    actionButton: true,
    aboutUs: true,
    documents: true,
    status: true,
    notes: true
  });

  // Visibility state
  const [visibilityType, setVisibilityType] = useState('private');
  
  // Product page data
  const [buttonConfig, setButtonConfig] = useState({
    buttonText: 'Request Service',
    buttonEmail: '',
    buttonSubject: '',
    buttonMessage: '',
    buttonEnabled: false,
    showContactForm: true
  });
  const [aboutUsData, setAboutUsData] = useState({
    companyName: '',
    website: '',
    phoneNumber: '',
    contactEmail: ''
  });

  // Image handling
  const [imageFileType, setImageFileType] = useState('');
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreviewUrl, setImagePreviewUrl] = useState('');
  const [imageBase64, setImageBase64] = useState(null);
  const [isImageUploading, setIsImageUploading] = useState(false);

  // Authentication handling
  const getAuthHeaders = useCallback(async () => {
    const session = await Auth.currentSession();
    return {
      Authorization: `Bearer ${session.getAccessToken().getJwtToken()}`,
      'id-Token': session.getIdToken().getJwtToken(),
    };
  }, []);

  // Handler for document updates
  const handleDocumentUpdated = useCallback((updatedDocuments) => {
    setAssetData(prev => ({
      ...prev,
      documents: updatedDocuments
    }));
  }, []);
  
  // Utility functions for visibility
  const getVisibilityType = useCallback((publicEnabled, privateEnabled) => {
    if (publicEnabled && privateEnabled) return 'everyone';
    if (publicEnabled) return 'public';
    return 'private';
  }, []);

  const getVisibilitySettings = useCallback((type) => {
    switch (type) {
      case 'public':
        return { publicStorefrontEnabled: true, privateStorefrontEnabled: false };
      case 'everyone':
        return { publicStorefrontEnabled: true, privateStorefrontEnabled: true };
      case 'private':
      default:
        return { publicStorefrontEnabled: false, privateStorefrontEnabled: true };
    }
  }, []);

  // Helper function to determine if a panel should be expanded
  const shouldExpandPanel = useCallback((panel, enabled) => {
    return formData.storefrontEnabled && openPanels[panel] && enabled;
  }, [formData.storefrontEnabled, openPanels]);

  // Helper function to get proper redirect values based on storefront settings
  const getRedirectValues = useCallback(() => {
    const productPageUrl = `https://thinkertags.com/assets/${assetId}/storefront/view`;
    const redirects = {
      publicAction: assetData.publicAction,
      privateAction: assetData.privateAction
    };
    
    // If storefront is enabled, override redirects based on visibility settings
    if (formData.storefrontEnabled) {
      if (formData.publicStorefrontEnabled) {
        redirects.publicAction = productPageUrl;
      }
      
      if (formData.privateStorefrontEnabled) {
        redirects.privateAction = productPageUrl;
      }
    }
    
    return redirects;
  }, [assetId, formData.storefrontEnabled, formData.publicStorefrontEnabled, 
      formData.privateStorefrontEnabled, assetData.publicAction, assetData.privateAction]);

  // Updated validation function - ONLY validate email
  const validateFormData = useCallback(() => {
    const errors = [];
    
    // Check if service button is enabled but missing required fields
    if (buttonConfig.buttonEnabled && formData.storefrontEnabled) {
      // REMOVED: buttonText validation
      
      if (!buttonConfig.buttonEmail?.trim()) {
        errors.push({ field: 'buttonEmail', message: 'Recipient Email is required' });
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(buttonConfig.buttonEmail)) {
        errors.push({ field: 'buttonEmail', message: 'Please enter a valid email address' });
      }
    }
    
    return errors;
  }, [buttonConfig, formData.storefrontEnabled]);

  // Effect for handling image selection
  useEffect(() => {
    if (selectedImage) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreviewUrl(reader.result);
      };
      reader.readAsDataURL(selectedImage);
      
      const processImage = async () => {
        try {
          const base64Data = await processImageForUpload(selectedImage, 0.8);
          setImageBase64(base64Data);
        } catch (error) {
          console.error('Error processing image:', error);
        }
      };
      
      processImage();
      setHasUnsavedChanges(true);
    }
  }, [selectedImage]);

  // Fetch asset data on mount
  useEffect(() => {
    let isMounted = true;

    async function fetchAsset() {
      if (!assetId) {
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      try {
        const headers = await getAuthHeaders();
        const result = await API.get('api', `/assets/${assetId}`, { headers });
        
        if (!isMounted || !result) return;
        
        // Set asset data
        setAssetData({
          id: result.id || '',
          name: result.name || '',
          documents: result.documents || [],
          imageUrl: result.imageUrl || '',
          publicAction: result.publicAction || '',
          privateAction: result.privateAction || '',
        });
        
        // Set button config
        if (result.buttonConfig) {
          setButtonConfig(result.buttonConfig);
        } else {
          setButtonConfig({
            buttonText: 'Request Service',
            buttonEmail: '',
            buttonSubject: `Service Request for ${result.name}`,
            buttonMessage: `A service request has been submitted for ${result.name}.`,
            buttonEnabled: false,
            showContactForm: true
          });
        }
        
        // Set about us data
        if (result.aboutUs) {
          setAboutUsData(result.aboutUs);
        } else {
          setAboutUsData({
            companyName: result.companyName || '',
            website: result.website || '',
            phoneNumber: result.phoneNumber || '',
            contactEmail: result.contactEmail || ''
          });
        }
        
        // Set visibility settings
        const storefrontEnabled = result.storefrontEnabled || false;
        const publicStorefrontEnabled = result.publicStorefrontEnabled || false;
        const privateStorefrontEnabled = result.privateStorefrontEnabled || false;
        
        const initialVisibilityType = getVisibilityType(
          publicStorefrontEnabled,
          privateStorefrontEnabled
        );
        
        setVisibilityType(initialVisibilityType);
        
        // Set form data
        const newFormData = {
          storefrontEnabled,
          publicStorefrontEnabled,
          privateStorefrontEnabled,
          imageEnabled: result.imageEnabled !== false,
          aboutUsEnabled: result.aboutUsEnabled === true,
          documentsEnabled: result.documentsEnabled !== false,
          notesEnabled: result.notesEnabled || false,
          notes: result.notes || '',
          customStatuses: result.customStatuses || [] // Added for status management
        };
        
        setFormData(newFormData);
        
        // Initialize panel expansion state based on section enabled state and storefront enabled
        setOpenPanels({
          image: storefrontEnabled && (result.imageEnabled !== false),
          actionButton: storefrontEnabled && (result.buttonConfig?.buttonEnabled === true),
          aboutUs: storefrontEnabled && (result.aboutUsEnabled === true),
          documents: storefrontEnabled && (result.documentsEnabled !== false),
          status: storefrontEnabled,
          notes: storefrontEnabled && (result.notesEnabled === true)
        });
      } catch (error) {
        console.error('Error fetching asset:', error);
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    }

    fetchAsset();
    return () => {
      isMounted = false;
    };
  }, [assetId, getVisibilityType, getAuthHeaders]);

  const handleImageUpload = useCallback((file) => {
    setSelectedImage(file);
    
    // Extract the file extension from the original filename
    const fileExtension = file.name.split('.').pop().toLowerCase();
    setImageFileType(fileExtension);
  }, []);

  const handleButtonConfigChange = useCallback((field, value) => {
    setButtonConfig(prev => ({
      ...prev,
      [field]: value
    }));
    setHasUnsavedChanges(true);
  }, []);

  const handleAboutUsChange = useCallback((field, value) => {
    setAboutUsData(prev => ({
      ...prev,
      [field]: value
    }));
    setHasUnsavedChanges(true);
  }, []);

  // Handler for status changes
  const handleStatusChange = useCallback((field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setHasUnsavedChanges(true);
  }, []);

  // Handler for image visibility toggle
  const handleImageVisibilityChange = useCallback((isVisible) => {
    setFormData(prev => ({
      ...prev,
      imageEnabled: isVisible
    }));
    // Also update panel expansion state
    setOpenPanels(prev => ({
      ...prev,
      image: isVisible
    }));
    setHasUnsavedChanges(true);
  }, []);

  // Handler for aboutUs visibility toggle
  const handleAboutUsVisibilityChange = useCallback((isVisible) => {
    setFormData(prev => ({
      ...prev,
      aboutUsEnabled: isVisible
    }));
    // Also update panel expansion state
    setOpenPanels(prev => ({
      ...prev,
      aboutUs: isVisible
    }));
    setHasUnsavedChanges(true);
  }, []);

  // Handler for document visibility toggle
  const handleDocumentsVisibilityChange = useCallback((isVisible) => {
    setFormData(prev => ({
      ...prev,
      documentsEnabled: isVisible
    }));
    // Also update panel expansion state
    setOpenPanels(prev => ({
      ...prev,
      documents: isVisible
    }));
    setHasUnsavedChanges(true);
  }, []);

  // Handler for notes visibility toggle
  const handleNotesVisibilityChange = useCallback((isVisible) => {
    setFormData(prev => ({
      ...prev,
      notesEnabled: isVisible
    }));
    // Also update panel expansion state
    setOpenPanels(prev => ({
      ...prev,
      notes: isVisible
    }));
    setHasUnsavedChanges(true);
  }, []);

  // Handler for notes content change
  const handleNotesChange = useCallback((value) => {
    setFormData(prev => ({
      ...prev,
      notes: value
    }));
    setHasUnsavedChanges(true);
  }, []);

  // Handler for contact form toggle
  const handleContactFormToggle = useCallback((e) => {
    const isChecked = e.target.checked;
    setButtonConfig(prev => ({
      ...prev,
      showContactForm: isChecked
    }));
    setHasUnsavedChanges(true);
  }, []);

  // Handle button enabled toggle
  const handleButtonEnabledToggle = useCallback((isEnabled) => {
    setButtonConfig(prev => ({
      ...prev,
      buttonEnabled: isEnabled
    }));
    // Also update panel expansion state
    setOpenPanels(prev => ({
      ...prev,
      actionButton: isEnabled
    }));
    setHasUnsavedChanges(true);
  }, []);

  const handleVisibilityToggle = useCallback((e) => {
    const isEnabled = e.target.checked;
    
    if (isEnabled) {
      setFormData(prev => ({
        ...prev,
        storefrontEnabled: true,
        privateStorefrontEnabled: true,
        publicStorefrontEnabled: false 
      }));
      
      // Restore panel states based on which features are enabled
      setOpenPanels(prev => ({
        image: formData.imageEnabled,
        actionButton: buttonConfig.buttonEnabled,
        aboutUs: formData.aboutUsEnabled,
        documents: formData.documentsEnabled,
        status: true, // Status is always available when StoreFront is enabled
        notes: formData.notesEnabled
      }));
      
      setVisibilityType('private');
    } else {
      setFormData(prev => ({
        ...prev,
        storefrontEnabled: false,
      }));
      
      // When turning storefront off, collapse all panels
      setOpenPanels({
        image: false,
        actionButton: false,
        aboutUs: false,
        documents: false,
        status: false,
        notes: false
      });
    }
    
    setHasUnsavedChanges(true);
  }, [formData.imageEnabled, formData.aboutUsEnabled, formData.documentsEnabled, 
      formData.notesEnabled, buttonConfig.buttonEnabled]);

  const handleVisibilityTypeChange = useCallback((newVisibilityType, settings) => {
    setVisibilityType(newVisibilityType);
    setFormData(prev => ({
      ...prev,
      ...settings
    }));
    setHasUnsavedChanges(true);
  }, []);

  // Toggle panel expansion state
  const handleTogglePanel = useCallback((panel) => {
    if (!formData.storefrontEnabled) return;
    
    setOpenPanels(prev => ({
      ...prev,
      [panel]: !prev[panel]
    }));
  }, [formData.storefrontEnabled]);

  // Navigate to product page
  const goToProductPage = useCallback(() => {
    navigate(`/assets/${assetId}/storefront/view`);
  }, [assetId, navigate]);

  // Get footer button text - updated to match AssetPage behavior
  const getFooterButtonText = useCallback(() => {
    if (isSaving) return 'Saving...';
    if (!hasUnsavedChanges) return 'Saved';
    return 'Update';
  }, [isSaving, hasUnsavedChanges]);

  // Updated handleSave with ConfirmationDialog validation
  const handleSave = useCallback(async () => {
    if (!hasUnsavedChanges) return;
    
    setShowValidation(true); // Enable validation display
    
    // Validate form data
    const validationErrors = validateFormData();
    if (validationErrors.length > 0) {
      // Focus on the first error field (now only email possible)
      const firstError = validationErrors[0];
      const fieldElement = document.getElementById('button-email');
      
      if (fieldElement) {
        fieldElement.focus();
        fieldElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      
      // Show validation dialog instead of alert
      setValidationDialogMessage(firstError.message);
      setShowValidationDialog(true);
      return;
    }
    
    setIsSaving(true);
    try {
      const headers = await getAuthHeaders();
      const redirectValues = getRedirectValues();
      
      // Handle image upload if there is a new image
      if (imageBase64 && selectedImage) {
        setIsImageUploading(true);
        try {
          const fileExtension = imageFileType || 'jpg';
          
          // Strip the data URL prefix to get just the base64 data
          const rawBase64Data = imageBase64.split(',')[1];
          
          const imageResponse = await API.post('api', `/assets/${assetId}/image`, {
            headers,
            body: {
              base64Image: rawBase64Data, // only the raw base64 data
              fileName: `product-${Date.now()}.${fileExtension}`
            }
          });
          
          if (imageResponse && imageResponse.publicUrl) {
            const imageUrl = imageResponse.publicUrl;      
            
            // Update local state for preview
            setAssetData(prev => ({
              ...prev,
              imageUrl
            }));
            setImagePreviewUrl('');
            setSelectedImage(null);
            setImageBase64(null);
          }
        } catch (imageError) {
          console.error('Error uploading image:', imageError);
          throw new Error('Failed to upload image. Please try again.');
        } finally {
          setIsImageUploading(false);
        }
      }

      // Prepare data for API update
      const updateData = {
        // StoreFront settings
        storefrontEnabled: formData.storefrontEnabled,
        publicStorefrontEnabled: formData.publicStorefrontEnabled,
        privateStorefrontEnabled: formData.privateStorefrontEnabled,
        
        // Update redirects when saving storefront settings
        publicAction: redirectValues.publicAction,
        privateAction: redirectValues.privateAction,
        
        // Button config
        buttonConfig,
        
        // About us data
        aboutUs: aboutUsData,
        companyName: aboutUsData.companyName,
        website: aboutUsData.website,
        phoneNumber: aboutUsData.phoneNumber,
        contactEmail: aboutUsData.contactEmail,
        
        // Status management
        customStatuses: formData.customStatuses,
        
        // Notes data
        notes: formData.notes,
        notesEnabled: formData.notesEnabled,
        
        // Section visibility settings
        imageEnabled: formData.imageEnabled,
        aboutUsEnabled: formData.aboutUsEnabled,
        documentsEnabled: formData.documentsEnabled
      };

      await API.post('api', `/assets/${assetId}`, { 
        headers, 
        body: updateData 
      });

      // Update local asset data
      setAssetData(prev => ({
        ...prev,
        publicAction: redirectValues.publicAction,
        privateAction: redirectValues.privateAction,
      }));
      
      setHasUnsavedChanges(false);
      setShowValidation(false); // Hide validation after successful save
    } catch (error) {
      console.error('Error saving asset data:', error);
      alert('Failed to save changes. Please try again.');
    } finally {
      setIsSaving(false);
    }
  }, [assetId, formData, buttonConfig, aboutUsData, 
      hasUnsavedChanges, imageBase64, selectedImage, imageFileType, 
      getAuthHeaders, getRedirectValues, validateFormData]);

  // Render loading skeleton for inputs
  const renderSkeletonForInputs = () => (
    <div>
      <div style={{ marginBottom: '20px' }}>
        <Skeleton height={20} width={120} style={{ marginBottom: '8px' }} />
        <Skeleton height={36} />
      </div>
      
      <div style={{ marginBottom: '20px' }}>
        <Skeleton height={20} width={150} style={{ marginBottom: '8px' }} />
        <Skeleton height={36} />
      </div>
      
      <div style={{ marginBottom: '20px' }}>
        <Skeleton height={20} width={130} style={{ marginBottom: '8px' }} />
        <Skeleton height={80} />
      </div>
    </div>
  );

  return (
    <div className="settings-container">
      {/* Header */}
      <div className="settings-header-container">
        <Link to={`/assets/${assetId}`} className="back-arrow">
          <img src="/arrow_back.svg" alt="Back" className="backarrow" />
          {' '}Asset Settings
        </Link>
        <div
          onClick={formData.storefrontEnabled ? goToProductPage : undefined}
          role={formData.storefrontEnabled ? "button" : undefined}
          tabIndex={formData.storefrontEnabled ? 0 : undefined}
          onKeyPress={(e) => formData.storefrontEnabled && e.key === 'Enter' && goToProductPage()}
          style={{ 
            marginLeft: 'auto',
            cursor: formData.storefrontEnabled ? 'pointer' : 'default',
            opacity: formData.storefrontEnabled ? 1 : 0.5
          }}
        >
          <img 
            src="/eye.fill.svg" 
            alt={formData.storefrontEnabled ? "View Product Page" : "Product Page (Disabled)"}
            className="header-diamond-icon"
            style={{
              height: '24px',
              objectFit: 'contain'
            }}
          />
        </div>
      </div>

      {/* Main Panel */}
      <div className="settings-panel">

        {isLoading ? (
          <div className="settings-panel-body" style={{ marginTop: '10px' }}>
            {renderSkeletonForInputs()}
          </div>
        ) : (
          <div className="settings-panel-body">
            {/* StoreFront Settings */}
            <StoreFrontSettings
              storefrontEnabled={formData.storefrontEnabled}
              visibilityType={visibilityType}
              onVisibilityTypeChange={handleVisibilityTypeChange}
              onVisibilityToggle={handleVisibilityToggle}
              getVisibilitySettings={getVisibilitySettings}
            />

            {/* Product Image Panel */}
            <div className="storefront-section">
              <ProductImagePanel
                imageUrl={assetData.imageUrl}
                imagePreviewUrl={imagePreviewUrl}
                onImageUpload={handleImageUpload}
                isImageUploading={isImageUploading}
                isEnabled={formData.imageEnabled}
                toggleSectionEnabled={() => handleImageVisibilityChange(!formData.imageEnabled)}
                isStorefrontEnabled={formData.storefrontEnabled}
                shouldExpandPanel={shouldExpandPanel}
                onTogglePanel={() => handleTogglePanel('image')}
                optionalLabelStyle={{ 
                  fontSize: '12px', 
                  color: '#666', 
                  fontWeight: 'normal', 
                  marginLeft: '5px' 
                }}
              />
            </div>

            {/* Action Button Panel */}
            <div className="storefront-section">
              <ActionButtonPanel
                buttonConfig={buttonConfig}
                isEnabled={buttonConfig.buttonEnabled}
                toggleSectionEnabled={() => handleButtonEnabledToggle(!buttonConfig.buttonEnabled)}
                isStorefrontEnabled={formData.storefrontEnabled}
                shouldExpandPanel={shouldExpandPanel}
                onTogglePanel={() => handleTogglePanel('actionButton')}
                optionalLabelStyle={{ 
                  fontSize: '12px', 
                  color: '#666', 
                  fontWeight: 'normal', 
                  marginLeft: '5px' 
                }}
                onButtonConfigChange={handleButtonConfigChange}
                handleContactFormToggle={handleContactFormToggle}
                showValidation={showValidation} // Add this new prop
              />
            </div>

            {/* About Us Panel */}
            <div className="storefront-section">
              <AboutUsPanel
                aboutUsData={aboutUsData}
                isEnabled={formData.aboutUsEnabled}
                toggleSectionEnabled={() => handleAboutUsVisibilityChange(!formData.aboutUsEnabled)}
                isStorefrontEnabled={formData.storefrontEnabled}
                shouldExpandPanel={shouldExpandPanel}
                onTogglePanel={() => handleTogglePanel('aboutUs')}
                optionalLabelStyle={{ 
                  fontSize: '12px', 
                  color: '#666', 
                  fontWeight: 'normal', 
                  marginLeft: '5px' 
                }}
                onAboutUsChange={handleAboutUsChange}
              />
            </div>

            {/* Custom Notes */}
            <div className="storefront-section">
              <CustomNotes
                notes={formData.notes}
                notesEnabled={formData.notesEnabled}
                storefrontEnabled={formData.storefrontEnabled}
                onNotesChange={handleNotesChange}
                onNotesEnabledChange={handleNotesVisibilityChange}
                shouldExpandPanel={shouldExpandPanel}
                onTogglePanel={() => handleTogglePanel('notes')}
              />
            </div>

            {/* Status Management */}
            <div className="storefront-section">
              <StatusManagement 
                assetData={assetData}
                statuses={formData.customStatuses || []}
                onStatusChange={handleStatusChange}
                storefrontEnabled={formData.storefrontEnabled}
              />
            </div>

            {/* Document Management */}
            <div className="storefront-section">
              <ManageAssetDocuments
                assetId={assetId}
                documents={assetData.documents || []}
                getAuthHeaders={getAuthHeaders}
                onDocumentUpdated={handleDocumentUpdated}
                documentsEnabled={formData.documentsEnabled}
                onDocumentsEnabledChange={handleDocumentsVisibilityChange}
                storefrontEnabled={formData.storefrontEnabled}
                shouldExpandPanel={shouldExpandPanel}
                onTogglePanel={() => handleTogglePanel('documents')}
              />
            </div>
          </div>
        )}

        {/* Footer with Save button only */}
        <div className="settings-footer">
          <button
            className={`settings-footer-button ${isSaving ? 'saving' : ''}`}
            onClick={handleSave}
            disabled={!hasUnsavedChanges || isSaving}
          >
            {getFooterButtonText()}
          </button>
        </div>
      </div>

      {/* Validation Error Dialog */}
      <ConfirmationDialog
        isOpen={showValidationDialog}
        title="Required fields missing"
        message={validationDialogMessage}
        confirmText="OK"
        cancelText=""
        onConfirm={() => setShowValidationDialog(false)}
        onCancel={() => setShowValidationDialog(false)}
        isDanger={false}
        closeOnBackdropClick={true}
      />
    </div>
  );
}