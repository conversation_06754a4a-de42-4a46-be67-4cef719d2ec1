import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Auth, API } from "aws-amplify";
import QRCode from 'qrcode.react';
import Modal from 'react-modal';
import Skeleton from 'react-loading-skeleton';
import { useDebounce } from 'use-debounce';
import { useCookies } from 'react-cookie';

import { useLocation } from '../components/LocationContext';
import { ToggleSwitch } from '../components/ToggleSwitch';

import '../styles/TagList.css';

Modal.setAppElement('#root');

export const TagList = () => {
  const [tags, setTags] = useState([]);
  const [assets, setAssets] = useState([]);
  const [tagCount, setTagCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm] = useDebounce(searchTerm, 500);
  const [loading, setLoading] = useState(true);
  const [noResults, setNoResults] = useState(false);
  const { location, isLocationReady, isLoadingLocation, setIsLoadingLocation } = useLocation();
  const [cookies] = useCookies(['location_mode']);
  const navigate = useNavigate();

  // Update loading state when isLoadingLocation changes
  useEffect(() => {
    if (isLoadingLocation) {
      setLoading(true);
    }
  }, [isLoadingLocation]);

  // Fetch tags based on search term and location
  useEffect(() => {
    const fetchTags = async () => {
      // Don't fetch if we're waiting for location in location mode
      if (cookies.location_mode === 'true' && !isLocationReady) {
        return;
      }

      setLoading(true);
      try {
        const session = await Auth.currentSession();
        const headers = {
          Authorization: `Bearer ${session.getAccessToken().getJwtToken()}`,
          'id-Token': session.getIdToken().getJwtToken()
        };

        // Build query parameters
        const queryParams = new URLSearchParams();

        if (debouncedSearchTerm) {
          queryParams.append('customName', debouncedSearchTerm);
          queryParams.append('description', debouncedSearchTerm);
          queryParams.append('id', debouncedSearchTerm);
        }

        if (cookies.location_mode === 'true' && location.latitude && location.longitude) {
          queryParams.append('latitude', location.latitude);
          queryParams.append('longitude', location.longitude);
        }

        const url = `/tags${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

        const response = await API.get("api", url, { headers });

        if (response) {
          if (response.groups) {
            const nonEmptyGroups = response.groups.filter(group => group.items.length > 0);
            setAssets(nonEmptyGroups);
            setTags([]);
            setNoResults(nonEmptyGroups.length === 0);
          } else {
            setAssets([]);
            setTags(response.items || []);
            setNoResults(!response.items?.length);
          }
        }
      } catch (error) {
        console.error('Error fetching tags:', error);
        setNoResults(true);
      } finally {
        setLoading(false);
        // Reset the context loading state when finished
        setIsLoadingLocation(false);
      }
    };

    fetchTags();
  }, [debouncedSearchTerm, location, cookies.location_mode, isLocationReady]);

  // Log when TagList renders - for debugging purposes
  console.log('TagList rendered');

  // Fetch tag count once on component mount
  useEffect(() => {
    const fetchTagCount = async () => {
      try {
        const session = await Auth.currentSession();
        const response = await API.get("api", "/tags/count", {
          headers: {
            Authorization: `Bearer ${session.getAccessToken().getJwtToken()}`,
            'id-Token': session.getIdToken().getJwtToken()
          }
        });
        if (response?.count) {
          setTagCount(response.count);
        }
      } catch (error) {
        console.error('Error fetching tag count:', error);
      }
    };

    fetchTagCount();
  }, []);

  const renderSkeletonAsset = () => (
    <div className="tag-list">
      <Skeleton className="group-header" textAlign={'left'} height={19} width={150} style={{ marginTop: '1em', marginBottom: '0px' }} />
      {Array(3).fill(0).map((_, index) => (
        <div key={index}>{renderSkeletonItem()}</div>
      ))}
    </div>
  );

  const renderSkeletonItem = () => (
    <div className="tag-list-item">
      <div className='qr-div'>
        <Skeleton width={53} height={52} borderRadius={5} />
      </div>
      <div className='tag-info'>
        <Skeleton width={100} height={15} />
        <Skeleton width={150} height={15} />
      </div>
      <div className='tag-id'>&nbsp;</div>
    </div>
  );

  const renderTagItem = (tag) => (
    <div
      key={tag.id}
      onClick={() => navigate(`/tags/${tag.id}`)}
      className="tag-list-item"
    >
      <div className='qr-div'>
        <QRCode className='local-canvas' value={null} size={31} />
      </div>
      <div className='tag-info'>
        <div>{tag.customName || 'Tag Name'}</div>
        <p>{tag.description || 'Tag Description'}</p>
      </div>
      <div className='tag-id'>{tag.id}</div>
    </div>
  );

  // Update logo visibility
  useEffect(() => {
    const logoContainer = document.querySelector('.logo');
    const isometricNavbar = document.querySelector('.isometric-navbar');

    if (logoContainer) logoContainer.style.display = 'none';
    if (isometricNavbar) isometricNavbar.style.display = null;
  }, []);

  // Handle toggle switch change
  const handleToggleChange = (option) => {
    if (option === 'Assets') {
      navigate('/assets');
    }
  };

  return (
    <div className='settings-container'>
      <div className='title'>
        <h2>Tags</h2>
        <div className='count-container'>
          <div className='count'>
            {loading ? <Skeleton width={30} /> : tagCount}
          </div>
          <div className='count-color'></div>
        </div>
      </div>

      <ToggleSwitch options={['Tags', 'Assets']} activeOption={'Tags'} onChange={handleToggleChange} />

      <div className="search-container">
        <input
          className='search-input'
          type="text"
          value={searchTerm}
          placeholder="Search all tags"
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      <div className="tag-list">
        {loading ? (
          cookies.location_mode === 'true' ? (
            // Render multiple skeleton assets when in location mode
            Array(3).fill(0).map((_, index) => (
              <div key={index}>{renderSkeletonAsset()}</div>
            ))
          ) : (
            // Regular skeleton items when not in location mode
            Array(10).fill(0).map((_, index) => (
              <div key={index}>{renderSkeletonItem()}</div>
            ))
          )
        ) : assets.length > 0 ? (
          assets.map((asset) => (
            <div className='tag-list' key={asset.name}>
              <h3 className="group-header">{asset.name}</h3>
              {asset.items.map(renderTagItem)}
            </div>
          ))
        ) : (
          tags.map(renderTagItem)
        )}

        {!loading && noResults && (
          <div className="no-results">No tags found</div>
        )}
      </div>
    </div>
  );
};