import React, { useState, useEffect } from 'react';
import { Auth } from 'aws-amplify';
import { useParams, useNavigate } from 'react-router-dom';
import '../styles/InviteUser.css';
import '../styles/TagPage.css';

const VerifyInvite = () => {
  const [username, setUsername] = useState('');
  const [code, setCode] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [resending, setResending] = useState(false);
  const [codeRequested, setCodeRequested] = useState(false);
  const [resendCount, setResendCount] = useState(0);
  const [cooldownTimer, setCooldownTimer] = useState(0);
  const [redirecting, setRedirecting] = useState(false);

  const navigate = useNavigate();
  const { username: usernameParam } = useParams();

  // Cooldown timer effect
  useEffect(() => {
    let timer;
    if (cooldownTimer > 0) {
      timer = setTimeout(() => {
        setCooldownTimer(prevTime => prevTime - 1);
      }, 1000);
    }
    return () => clearTimeout(timer);
  }, [cooldownTimer]);

  useEffect(() => {
    if (usernameParam) {
      setUsername(usernameParam);
      
      // Check if we've already requested a code in this session
      const hasRequested = sessionStorage.getItem('codeRequested');
      if (!hasRequested) {
        triggerPasswordReset(usernameParam);
        sessionStorage.setItem('codeRequested', 'true');
        
        // Initialize resend count in session storage
        sessionStorage.setItem('resendCount', '0');
      } else {
        setCodeRequested(true);
        setIsLoading(false);
        
        // Restore resend count from session storage
        const storedCount = sessionStorage.getItem('resendCount');
        if (storedCount) {
          setResendCount(parseInt(storedCount, 10));
        }
      }
    }
  }, [usernameParam]);

  const triggerPasswordReset = async (user) => {
    setIsLoading(true);
    setResending(true);
    setError('');
    try {
      await Auth.forgotPassword(user);
      setSuccess('A verification code has been sent to your email.');
      setCodeRequested(true);
    } catch (err) {
      setError(err.message || 'An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
      setResending(false);
    }
  };

  const handleResendCode = (e) => {
    e.preventDefault();
    
    // Anti-spam checks
    if (resendCount >= 3) {
      setError('Maximum resend attempts reached. Please try again later.');
      return;
    }
    
    if (cooldownTimer > 0) {
      return; // Button should be disabled anyway
    }
    
    // Update resend count
    const newResendCount = resendCount + 1;
    setResendCount(newResendCount);
    sessionStorage.setItem('resendCount', newResendCount.toString());
    
    // Set cooldown timer based on number of attempts (increases with each attempt)
    setCooldownTimer(30 * newResendCount); // 30 seconds, 60 seconds, 90 seconds
    
    triggerPasswordReset(username);
  };

  const handleConfirmReset = async (e) => {
    e.preventDefault();
    setError('');
    if (password !== confirmPassword) {
      setError('Passwords do not match.');
      return;
    }
    try {
      await Auth.forgotPasswordSubmit(username, code, password);
      setSuccess('Password set successfully. Redirecting to login...');
      setRedirecting(true);
      
      // Clear session storage after successful password reset
      sessionStorage.removeItem('codeRequested');
      sessionStorage.removeItem('resendCount');
      
      // Redirect to login page after a short delay
      setTimeout(() => {
        navigate('/');
      }, 2000);
    } catch (err) {
      if (err.code === 'CodeMismatchException') {
        setError('Invalid verification code provided, please try again.');
      } else {
        setError(err.message || 'An error occurred. Please try again.');
      }
    }
  };

  if (isLoading) {
    return (
      <div className="settings-container">
        <div className="panel">
          <p>Sending verification code...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="settings-container">
      <div className="title">
        <h2>Confirm Password</h2>
      </div>
      
      <div className="panel">
        {success && <div className="feedback-message success">{success}</div>}
        {error && <div className="feedback-message error">{error}</div>}
        
        <form onSubmit={handleConfirmReset}>
          <div className="form-group">
            <label htmlFor="code">Verification Code</label>
            <input
              type="text"
              id="code"
              className="input-v2"
              value={code}
              onChange={(e) => setCode(e.target.value)}
              required
              autoComplete="one-time-code"
              disabled={redirecting}
            />
          </div>
          
          {codeRequested && (
            <div className="form-group form-group-left-aligned">
              <button 
                type="button" 
                onClick={handleResendCode} 
                className="send-invite-button"
                disabled={resending || cooldownTimer > 0 || resendCount >= 3 || redirecting}
              >
                {resending ? 'Sending...' : 
                 cooldownTimer > 0 ? `Resend in ${cooldownTimer}s` : 
                 resendCount >= 3 ? 'Max attempts reached' : 'Resend code'}
              </button>
            </div>
          )}
          
          <div className="form-group">
            <label htmlFor="password">New Password</label>
            <input
              type="password"
              id="password"
              className="input-v2"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              autoComplete="new-password"
              disabled={redirecting}
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="confirmPassword">Confirm New Password</label>
            <input
              type="password"
              id="confirmPassword"
              className="input-v2"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
              autoComplete="new-password"
              disabled={redirecting}
            />
          </div>
          
          <button
            type="submit"
            className="send-invite-button"
            disabled={redirecting}
          >
            {redirecting ? 'Redirecting...' : 'Confirm Password'}
          </button>
        </form>
      </div>
    </div>
  );
};

export { VerifyInvite };