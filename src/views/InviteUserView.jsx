import React, { useState, useEffect, useCallback, useMemo, useReducer, memo } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Auth, API } from 'aws-amplify';
import { debounce } from 'lodash';
import { Input } from '../components/Input';
import { UserList } from '../components/UserList';
import Skeleton from 'react-loading-skeleton';
import '../styles/InviteUser.css';
import '../styles/TagList.css';
import '../styles/TagPage.css';

const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

const ROLE_PRIORITY = {
  'Admin': 1,
  'Manager': 2,
  'Operator': 3,
  'PENDING': 4,
  'default': 5
};

const ROLE_DESCRIPTIONS = {
  'Admin': 'Admins manage the account, other users, and tags.',
  'Manager': 'Managers have full access and control.',
  'Operator': 'Operators have access for day-to-day tasks.',
  'default': 'Select a role to see description.'
};

const RoleSelector = memo(({ value, onChange }) => (
  <div className="select-wrapper">
    <select
      id="invite-role-select"
      className="input-v2"
      value={value}
      onChange={onChange}
    >
      <option value="Admin">Admin</option>
      <option value="Manager">Manager</option>
      <option value="Operator">Operator</option>
    </select>
    <img
      src="/expand_down.svg"
      alt="Expand Down"
      className="select-arrow-icon"
    />
  </div>
));

const RoleInfoText = memo(({ description }) => (
  <small className="info-text">
    <span className="info-container">
      <span className="info-icon">i</span>
      <i>{description}</i>
    </span>
  </small>
));

const makeApiRequestWithTimeout = async (method, path, tokens, body = null, timeoutMs = 15000) => {
  if (!tokens) {
    throw new Error('No authentication tokens available');
  }
  
  const options = {
    headers: {
      Authorization: `Bearer ${tokens.accessToken}`,
      'id-Token': tokens.idToken,
    }
  };
  
  if (body) {
    options.body = body;
  }
  
  try {
    return await Promise.race([
      API[method]('api', path, options),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Request timeout')), timeoutMs)
      )
    ]);
  } catch (error) {
    if (error.response && error.response.status === 409) {
      throw new Error(`Account with email ${body?.email || ''} already exists`);
    } else if (error.response && error.response.status === 401) {
      throw new Error('Authentication failed. Please log in again.');
    } else if (error.message === 'Network Error') {
      throw new Error('Network error. Please check your connection.');
    } else if (error.message === 'Request timeout') {
      throw new Error('Request timed out. Please try again.');
    }
    throw error;
  }
};

const makeApiRequestWithRetry = async (method, path, tokens, body = null, retries = 2, delay = 1000) => {
  try {
    return await makeApiRequestWithTimeout(method, path, tokens, body);
  } catch (error) {
    if (error.message === 'Network Error' && retries > 0) {
      await new Promise(r => setTimeout(r, delay));
      return makeApiRequestWithRetry(
        method, path, tokens, body, retries - 1, delay * 1.5
      );
    }
    throw error;
  }
};

function useAuthTokens() {
  const [tokens, setTokens] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  useEffect(() => {
    const fetchTokens = async () => {
      try {
        const session = await Auth.currentSession();
        if (!session) {
          throw new Error('No active session found');
        }
        const newTokens = {
          accessToken: session.getAccessToken().getJwtToken(),
          idToken: session.getIdToken().getJwtToken(),
        };

        setTokens(newTokens);
        setIsLoggedIn(true);
      } catch (err) {
        console.error('Error getting auth tokens:', err);
        setIsLoggedIn(false);
        setError(err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTokens();
  }, []);

  return { tokens, isLoggedIn, isLoading, error };
}

const formatDisplayName = (user) => {
  if (!user) return 'User';
  
  if (user.nickname && user.nickname.trim()) return user.nickname;
  if (!user.email) return user.username || 'User';
  
  const namePart = user.email.split('@')[0];
  if (!namePart) return user.username || 'User';
  
  if (namePart.includes('.')) {
    return namePart.replace(/\.|\b[a-z]/g, m => m === '.' ? ' ' : m.toUpperCase());
  } else if (namePart.includes('_')) {
    return namePart.replace(/_|\b[a-z]/g, m => m === '_' ? ' ' : m.toUpperCase());
  } else if (/[A-Z]/.test(namePart.substring(1))) {
    return namePart.replace(/([A-Z])/g, ' $1').trim();
  }
  
  return namePart.charAt(0).toUpperCase() + namePart.slice(1);
};

function useUsersData(tokens, isLoggedIn) {
  const [users, setUsers] = useState([]);
  const [count, setCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [loadingStage, setLoadingStage] = useState('idle');
  
  const fetchUsersData = useCallback(async () => {
    if (!tokens || !isLoggedIn) return { users: [], count: 0 };
    
    setLoading(true);
    setLoadingStage('users');
    
    try {
      const usersPromise = makeApiRequestWithRetry('get', '/users', tokens);
      const countPromise = makeApiRequestWithRetry('get', '/users/count', tokens);
      
      const usersResponse = await usersPromise;
      setUsers(usersResponse || []);
      setLoadingStage('count');
      
      const countResponse = await countPromise;
      setCount(countResponse?.count || 0);
      setLoadingStage('complete');
      
      return { users: usersResponse || [], count: countResponse?.count || 0 };
    } catch (err) {
      console.error('Error fetching users data:', err);
      setError(err);
      setLoadingStage('error');
      return { users: [], count: 0 };
    } finally {
      setLoading(false);
    }
  }, [tokens, isLoggedIn]);
  
  useEffect(() => {
    if (isLoggedIn) {
      fetchUsersData();
    }
  }, [isLoggedIn, fetchUsersData]);
  
  const processedUsers = useMemo(() => {
    return users.map(user => {
      let priority;
      const isPending = user.userStatus !== 'CONFIRMED' && user.userStatus !== 'CONFIRMED_AUTO';
      if (isPending) {
        priority = ROLE_PRIORITY.PENDING;
      } else {
        const role = user.groups && user.groups.length ? user.groups[0] : '';
        priority = ROLE_PRIORITY[role] || ROLE_PRIORITY.default;
      }
      
      const isOperator = (user.groups || []).includes('Operator');
      
      return {
        ...user,
        _priority: priority,
        _displayName: formatDisplayName(user),
        _isOperator: isOperator,
        _isPending: isPending
      };
    });
  }, [users]);
  
  return { 
    users: processedUsers,
    setUsers,
    count, 
    setCount,
    loading, 
    loadingStage,
    error,
    refreshData: fetchUsersData
  };
}

const formReducer = (state, action) => {
  switch (action.type) {
    case 'RESET_FORM':
      return {
        ...state,
        inviteEmail: '',
        inviteRole: 'Operator',
        isValidEmail: false,
        message: action.message || ''
      };
    case 'UPDATE_EMAIL':
      return { ...state, inviteEmail: action.value };
    case 'UPDATE_ROLE':
      return { ...state, inviteRole: action.value };
    case 'VALIDATE_EMAIL':
      return { ...state, isValidEmail: action.isValid };
    case 'SET_MESSAGE':
      return { ...state, message: action.message };
    case 'BATCH_UPDATE':
      return { ...state, ...action.updates };
    default:
      return state;
  }
};

export function InviteUserView() {
  const { tokens, isLoggedIn, isLoading: isAuthLoading, error: authError } = useAuthTokens();
  const { 
    users, 
    count: userCount, 
    loading: loadingUsers,
    error: usersError,
    refreshData
  } = useUsersData(tokens, isLoggedIn);
  
  const [formState, dispatchFormState] = useReducer(formReducer, {
    inviteEmail: '',
    inviteRole: 'Operator',
    isValidEmail: false,
    message: ''
  });
  
  const { inviteEmail, inviteRole, isValidEmail, message } = formState;
  
  const [filter, setFilter] = useState('All');
  const [inviting, setInviting] = useState(false);
  
  const navigate = useNavigate();
  
  useEffect(() => {
    if (authError) {
      dispatchFormState({ type: 'SET_MESSAGE', message: `Authentication error: ${authError.message}` });
    } else if (usersError) {
      dispatchFormState({ type: 'SET_MESSAGE', message: `Error loading users: ${usersError.message}` });
    }
  }, [authError, usersError]);
  
  const validateEmailDebounced = useMemo(() => 
    debounce((value) => {
      if (!value || value.length < 5) {
        dispatchFormState({ type: 'VALIDATE_EMAIL', isValid: false });
        return;
      }
      
      let hasAt = false, hasDot = false, atIndex = -1;
      for (let i = 0; i < value.length; i++) {
        if (value[i] === '@') {
          hasAt = true;
          atIndex = i;
        } else if (hasAt && value[i] === '.') {
          hasDot = true;
          break;
        }
      }
      
      if (!hasAt || !hasDot || atIndex === 0) {
        dispatchFormState({ type: 'VALIDATE_EMAIL', isValid: false });
        return;
      }
      
      dispatchFormState({ 
        type: 'VALIDATE_EMAIL', 
        isValid: EMAIL_REGEX.test(value)
      });
    }, 300)
  , []);
  
  const handleInviteEmailChange = useCallback((e) => {
    const val = e.target.value;
    dispatchFormState({ type: 'UPDATE_EMAIL', value: val });
    validateEmailDebounced(val);
  }, [validateEmailDebounced]);
  
  const handleRoleChange = useCallback((e) => {
    dispatchFormState({ type: 'UPDATE_ROLE', value: e.target.value });
  }, []);

  const getRoleDescription = useMemo(() => {
    const description = ROLE_DESCRIPTIONS[inviteRole];
    return description !== undefined ? description : ROLE_DESCRIPTIONS.default;
  }, [inviteRole]);
  
  const resetForm = useCallback((message = '') => {
    validateEmailDebounced.cancel();
    dispatchFormState({ 
      type: 'BATCH_UPDATE', 
      updates: {
        inviteEmail: '',
        inviteRole: 'Operator',
        isValidEmail: false,
        message: message || ''
      }
    });
  }, [validateEmailDebounced]);
  
  const usersByFilter = useMemo(() => {
    const operatorUsers = [];
    const pendingUsers = [];
    
    for (let i = 0; i < users.length; i++) {
      const user = users[i];
      if (user._isOperator) {
        operatorUsers.push(user);
      }
      if (user._isPending) {
        pendingUsers.push(user);
      }
    }
    
    return {
      'All': users,
      'Operators': operatorUsers,
      'Pending': pendingUsers
    };
  }, [users]);

  const filteredUsers = useMemo(() => {
    const userSet = usersByFilter[filter] || usersByFilter['All'];
    
    return [...userSet].sort((a, b) => {
      if (a._priority ^ b._priority) return a._priority - b._priority;
      return a._displayName.localeCompare(b._displayName);
    });
  }, [usersByFilter, filter]);
  
  const handleInviteError = useCallback(async (error) => {
    const isUserExistsError = error.message && error.message.includes('already exists');
    
    if (isUserExistsError) {
      resetForm(`Error: ${error.message}`);
      await refreshData();
    } else if (error.message === 'Network Error') {
      dispatchFormState({ 
        type: 'SET_MESSAGE', 
        message: 'Network error. Please check your connection and try again.'
      });
    } else if (error.message === 'Request timeout') {
      dispatchFormState({ 
        type: 'SET_MESSAGE', 
        message: 'Request timed out. Please try again.'
      });
    } else {
      dispatchFormState({ 
        type: 'SET_MESSAGE', 
        message: `Error inviting user: ${error.message}`
      });
    }
  }, [resetForm, refreshData]);

  const handleInvite = useCallback(async (evt) => {
    evt.preventDefault();
    
    if (!tokens || !isLoggedIn) {
      dispatchFormState({ 
        type: 'SET_MESSAGE', 
        message: 'Authentication required. Please log in again.'
      });
      return;
    }
    
    setInviting(true);
    dispatchFormState({ type: 'SET_MESSAGE', message: 'Sending invitation...' });
    
    try {
      await makeApiRequestWithRetry('post', '/invite', tokens, {
        email: inviteEmail,
        role: inviteRole
      });
      
      dispatchFormState({ type: 'SET_MESSAGE', message: 'Invitation sent successfully!' });
      resetForm('Invitation sent successfully!');
      
      await refreshData();
      dispatchFormState({ type: 'SET_MESSAGE', message: 'Invitation sent successfully!' });
      
    } catch (error) {
      await handleInviteError(error);
    } finally {
      setInviting(false);
    }
  }, [tokens, isLoggedIn, inviteEmail, inviteRole, resetForm, refreshData, handleInviteError]);

  const handleFilterChange = useCallback((option) => {
    setFilter(option);
  }, []);
  
  const handleNavigateToUser = useCallback((username) => {
    navigate(`/users/${username}`);
  }, [navigate]);

  const getUserDisplayName = useCallback((user) => {
    return user._displayName || user.username || 'User';
  }, []);

  return (
    <div className="settings-container">
      {/* Header Section */}
      <div className="settings-header-container">
        <Link to="/tags" className="back-arrow">
          <img src="/arrow_back.svg" alt="Back" className="backarrow" />
          {' '}Tags
        </Link>
      </div>

      {/* Title and Count */}
      <div className="title">
        <h2>Users</h2>
        <div className="count-container">
          <div className="count">
            {loadingUsers ? <Skeleton width={30} /> : userCount}
          </div>
          <div className="count-color"></div>
        </div>
      </div>

      {/* Feedback Message */}
      {message && (
        <div className={`feedback-message ${message.includes('Error') ? 'error' : 'success'}`}>
          {message}
        </div>
      )}
      
      <UserList
        filter={filter}
        onFilterChange={handleFilterChange}
        filteredUsers={filteredUsers}
        loadingUsers={loadingUsers}
        isAuthLoading={isAuthLoading}
        getUserDisplayName={getUserDisplayName}
        onUserClick={handleNavigateToUser}
      />

      {/* Invitation Form */}
      <h3 className="settings-panel-body-title">Invite User</h3>
      <div className="panel">
        <div className="form-group">
          <label htmlFor="invite-email-input">Email</label>
          <Input
            className="input-v2"
            value={inviteEmail}
            onChange={handleInviteEmailChange}
            placeholder="<EMAIL>"
            name="invite-email"
            id="invite-email-input"
            type="email"
            autoComplete="off"
            dataFormType="other"
          />
        </div>

        <div className="form-group">
          <label htmlFor="invite-role-select">Role</label>
          <RoleSelector 
            value={inviteRole} 
            onChange={handleRoleChange} 
          />
        </div>

        <div className="form-group form-group-left-aligned">
          <RoleInfoText description={getRoleDescription} />
        </div>

        <button
          className="send-invite-button"
          onClick={handleInvite}
          disabled={inviting || !isValidEmail || isAuthLoading}
        >
          {inviting ? 'Sending...' : 'Send Invite'}
        </button>
      </div>
    </div>
  );
}