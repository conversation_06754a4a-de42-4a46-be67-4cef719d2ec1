import React, { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Auth } from 'aws-amplify';

import ConfirmationDialog from '../components/ConfirmationDialog';
import UserHeader from '../components/user/UserHeader';
import UserAvatar from '../components/user/UserAvatar';
import FeedbackMessage from '../components/user/FeedbackMessage';
import UserForm from '../components/user/UserForm';
import UserActions from '../components/user/UserActions';

import useAuth from '../hooks/useAuth';
import useFormReducer from '../hooks/useFormReducer';
import useImageUpload from '../hooks/useImageUpload';
import useUserData from '../hooks/useUserData';
import { useNavigationConfirmation, useDeleteConfirmation } from '../hooks/useConfirmationDialog';

import ImageProcessor from '../utils/imageProcessing';
import { createErrorHandler } from '../utils/apiRequests';
import { makeApiRequestWithCancellation, fetchUserData } from '../utils/apiRequests';

import '../styles/InviteUser.css';
import '../styles/TagList.css';
import '../styles/TagPage.css';
import '../styles/UserPage.css';

export function UserPage() {
  const { username } = useParams();
  const navigate = useNavigate();
  const fileInputRef = useRef(null);
  const isMountedRef = useRef(true);
  const refetchTimeoutRef = useRef(null);

  const { tokens, isLoading: isAuthLoading, isLoggedIn, error: authError } = useAuth();
  const { 
    userData, 
    currentUserData, 
    loading: isDataLoading, 
    error: userDataError, 
    cognitoUsername 
  } = useUserData(username, tokens, isLoggedIn);

  const [uiState, setUiState] = useState({
    message: '',
    saving: false
  });

  const handleError = useCallback(createErrorHandler(setUiState), []);

  const isLoading = isAuthLoading || isDataLoading;

  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
      ImageProcessor.clearContext();
      if (refetchTimeoutRef.current) {
        clearTimeout(refetchTimeoutRef.current);
      }
    };
  }, []);

  const isCurrentUserOperator = useMemo(() => {
    if (isLoading) return false;
    return currentUserData?.role === 'Operator';
  }, [isLoading, currentUserData]);

  const initialUserIdentifier = useMemo(() => {
    return cognitoUsername || username;
  }, [cognitoUsername, username]);

  const {
    formState,
    dispatch,
    handleDisplayNameChange,
    handleRoleChange,
    getRoleDescription
  } = useFormReducer(userData, initialUserIdentifier);

  const userIdentifier = useMemo(() => {
    return formState.cognitoId || cognitoUsername || formState.email || username;
  }, [formState.cognitoId, cognitoUsername, formState.email, username]);

  const isSelfDeletion = useMemo(() => {
    if (!currentUserData || !formState.email) return false;
    return currentUserData.username === userIdentifier || 
           currentUserData.email === formState.email;
  }, [currentUserData, userIdentifier, formState.email]);

  const navigationTarget = isCurrentUserOperator ? '/tags' : '/invite';

  const {
    handleAvatarClick,
    handleAvatarChange,
    handleImageLoad,
    handleImageError,
    isImageLoading
  } = useImageUpload(formState, dispatch, setUiState, isMountedRef, handleError, fileInputRef);

  const handleSave = useCallback(async () => {
    if (!tokens || !isLoggedIn) {
      setUiState(prev => ({ 
        ...prev, 
        message: 'Authentication required. Please log in again.' 
      }));
      return;
    }

    // Clear any existing refetch timeout
    if (refetchTimeoutRef.current) {
      clearTimeout(refetchTimeoutRef.current);
      refetchTimeoutRef.current = null;
    }

    setUiState(prev => ({ ...prev, saving: true, message: '' }));
    
    const saveRequest = makeApiRequestWithCancellation('post', `/users/${userIdentifier}`, tokens);

    try {
      const updateData = { 
        nickname: formState.displayName, 
        role: formState.role 
      };

      // Check if we're uploading a new image
      const isUploadingNewImage = formState.avatarFile && formState.avatarBase64;
      
      if (isUploadingNewImage) {
        updateData.picture = formState.avatarBase64;
      }

      const result = await saveRequest.execute(updateData);

      if (!isMountedRef.current) return;

      // Handle image uploads with special care for CloudFront delay
      if (isUploadingNewImage) {
        // First update - keep showing base64 preview while CloudFront propagates
        dispatch({ 
          type: 'UPDATE_FROM_SAVE', 
          picture: result.picture,
          keepBase64: true // Keep showing the base64 preview
        });

        // Schedule a refetch after 3 seconds to get the updated image URL
        refetchTimeoutRef.current = setTimeout(async () => {
          try {
            if (!isMountedRef.current) return;
            
            // Re-fetch user data to get the most up-to-date image URL
            const userDataResponse = await fetchUserData(userIdentifier, tokens);
            
            if (userDataResponse && userDataResponse.userData && isMountedRef.current) {
              // Now we can update with the refreshed data and clear the base64
              dispatch({
                type: 'UPDATE_FROM_SAVE',
                picture: userDataResponse.userData.picture,
                clearBase64: true // Now we can clear the base64 data
              });
            }
          } catch (error) {
            console.warn('Error re-fetching user data:', error);
            // If re-fetch fails, we'll just continue showing the base64 preview
          }
        }, 3000); // Wait 3 seconds before re-fetching
      } else {
        // For non-image updates, use the standard dispatch
        dispatch({ 
          type: 'UPDATE_FROM_SAVE', 
          picture: result.picture 
        });
      }

      setUiState(prev => ({ 
        ...prev, 
        saving: false,
        message: 'User updated successfully!' 
      }));
    } catch (error) {
      if (!isMountedRef.current) return;
      
      handleError(error, 'Error updating user');
      
      setUiState(prev => ({ ...prev, saving: false }));
    }
  }, [tokens, isLoggedIn, formState, userIdentifier, handleError, dispatch]);

  const onDelete = useCallback(async () => {
    const deleteRequest = makeApiRequestWithCancellation('del', `/users/${userIdentifier}`, tokens);
    
    try {
      await deleteRequest.execute();
      
      if (isSelfDeletion) {
        try {
          await Auth.signOut();
        } catch (error) {
          console.error('Error signing out after self-deletion:', error);
        }
        navigate('/');
      } else {
        navigate('/invite');
      }
    } catch (error) {
      if (!isMountedRef.current) return;
      throw error;
    }
  }, [userIdentifier, tokens, navigate, isSelfDeletion]);

  const { 
    showDeleteConfirmation,
    isDeleting,
    handleDelete,
    confirmDelete,
    cancelDelete
  } = useDeleteConfirmation({
    tokens,
    isLoggedIn,
    isAccountOwner: formState.isAccountOwner,
    userId: userIdentifier,
    onDelete,
    setMessage: setUiState
  });

  const {
    showNavigationConfirmation,
    handleNavigationAttempt,
    confirmNavigation,
    cancelNavigation
  } = useNavigationConfirmation(
    navigate,
    navigationTarget,
    formState.hasUnsavedChanges,
    userIdentifier
  );

  useEffect(() => {
    if (authError) {
      setUiState(prev => ({ 
        ...prev, 
        message: `Authentication error: ${authError.message || 'Failed to authenticate'}` 
      }));
    }
  }, [authError]);

  useEffect(() => {
    if (userDataError) {
      handleError(userDataError, `Error fetching user: ${userDataError.message || 'Could not load user data'}`);
    }
  }, [userDataError, handleError]);

  return (
    <div className="settings-container" role="main" aria-label="User settings">
      {/* Header with back link */}
      <UserHeader 
        navigationTarget={navigationTarget}
        handleNavigationAttempt={handleNavigationAttempt}
        isLoading={isLoading}
        isCurrentUserOperator={isCurrentUserOperator}
      />

      {/* User info block with avatar */}
      <div className="user-card-mb">
        <UserAvatar
          isLoading={isLoading}
          formState={formState}
          handleAvatarClick={handleAvatarClick}
          handleAvatarChange={handleAvatarChange}
          handleImageLoad={handleImageLoad}
          handleImageError={handleImageError}
          isImageLoading={isImageLoading}
          fileInputRef={fileInputRef}
        />
      </div>

      {/* Feedback message */}
      {uiState.message && <FeedbackMessage message={uiState.message} />}

      {/* Main form panel */}
      <UserForm
        isLoading={isLoading}
        formState={formState}
        handleDisplayNameChange={handleDisplayNameChange}
        handleRoleChange={handleRoleChange}
        getRoleDescription={getRoleDescription}
        isCurrentUserOperator={isCurrentUserOperator}
      />

      {/* User actions (save and delete buttons) */}
      <div className="panel">
        <UserActions
          isLoading={isLoading}
          uiState={{...uiState, deleting: isDeleting}}
          formState={formState}
          handleSave={handleSave}
          handleDelete={handleDelete}
          isSelfDeletion={isSelfDeletion}
        />
      </div>

      {/* Confirmation dialog for deletion */}
      <ConfirmationDialog
        isOpen={showDeleteConfirmation}
        title={isSelfDeletion ? "Delete Your Account" : "Delete User"}
        message={isSelfDeletion 
          ? "Are you sure you want to delete your account? You will be signed out immediately."
          : `Are you sure you want to delete "${formState.email}"?`}
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={confirmDelete}
        onCancel={cancelDelete}
        isDanger={true}
      />

      {/* Confirmation dialog for navigation with unsaved changes */}
      <ConfirmationDialog
        isOpen={showNavigationConfirmation}
        title="Unsaved Changes"
        message="You have unsaved changes. Are you sure you want to leave this page? Your changes will be lost."
        confirmText="Leave Page"
        cancelText="Stay"
        onConfirm={confirmNavigation}
        onCancel={cancelNavigation}
        isDanger={false}
      />
    </div>
  );
}