import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCookies } from 'react-cookie';
import { Auth } from 'aws-amplify';
import MetaTags from '../components/MetaTags';
import { Thinkertag } from '../components/Thinkertag';
import { isDebugModeEnabled, isDevelopmentMode } from '../utils/debugUtils';
import '../styles/views/landingPage/LandingPage.css';

export const LandingPage = () => {
  const [user, setUser] = useState(null);
  const [cookies] = useCookies(['idToken']);
  const navigate = useNavigate();

  // Quote cards visibility logic:
  // - In production: always show quote cards
  // - In development: show only when debug flag is present
  const showQuoteCards = isDevelopmentMode()
    ? isDebugModeEnabled('debug_show_quotes')
    : true;

  useEffect(() => {
    const checkUser = async () => {
      try {
        const currentUser = await Auth.currentAuthenticatedUser();
        setUser(currentUser);
      } catch (error) {
        setUser(null);
      }
    };

    checkUser();
  }, []);

  const handleCTAClick = () => {
    navigate('/f/ea/operations-scale/introduction');
  };

  const handleConsole = () => {
    navigate('/tags');
  };

  const handleLogin = () => {
    navigate('/login');
  };

  return (
    <div className="landing-page">
      <MetaTags
        title="Thinkertags - The QR code you actually want to scan"
        description="Turn every scan into a moment of delight. Because the details you care about are the ones customers remember."
        imageUrl="https://thinkertags.com/landingpage/features/how-it-looks.png"
        url="https://thinkertags.com/"
        type="website"
      />
      <section className="hero">
        <div className="hero-content">
          <div className="hero-thinkertag-container">
            <Thinkertag
              value="gold"
              tagId="OOOQZW"

              url="https://api.thinkertags.com/OOOQZW"
            />
          </div>
          <img src="/logo-gold.svg" alt="Thinkertags Logo" className="hero-logo" />
          <div className="hero-title-container">
            <h1 className="hero-main-title">The QR code with <span className="powerful-wrapper">powerful<img src="/landingpage/hero-power-doodle.svg" alt="" className="hero-power-doodle" aria-hidden="true" />  </span> features</h1>
          </div>

          <p className="hero-cta-text">Get Thinkertags for free! Apply for Early Access and help make it ready for prime-time!</p>
          <button className="primary-btn" onClick={handleCTAClick}>Get Early Access</button>
          <button className="sign-in-button" onClick={user ? handleConsole : handleLogin}>Access Console</button>
          {/* <button className="secondary-btn" onClick={handleCTAClick}>Learn More</button> */}
        </div>
      </section>


      {showQuoteCards && (
        <div className="quotes-section">
          <div className='thinkertags-quote-component'>
            <div className="thinkertags-quote-card">
              <div className="thinkertags-quote-avatar-container">
                <img src='/images/headshots/gus.jpg' alt="Gustaf Ydström" className="thinkertags-quote-avatar" />
              </div>
              <div className="thinkertags-quote-content">
                <p className="thinkertags-quote-text">"It's the most versatile re-programmable OR for businesses"</p>
                <p className="thinkertags-quote-author">Carl Gustaf Ydström</p>
              </div>
            </div>
          </div>

          <div className='thinkertags-quote-component-left'>
            <div className="thinkertags-quote-card-left">
              <div className="thinkertags-quote-avatar-container-left">
                <img src='/images/headshots/chris.jpg' alt="Krzysztof Strzelec" className="thinkertags-quote-avatar" />
              </div>
              <div className="thinkertags-quote-content-left">
                <p className="thinkertags-quote-text">"You manage all your OR codes from the console"</p>
                <p className="thinkertags-quote-author">Krzysztof Strzelec</p>
              </div>
            </div>
          </div>
        </div>
      )}


      {/* Features Section */}
      <section className="features">
        <div className="features-title-container">
          <h2>We made the <span className="best-wrapper">best<img src="/landingpage/features/saturn-with-stars-doodle.svg" alt="" className="saturn-with-stars-doodle" aria-hidden="true" /></span> <br></br> features for QR codes</h2>
        </div>
        <p>We build the best features for managing and using QR codes</p>
        <img src="/landingpage/features/how-it-looks.png" alt="How it looks" className="how-it-looks" />


        <div className="feature">
          <div className='feature-text'>
            {/* <h3>Super Easy Redirects</h3> */}
          </div>
          <img src="/landingpage/features/redirect-auth.png" alt="Redirect Auhenticated" />
        </div>



      {showQuoteCards && (
        <div className="quotes-section">
          <div className='thinkertags-quote-component'>
            <div className="thinkertags-quote-card">
              <div className="thinkertags-quote-avatar-container">
                <img src='/images/headshots/gus.jpg' alt="Gustaf Ydström" className="thinkertags-quote-avatar" />
              </div>
              <div className="thinkertags-quote-content">
                <p className="thinkertags-quote-text">"Our employees get a separate redirect with the same QR code!"</p>
                <p className="thinkertags-quote-author">Carl Gustaf Ydström</p>
              </div>
            </div>
          </div>

          <div className='thinkertags-quote-component-left'>
            <div className="thinkertags-quote-card-left">
              <div className="thinkertags-quote-avatar-container-left">
                <img src='/images/headshots/chris.jpg' alt="Krzysztof Strzelec" className="thinkertags-quote-avatar" />
              </div>
              <div className="thinkertags-quote-content-left">
                <p className="thinkertags-quote-text">"“We’ve enabled location on our most important assets"</p>
                <p className="thinkertags-quote-author">Krzysztof Strzelec</p>
              </div>
            </div>
          </div>
        </div>
      )}

        <div className="feature">
          <div className='feature-text'>
            {/* <h3>Add Location</h3> */}
          </div>
          <img src="/landingpage/features/tag-location.png" alt="Tag Location" />
        </div>




        {/* <div className="feature">
          <div className='feature-text'>
            <h3>Attach to Objects</h3>
          </div>
          <img src="/landingpage/features/attach-obj.png" alt="Attach an object" />
        </div>

        <div className="feature">
          <div className='feature-text'>
            <h3>StoreFront</h3>
          </div>
          <img src="/landingpage/features/obj-storefront.png" alt="Object StoreFront" />
        </div> */}

      </section>


      {/* Article Cards Section */}
      {/* <section className="article-cards">
        <h2>Thinkertag Stories</h2>
        <div className="cards-container">
          {featureCards.map((card, index) => (
            <div
              key={index}
              className="card"
            >
              <div className="card-image-container">
                <img
                  src={`/${card.src || 'feature-card-default.png'}`}
                  alt={`${card.title} illustration`}
                  className="card-image"
                />
              </div>
              <h3>{card.title}</h3>
              <p className="subtitle">{card.subtitle}</p>
              <p className="description">{card.description}</p>
              <p className="tagline"><strong>{card.tagline}</strong></p>
            </div>
          ))}
        </div>
      </section> */}

      {/* CTA Section
      <section className="cta">
        <h2>Are You Ready to Transform Your Operations?</h2>
        <p>Take our quick assessment to discover your Thinkertags <strong>Readiness Score</strong>. Find out where your organization stands on the scale from "opportunity" to "highly qualified" and see exactly how Thinkertags can revolutionize your asset management.</p>
        <button className="alternative-btn">Get Your Readiness Score</button>
      </section> */}

{/* Enhanced City Insights with Brand Principles */}
{/* <section className="thinkertags-ethos">
  <div className="ethos-container">
    <div className="ethos-header">
      <span className="micro-heading">Our Journey</span>
      <h2>Every Place Taught Us Something Essential</h2>
    </div>

    <div className="city-insights">
      <div className="insight-container">
        <div className="city-group">
          <span className="city-tag">Stockholm</span>
          <span className="city-tag">Hamburg</span>
          <span className="city-tag">Copenhagen</span>
        </div>
        <h3 className="insight-title">Invisible Simplicity</h3>
        <p className="insight-story">Here we learned that true efficiency isn't flashy—it's nearly invisible. Every unnecessary click and search is a barrier between your technicians and their work.</p>
      </div>

      <div className="insight-container">
        <div className="city-group">
          <span className="city-tag">Bali</span>
          <span className="city-tag">Marbella</span>
          <span className="city-tag">Lisbon</span>
        </div>
        <h3 className="insight-title">Beautiful Utility</h3>
        <p className="insight-story">These places showed us that beauty and function aren't opposing forces. Your tools should be as distinctive as they are useful—because what stands out gets used.</p>
      </div>

      <div className="insight-container">
        <div className="city-group">
          <span className="city-tag">London</span>
          <span className="city-tag">New York</span>
          <span className="city-tag">Paris</span>
        </div>
        <h3 className="insight-title">Ambitious Scale</h3>
        <p className="insight-story">In these urban centers, we confronted the challenge of scale. Systems that work for a handful of assets must work just as flawlessly for thousands—without losing their human touch.</p>
      </div>
    </div>

    <div className="ethos-connector">
      <div className="connector-line"></div>
      <div className="connector-dot"></div>
      <div className="connector-line"></div>
    </div>

    <div className="ethos-message">
      <p>These aren't just places we worked—they're the roots of our approach. What began as a simple idea transformed through each new environment, challenge, and conversation. Thinkertags exists because no single perspective was enough.</p>
      <p className="ethos-signature">This journey continues with you.</p>
    </div>
  </div>
</section> */}

      {/* Footer */}
      <footer className="footer">
        <div className="footer-content">
          <img src="/logo.svg" alt="Thinkertags Logo" className="footer-logo" />
          <p className="copyright">© {new Date().getFullYear()} Thinkertags. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};