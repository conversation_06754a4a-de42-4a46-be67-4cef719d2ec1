import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import '../../../../styles/views/forms/GuidePages.css';

export const AssetsPortfolio = () => {
  const [selectedOption, setSelectedOption] = useState(null);
  const navigate = useNavigate();

    useEffect(() => {
      // Scroll to top when component mounts
      window.scrollTo(0, 0);
    }, []);

  const handleOptionSelect = (option) => {
    setSelectedOption(option);
    
    // Get existing data and update with new selection
    const existingData = JSON.parse(localStorage.getItem('eaGuideData') || '{}');
    const updatedData = {
      ...existingData,
      assetsCount: option,
      completedSteps: (existingData.completedSteps || 0) + 1
    };
    
    // Store updated data
    localStorage.setItem('eaGuideData', JSON.stringify(updatedData));
    
    // Navigate to next page after selecting an option
    setTimeout(() => {
      navigate('/f/ea/operations-scale/service-volume');
    }, 300); // Short delay for the selection animation to be visible
  };

  const options = [
    { id: 'less-than-1000', label: 'Less than 1,000 assets', value: 'less-than-1000' },
    { id: '1000-to-5000', label: '1,000 to 5,000 assets', value: '1000-to-5000' },
    { id: '5000-to-25000', label: '5,000 to 25,000 assets', value: '5000-to-25000' },
    { id: 'more-than-25000', label: 'More than 25,000 assets', value: 'more-than-25000' },
  ];

  return (
    <div className="enterprise-guide">
      <div className="guide-progress">
        <div className="progress-bar">
          <div className="progress-fill" style={{ width: '15%' }}></div>
        </div>
      </div>
      
      <div className="guide-branding">
        <img src="/logo.svg" alt="Thinkertags Logo" className="guide-logo" />
      </div>

      <div className="guide-content">
        <h1>How many physical assets does your organization manage?</h1>

        <div className="options-container">
          {options.map((option) => (
            <button
              key={option.id}
              className={`option-button ${selectedOption === option.value ? 'selected' : ''}`}
              onClick={() => handleOptionSelect(option.value)}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};