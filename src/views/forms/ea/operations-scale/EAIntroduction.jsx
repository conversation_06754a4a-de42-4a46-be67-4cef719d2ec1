import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import '../../../../styles/views/forms/GuidePages.css';

export const EAIntroduction = () => {
  const navigate = useNavigate();

  // Initialize guide data when starting the assessment
  useEffect(() => {
    localStorage.setItem('eaGuideData', JSON.stringify({
      startTime: new Date().toISOString(),
      completedSteps: 0
    }));

    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);

  const handleContinue = () => {
    // Navigate to the assets portfolio page
    navigate('/f/ea/operations-scale/assets-portfolio');
  };

  return (
    <div className="enterprise-guide">
      <div className="guide-progress">
        <div className="progress-bar">
          <div className="progress-fill" style={{ width: '5%' }}></div>
        </div>
      </div>
      
      <div className="guide-branding">
        <img src="/logo.svg" alt="Thinkertags Logo" className="guide-logo" />
      </div>

      <div className="guide-content">
        <div className="ea-intro-heading">Early Access</div>
        <p className="intro-description">
          Is your organization ready to transform service operations with Thinkertags?<br /> <br />
          Take this 2-minute assessment to discover your early access eligibility and apply.
        </p>
        
        <div className="guide-bottom-section">
        <p className="legal-text">
          By clicking <b>Continue</b>, you agree that Thinkertags may use your responses to personalize your experience.
        </p>
        <div className="guide-actions">
          <button 
            className="guide-next-button" 
            onClick={handleContinue}
          >
            Continue
          </button>
        </div>
        </div>
      </div>
    </div>
  );
};