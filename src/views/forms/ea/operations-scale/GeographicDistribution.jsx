import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import '../../../../styles/views/forms/GuidePages.css';

export const GeographicDistribution = () => {
  const [selectedOption, setSelectedOption] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);

  const handleOptionSelect = (option) => {
    setSelectedOption(option);
    
    // Get existing data and update with new selection
    const existingData = JSON.parse(localStorage.getItem('eaGuideData') || '{}');
    const updatedData = {
      ...existingData,
      geographicRegions: option,
      completedSteps: (existingData.completedSteps || 0) + 1
    };
    
    // Store updated data
    localStorage.setItem('eaGuideData', JSON.stringify(updatedData));
    
    // Navigate to next page after selecting an option
    setTimeout(() => {
      navigate('/f/ea/operations-scale/team-composition');
    }, 300); // Short delay for the selection animation to be visible
  };

  const options = [
    { id: '1-2', label: '1-2 regions', value: '1-2' },
    { id: '3-5', label: '3-5 regions', value: '3-5' },
    { id: '6-10', label: '6-10 regions', value: '6-10' },
    { id: '11-20', label: '11-20 regions', value: '11-20' },
    { id: '21-plus', label: '21+ regions', value: '21-plus' },
  ];

  return (
    <div className="enterprise-guide">
      <div className="guide-progress">
        <div className="progress-bar">
          <div className="progress-fill" style={{ width: '45%' }}></div>
        </div>
      </div>
      
      <div className="guide-branding">
        <img src="/logo.svg" alt="Thinkertags Logo" className="guide-logo" />
      </div>

      <div className="guide-content">
        <h1>Across how many distinct geographic regions does your team operate?</h1>

        <div className="options-container">
          {options.map((option) => (
            <button
              key={option.id}
              className={`option-button ${selectedOption === option.value ? 'selected' : ''}`}
              onClick={() => handleOptionSelect(option.value)}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};
