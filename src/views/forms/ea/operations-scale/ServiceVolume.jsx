import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import '../../../../styles/views/forms/GuidePages.css';

export const ServiceVolume = () => {
  const [selectedOption, setSelectedOption] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);

  const handleOptionSelect = (option) => {
    setSelectedOption(option);
    
    // Get existing data and update with new selection
    const existingData = JSON.parse(localStorage.getItem('eaGuideData') || '{}');
    const updatedData = {
      ...existingData,
      serviceVolume: option,
      completedSteps: (existingData.completedSteps || 0) + 1
    };
    
    // Store updated data
    localStorage.setItem('eaGuideData', JSON.stringify(updatedData));
    
    // Navigate to next page after selecting an option
    setTimeout(() => {
      navigate('/f/ea/operations-scale/geographic-distribution');
    }, 300); // Short delay for the selection animation to be visible
  };

  const options = [
    { id: 'under-1000', label: 'Under 1,000', value: 'under-1000' },
    { id: '1000-5000', label: '1,000-5,000', value: '1000-5000' },
    { id: '5000-20000', label: '5,000-20,000', value: '5000-20000' },
    { id: '20000-100000', label: '20,000-100,000', value: '20000-100000' },
    { id: '100000-plus', label: 'Over 100,000', value: '100000-plus' },
  ];

  return (
    <div className="enterprise-guide">
      <div className="guide-progress">
        <div className="progress-bar">
          <div className="progress-fill" style={{ width: '30%' }}></div>
        </div>
      </div>
      
      <div className="guide-branding">
        <img src="/logo.svg" alt="Thinkertags Logo" className="guide-logo" />
      </div>

      <div className="guide-content">
        <h1>Approximately how many service events does your team handle annually?</h1>

        <div className="options-container">
          {options.map((option) => (
            <button
              key={option.id}
              className={`option-button ${selectedOption === option.value ? 'selected' : ''}`}
              onClick={() => handleOptionSelect(option.value)}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};