import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import '../../../../styles/views/forms/GuidePages.css';

export const CurrentDocumentation = () => {
  const [selectedOption, setSelectedOption] = useState(null);
  const navigate = useNavigate();

    useEffect(() => {
      // Scroll to top when component mounts
      window.scrollTo(0, 0);
    }, []);

  const handleOptionSelect = (option) => {
    setSelectedOption(option);
    
    // Get existing data and update with new selection
    const existingData = JSON.parse(localStorage.getItem('eaGuideData') || '{}');
    const updatedData = {
      ...existingData,
      documentationMethod: option,
      completedSteps: (existingData.completedSteps || 0) + 1
    };
    
    // Store updated data
    localStorage.setItem('eaGuideData', JSON.stringify(updatedData));
    
    // Navigate to next page after selecting an option
    setTimeout(() => {
      navigate('/f/ea/operational-priorities');
    }, 300); // Short delay for the selection animation to be visible
  };

  const options = [
    { id: 'mobile-apps', label: 'Separate mobile apps', value: 'mobile-apps' },
    { id: 'central-system', label: 'Central system', value: 'central-system' },
    { id: 'mixed-methods', label: 'Mixed methods', value: 'mixed-methods' },
    { id: 'paper-forms', label: 'Paper forms', value: 'paper-forms' }
  ];

  return (
    <div className="enterprise-guide">
      <div className="guide-progress">
        <div className="progress-bar">
          <div className="progress-fill" style={{ width: '75%' }}></div>
        </div>
      </div>
      
      <div className="guide-branding">
        <img src="/logo.svg" alt="Thinkertags Logo" className="guide-logo" />
      </div>

      <div className="guide-content">
        <h1>How do your technicians currently document service activities?</h1>

        <div className="options-container">
          {options.map((option) => (
            <button
              key={option.id}
              className={`option-button ${selectedOption === option.value ? 'selected' : ''}`}
              onClick={() => handleOptionSelect(option.value)}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};