// Updates for TeamComposition.jsx
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import '../../../../styles/views/forms/GuidePages.css';

export const TeamComposition = () => {
  const [selectedOption, setSelectedOption] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);

  const handleOptionSelect = (option) => {
    setSelectedOption(option);
    
    // Get existing data and update with new selection
    const existingData = JSON.parse(localStorage.getItem('eaGuideData') || '{}');
    const updatedData = {
      ...existingData,
      teamSize: option,
      completedSteps: (existingData.completedSteps || 0) + 1
    };
    
    // Store updated data
    localStorage.setItem('eaGuideData', JSON.stringify(updatedData));
    
    // Navigate to next page after selecting an option
    setTimeout(() => {
      navigate('/f/ea/operations-scale/current-documentation');
    }, 300); // Short delay for the selection animation to be visible
  };

  const options = [
    { id: '5-19', label: '5-19 team members', value: '5-19' },
    { id: '20-49', label: '20-49 team members', value: '20-49' },
    { id: '50-99', label: '50-99 team members', value: '50-99' },
    { id: '100-499', label: '100-499 team members', value: '100-499' },
    { id: '500-plus', label: '500+ team members', value: '500-plus' },
  ];

  return (
    <div className="enterprise-guide">
      <div className="guide-progress">
        <div className="progress-bar">
          <div className="progress-fill" style={{ width: '60%' }}></div>
        </div>
      </div>
      
      <div className="guide-branding">
        <img src="/logo.svg" alt="Thinkertags Logo" className="guide-logo" />
      </div>

      <div className="guide-content">
        <h1>How large is your field service workforce?</h1>
        <div className="options-container">
          {options.map((option) => (
            <button
              key={option.id}
              className={`option-button ${selectedOption === option.value ? 'selected' : ''}`}
              onClick={() => handleOptionSelect(option.value)}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};
