import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import '../../../../styles/views/forms/GuidePages.css';
import '../../../../styles/views/forms/ResultsPages.css';

export const NextSteps = () => {
  const navigate = useNavigate();
  const [checkedItems, setCheckedItems] = useState([false, false, false]);
  
  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);

  // Function to progressively check items when page loads
  useEffect(() => {
    const checkItems = () => {
      const timeouts = [1000, 2000, 3000];
      
      timeouts.forEach((timeout, index) => {
        setTimeout(() => {
          setCheckedItems(prev => {
            const newChecked = [...prev];
            newChecked[index] = true;
            return newChecked;
          });
        }, timeout);
      });
    };
    
    checkItems();
  }, []);
  
  const handleContinue = () => {
    navigate('/f/ea/contact-information-name');
  };
  
  return (
    <div className="enterprise-guide">
      {/* Progress bar - 50% complete */}
      <div className="guide-progress">
        <div className="progress-bar">
          <div className="progress-fill" style={{ width: '100%' }}></div>
        </div>
      </div>
      
      <div className="guide-branding">
        <img src="/logo.svg" alt="Thinkertags Logo" className="guide-logo" />
      </div>

      <div className="guide-content">
        <p className="intro-description no-margin"> <span className="accent-text">
        You're qualified for Thinkertags Early Access!</span> Here's what you can expect:</p>
        
        {/* Next steps */}
        
        <div className="next-steps-container">
          <div className={`next-step-item ${checkedItems[0] ? 'checked' : ''}`}>
            <div className="checkmark-icon">
              {checkedItems[0] && <span>✓</span>}
            </div>
            <div className="next-step-content">
              <h3>Dedicated onboarding with a Customer Engineer</h3>
              <p>One-on-one support from expert engineers who understand your industry needs.</p>
            </div>
          </div>
          
          <div className={`next-step-item ${checkedItems[1] ? 'checked' : ''}`}>
            <div className="checkmark-icon">
              {checkedItems[1] && <span>✓</span>}
            </div>
            <div className="next-step-content">
              <h3>Custom implementation roadmap for your specific challenges</h3>
              <p>A tailored plan designed around your unique operational requirements.</p>
            </div>
          </div>
          
          <div className={`next-step-item ${checkedItems[2] ? 'checked' : ''}`}>
            <div className="checkmark-icon">
              {checkedItems[2] && <span>✓</span>}
            </div>
            <div className="next-step-content">
              <h3>Priority access to the Thinkertags Enterprise console</h3>
              <p>Advanced features and capabilities exclusive to enterprise customers.</p>
            </div>
          </div>
        </div>
        
        <div className="guide-bottom-section">
          <div className="guide-actions">
            <button 
              className="guide-next-button"
              onClick={handleContinue}
            >
              Continue
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};