import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import '../../../../styles/views/forms/GuidePages.css';

export const ConfirmationExperience = () => {
  const [showContent, setShowContent] = useState(false);
  
  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);
  
  return (
    <div className="enterprise-guide">
      <div className="guide-progress">
        <div className="progress-bar">
          <div className="progress-fill" style={{ width: '100%' }}></div>
        </div>
      </div>
      
      <div className="guide-branding">
        <img src="/logo.svg" alt="Thinkertags Logo" className="guide-logo" />
      </div>

      <div className="guide-content">
        <div className={`insights-container ${showContent ? 'delay-300' : ''}`}>
          {/* Custom styled checkmark since we're not using ResultsPages.css */}
          <div style={{
            width: '70px',
            height: '70px',
            backgroundColor: '#28a745',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontSize: '2rem',
            margin: '0 auto 24px',
            boxShadow: '0 0 20px rgba(40, 167, 69, 0.4)'
          }}>
            <span>✓</span>
          </div>
          
          <h1 className="ea-intro-heading" style={{ justifyContent: 'center', marginBottom: '24px' }}>Thank You!</h1>
          
          <p className="intro-description" style={{ textAlign: 'center' }}>
            Thank you for your interest in Thinkertags Enterprise. We've sent a summary of your assessment to your email, which will help prepare for your upcoming conversation with our Customer Care Manager.
          </p>
        
          
          <div style={{ 
            textAlign: 'left',
            backgroundColor: 'rgba(0, 102, 204, 0.05)',
            borderRadius: 'var(--border-radius)',
            padding: 'var(--spacing-md)',
            borderLeft: '4px solid var(--accent-color)',
            marginTop: 'var(--spacing-lg)'
          }}>
            <h3 style={{ color: 'var(--primary-color)', marginBottom: 'var(--spacing-sm)' }}>What happens next?</h3>
            <ul style={{ paddingLeft: 'var(--spacing-md)', color: 'var(--secondary-color)' }}>
              <li style={{ marginBottom: '10px' }}>Our Enterprise Solutions team will review your qualification profile</li>
              <li style={{ marginBottom: '10px' }}>You'll receive an email with demo login credentials</li>
              <li style={{ marginBottom: '10px' }}>A Customer Care Manager will contact you to schedule your personalized onboarding</li>
            </ul>
          </div>
        </div>
        
        <div className="guide-bottom-section">
          <div className="guide-actions">
            <Link to="/enterprise" className="guide-next-button">
              Return to Enterprise Page
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};