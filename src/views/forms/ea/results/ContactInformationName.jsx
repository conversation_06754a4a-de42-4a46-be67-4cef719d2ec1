import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Input } from '../../../../components/Input';
import '../../../../styles/views/forms/GuidePages.css';

export const ContactInformationName = () => {
  const navigate = useNavigate();
  const location = useLocation();

    useEffect(() => {
      // Scroll to top when component mounts
      window.scrollTo(0, 0);
    }, []);

  // Form state
  const [name, setName] = useState('');
  
  // Error states
  const [nameError, setNameError] = useState('');
  
  // Get qualification data from location state if available
  const qualificationData = location.state?.qualificationData || {};
  
  const handleNameChange = (e) => {
    setName(e.target.value);
    setNameError('');
  };
  
  const validateForm = () => {
    let isValid = true;
    
    // Validate name
    if (!name.trim()) {
      setNameError('Please enter your name');
      isValid = false;
    }
    
    return isValid;
  };
  
  const extractFirstNames = (fullName) => {
    // Split the name by spaces
    const nameParts = fullName.trim().split(' ');
    
    // If there's only one part, return it
    if (nameParts.length <= 1) {
      return fullName.trim();
    }
    
    // Otherwise, take all parts except the last one (assumed to be last name)
    return nameParts.slice(0, -1).join(' ');
  };
  
  const handleContinue = () => {
    if (validateForm()) {
      const firstName = extractFirstNames(name);
      
      navigate('/f/ea/contact-information-email', {
        state: {
          name: name, // Full name for form submission
          firstName: firstName, // First name(s) for display
          qualificationData: qualificationData
        }
      });
    }
  };
  
  return (
    <div className="enterprise-guide">
      {/* Progress bar - 75% complete for the contact information step */}
      <div className="guide-progress">
        <div className="progress-bar">
          <div className="progress-fill" style={{ width: '100%' }}></div>
        </div>
      </div>
      
      <div className="guide-branding">
        <img src="/logo.svg" alt="Thinkertags Logo" className="guide-logo" />
      </div>

      <div className="guide-content">
        <h1 className="enterprise-guide-heading">What's your name?</h1>
        
        <Input 
          name="name"
          value={name}
          onChange={handleNameChange}
          placeholder="Your name" 
          className={nameError ? 'input-v2-error' : 'input-v2'} 
        />
        {nameError && <div className="error-message">{nameError}</div>}
        
        <div className="guide-bottom-section">
          <div className="guide-actions">
            <button 
              className="guide-next-button"
              onClick={handleContinue}
            >
              Continue
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};