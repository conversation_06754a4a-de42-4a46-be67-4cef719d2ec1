import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import '../../../../styles/views/forms/GuidePages.css';

export const QualificationResults = () => {
  const [score, setScore] = useState(0);
  const [animationComplete, setAnimationComplete] = useState(false);
  const [resultsData, setResultsData] = useState({
    isQualified: false,
    score: 0,
    teamSize: 0,
    teamSizeRange: '20-49',
    assetsCount: 0,
    assetsCountRange: '1000-to-5000',
    locations: 0,
    locationsRange: '3-5',
    operationalComplexity: 0,
    primaryBenefit: '',
    secondaryBenefit: '',
    impactArea: '',
    keyFeature: '',
    potentialValue: '',
    priorities: [],
    disqualified: false,
    disqualificationReason: null
  });
  
    useEffect(() => {
      // Scroll to top when component mounts
      window.scrollTo(0, 0);
    }, []);
    
  useEffect(() => {
    // Retrieve data from localStorage
    const storedData = JSON.parse(localStorage.getItem('eaGuideData') || '{}');
    
    // Calculate score and prepare results data
    const calculatedResults = calculateResults(storedData);
    setResultsData(calculatedResults);
    
    // Animate the score counting up
    let currentScore = 0;
    const targetScore = calculatedResults.score;
    const interval = setInterval(() => {
      currentScore += 1;
      setScore(currentScore);
      
      if (currentScore >= targetScore) {
        clearInterval(interval);
        // Add a small delay before setting animation complete
        setTimeout(() => {
          setAnimationComplete(true);
        }, 500);
      }
    }, 15);
    
    return () => clearInterval(interval);
  }, []);
  
  // Helper function to format asset count ranges
  const formatAssetsRange = (range) => {
    switch (range) {
      case 'less-than-1000': return 'less than 1,000';
      case '1000-to-5000': return '1,000 to 5,000';
      case '5000-to-25000': return '5,000 to 25,000';
      case 'more-than-25000': return 'more than 25,000';
      default: return '1,000 to 5,000';
    }
  };
  
  // Function to calculate results based on user selections
  const calculateResults = (data) => {
    // Default values in case some data is missing
    const results = {
      isQualified: false,
      score: 0,
      teamSize: 0,
      teamSizeRange: '20-49',
      assetsCount: 0,
      assetsCountRange: '1000-to-5000',
      locations: 0,
      locationsRange: '3-5',
      operationalComplexity: 0,
      primaryBenefit: '',
      secondaryBenefit: '',
      impactArea: '',
      keyFeature: '',
      priorities: data.priorities || [],
      disqualified: false,
      disqualificationReason: null
    };
    
    // Calculate team size
    if (data.teamSize) {
      results.teamSizeRange = data.teamSize; // Store the original range
      switch (data.teamSize) {
        case '5-19': results.teamSize = 12; break;
        case '20-49': results.teamSize = 35; break;
        case '50-99': results.teamSize = 75; break;
        case '100-499': results.teamSize = 250; break;
        case '500-plus': results.teamSize = 500; break;
        default: 
          results.teamSize = 35;
          results.teamSizeRange = '20-49';
      }
    }
    
    // Calculate assets count
    if (data.assetsCount) {
      results.assetsCountRange = data.assetsCount; // Store the original range
      switch (data.assetsCount) {
        case 'less-than-1000': results.assetsCount = 900; break;
        case '1000-to-5000': results.assetsCount = 3000; break;
        case '5000-to-25000': results.assetsCount = 15000; break;
        case 'more-than-25000': results.assetsCount = 30000; break;
        default: 
          results.assetsCount = 3000;
          results.assetsCountRange = '1000-to-5000';
      }
    }
    
    // Calculate locations
    if (data.geographicRegions) {
      results.locationsRange = data.geographicRegions; // Store the original range
      switch (data.geographicRegions) {
        case '1-2': results.locations = 2; break;
        case '3-5': results.locations = 4; break;
        case '6-10': results.locations = 8; break;
        case '11-20': results.locations = 15; break;
        case '21-plus': results.locations = 25; break;
        default: 
          results.locations = 4;
          results.locationsRange = '3-5';
      }
    }
    
    // ----- NEW IMPACT SCORE ALGORITHM -----
    
    // 1. Operational Complexity Index
    const teamDispersionFactor = results.locations / Math.sqrt(results.teamSize);
    results.operationalComplexity = Math.round((results.assetsCount * teamDispersionFactor) / 1000);
    
    // 2. Base Score from Operational Complexity
    let baseScore = Math.min(Math.round(results.operationalComplexity * 1.5), 60);
    
    // 3. Pain Point Multipliers based on priorities
    let painPointMultiplier = 1;
    const priorities = data.priorities || [];
    
    // Determine primary and secondary benefits based on priorities and profile
    if (priorities.length > 0) {
      // Map priorities to benefits
      const benefitsMap = {
        'Technician Satisfaction': {
          benefit: 'single scan access to asset information',
          feature: 'field-optimized mobile interface'
        },
        'Service Quality': {
          benefit: 'ensuring consistent service delivery across all locations',
          feature: 'standardized protocols with location-aware intelligence'
        },
        'Performance Metrics': {
          benefit: 'transforming field data into actionable management insights',
          feature: 'real-time analytics dashboard'
        },
        'Scalability': {
          benefit: 'simplifying asset management as your operation grows',
          feature: 'bulk tagging and group-based management'
        },
        'Cost Reduction': {
          benefit: 'streamlining operations to reduce administrative overhead',
          feature: 'automated documentation and reporting'
        }
      };
      
      // Set primary benefit from first priority
      if (benefitsMap[priorities[0]]) {
        results.primaryBenefit = benefitsMap[priorities[0]].benefit;
        results.keyFeature = benefitsMap[priorities[0]].feature;
        
        // Increase multiplier if this priority aligns well with their profile
        if (priorities[0] === 'Service Quality' && results.locations > 5) {
          painPointMultiplier += 0.3;
        } else if (priorities[0] === 'Scalability' && results.assetsCount > 5000) {
          painPointMultiplier += 0.3;
        } else if (priorities[0] === 'Technician Satisfaction' && results.teamSize > 50) {
          painPointMultiplier += 0.3;
        } else if (priorities[0] === 'Performance Metrics' && results.operationalComplexity > 40) {
          painPointMultiplier += 0.3;
        }
      }
      
      // Set secondary benefit from second priority or generate based on profile
      if (priorities.length > 1 && benefitsMap[priorities[1]]) {
        results.secondaryBenefit = benefitsMap[priorities[1]].benefit;
      } else {
        // Choose secondary benefit based on operational profile
        if (results.locations > 10) {
          results.secondaryBenefit = 'providing location-specific asset visibility across your operation';
        } else if (results.assetsCount > 10000) {
          results.secondaryBenefit = 'eliminating the chaos of managing large asset inventories';
        } else if (results.teamSize > 100) {
          results.secondaryBenefit = 'enabling seamless collaboration across large technical teams';
        } else {
          results.secondaryBenefit = 'creating a unified system for improved operational control';
        }
      }
    } else {
      // Default benefits if no priorities selected
      results.primaryBenefit = 'streamlining asset management workflows';
      results.secondaryBenefit = 'reducing administrative overhead for field teams';
      results.keyFeature = 'enterprise asset management system';
    }
    
    // 4. Value Intensifiers
    let serviceMultiplier = 1;
    if (data.serviceVolume) {
      switch (data.serviceVolume) {
        case 'under-1000': serviceMultiplier = 1; break;
        case '1000-5000': serviceMultiplier = 1.2; break;
        case '5000-20000': serviceMultiplier = 1.35; break;
        case '20000-100000': serviceMultiplier = 1.5; break;
        case '100000-plus': serviceMultiplier = 1.7; break;
        default: serviceMultiplier = 1;
      }
    }
    
    // 5. Documentation Method Factor (paper forms is disqualifying)
    let documentationScore = 0;
    if (data.documentationMethod) {
      switch (data.documentationMethod) {
        case 'paper-forms': 
          // Paper forms is potentially disqualifying
          documentationScore = -50; 
          results.disqualified = true;
          results.disqualificationReason = 'Current paper-based documentation indicates your team would experience a dramatic transformation with Thinkertags, requiring a personalized adoption strategy.';
          break;
        case 'mixed-methods': documentationScore = 5; break;
        case 'mobile-apps': documentationScore = 15; break;
        case 'central-system': documentationScore = 10; break;
        default: documentationScore = 0;
      }
    }
    
    // 6. Determine impact area based on profile
    if (results.locations > 10 && results.teamSize > 100) {
      results.impactArea = 'streamlining cross-location communication';
    } else if (results.assetsCount > 10000) {
      results.impactArea = 'simplifying complex asset tracking';
    } else if (data.serviceVolume === '20000-100000' || data.serviceVolume === '100000-plus') {
      results.impactArea = 'handling high service volumes efficiently';
    } else if (priorities.includes('Performance Metrics')) {
      results.impactArea = 'transforming field data into management insights';
    } else {
      results.impactArea = 'reducing administrative friction in daily operations';
    }
    
    // 7. Determine potential value descriptor based on profile and score
    if (results.operationalComplexity > 50 || results.assetsCount > 20000) {
      results.potentialValue = 'transformative';
    } else if (results.locations > 10 || priorities.includes('Scalability')) {
      results.potentialValue = 'significant';
    } else if (results.teamSize > 100 || priorities.includes('Service Quality')) {
      results.potentialValue = 'substantial';
    } else if (results.score >= 80) {
      results.potentialValue = 'strong';
    } else {
      results.potentialValue = 'valuable';
    }
    
    // Calculate final score
    const weightedBaseScore = baseScore * serviceMultiplier * painPointMultiplier;
    let finalScore = weightedBaseScore + documentationScore;
    
    // Ensure score is between 0-100
    finalScore = Math.min(Math.max(Math.round(finalScore), 0), 100);
    
    // Set score and qualification status
    results.score = finalScore;
    
    // Qualification is based on score threshold (80+) unless disqualified
    results.isQualified = !results.disqualified && finalScore >= 80;
    
    return results;
  };

  return (
    <div className="enterprise-guide">
      <div className="guide-progress">
        <div className="progress-bar">
          <div className="progress-fill" style={{ width: '100%' }}></div>
        </div>
      </div>
            
      <div className="guide-branding">
        <img src="/logo.svg" alt="Thinkertags Logo" className="guide-logo" />
      </div>

      <div className="guide-content">

        
        <div className={`insights-container ${animationComplete ? 'delay-300' : ''}`}>
          {resultsData.disqualified ? (
            <>
              <p className="intro-description">Thank you for your interest in Thinkertags Enterprise.</p>
              
              <p className="consolidated-benefits">
                Based on your assessment, we're unable to offer Early Access at this time. {resultsData.disqualificationReason || 'Your current documentation methods would require a specialized implementation strategy that is not yet available through our Early Access program.'}
              </p>

              <p className="intro-description">
                We'd be happy to discuss alternative solutions that might be a better fit for your current operational setup.
              </p>
            </>
          ) : (
            <>
            
              <h1> We hear you</h1>

              <p className="intro-description">
                Based on your answers, you have <span className="accent-text">{resultsData.potentialValue}</span> potential to transform how your operation manages <span className="accent-text">{formatAssetsRange(resultsData.assetsCountRange)} assets</span> across <span className="accent-text">{resultsData.locationsRange} locations</span> by {resultsData.primaryBenefit} and {resultsData.secondaryBenefit}.
              </p>
              
              <p className="intro-description">
                Organizations with your complexity profile typically see the greatest transformation in <span className="accent-text">{resultsData.impactArea}</span> where Thinkertags <span className="accent-text">{resultsData.keyFeature}</span> provides immediate relief.
              </p>
            </>
          )}
        </div>
        
        <div className="guide-bottom-section">
          <div className="guide-actions">
            {resultsData.disqualified ? (
              <Link to="/enterprise" className="guide-next-button">
                Return to Enterprise Page
              </Link>
            ) : (
              <Link to="/f/ea/next-steps" className="guide-next-button">
                Continue
              </Link>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};