import React, {useState, useEffect } from 'react';
import { API } from 'aws-amplify';
import { useNavigate, useLocation } from 'react-router-dom';
import { EmailInput } from '../../../../components/Input';
import '../../../../styles/views/forms/GuidePages.css';

export const ContactInformationEmail = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [showContent, setShowContent] = useState(false);

  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);

  // Form state
  const [email, setEmail] = useState('');
  
  // Error states
  const [emailError, setEmailError] = useState('');
  const [submitError, setSubmitError] = useState('');
  
  // Loading state
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Get data from previous component
  const { name = '', firstName = '', qualificationData = {} } = location.state || {};
  
  const handleEmailChange = (e) => {
    setEmail(e.target.value);
    setEmailError('');
  };
  
  const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };
  
  const validateForm = () => {
    let isValid = true;
    
    // Validate email
    if (!email.trim()) {
      setEmailError('Please enter your email');
      isValid = false;
    } else if (!isValidEmail(email)) {
      setEmailError('Please enter a valid email');
      isValid = false;
    }
    
    return isValid;
  };
  
  const submitFormData = async () => {
    try {
      setIsSubmitting(true);
      setSubmitError('');
      
      // Get guide data from localStorage
      const eaGuideData = JSON.parse(localStorage.getItem('eaGuideData') || '{}');
      
      // Prepare form data by combining qualification data with contact info and guide data
      const formData = {
        ...qualificationData,
        ...eaGuideData,
        name,
        email,
        completedSteps: eaGuideData.completedSteps + 1 // Update step count for final submission
      };
      
      // Using regular fetch instead of Amplify API to avoid potential CORS issues
      const response = await API.post ('public-api', '/form-submit/ea-access', {
        body: formData,
      })
      // const response = await fetch('https://api.thinkertags.com/form-submit/ea-access', {
      //   method: 'POST',
      //   body: JSON.stringify(formData)
      // });
      
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      
      const data = await response.json();
      console.log('Form submission response:', data);
      
      // Update localStorage to mark the process as complete
      localStorage.setItem('eaGuideData', JSON.stringify({
        ...eaGuideData,
        completedSteps: eaGuideData.completedSteps + 1,
        completionTime: new Date().toISOString()
      }));
      
      // Navigate to confirmation page on success
      navigate('/f/ea/confirmation');
      
    } catch (error) {
      console.error('Error submitting form:', error);
      setSubmitError('Failed to submit your request. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const handleRequestAccess = () => {
    // Validate form before submitting
    if (validateForm()) {
      submitFormData();
    }
  };
  
  return (
    <div className="enterprise-guide">
      {/* Progress bar - 75% complete for the contact information step */}
      <div className="guide-progress">
        <div className="progress-bar">
          <div className="progress-fill" style={{ width: '100%' }}></div>
        </div>
      </div>
      
      <div className="guide-branding">
        <img src="/logo.svg" alt="Thinkertags Logo" className="guide-logo" />
      </div>

      <div className="guide-content">
        <h1 className="enterprise-guide-heading">What is your email,&nbsp; <span className="accent-text"> {  firstName}?</span></h1>
        
        <EmailInput
          name="email"
          value={email}
          onChange={handleEmailChange}
          placeholder="Your business email"
          className={emailError ? 'input-v2-error' : 'input-v2'}
        />
        {emailError && <div className="error-message">{emailError}</div>}
        
        {submitError && (
          <div className="error-message" style={{ marginTop: '1rem' }}>
            {submitError}
          </div>
        )}
      </div>
      
      <div className="guide-bottom-section">
        <p className="legal-text">
          Your qualification information will be reviewed by our team who will contact you within 24 hours to schedule your personalized onboarding session.
        </p>
        <div className="guide-actions">
          <button 
            className={`guide-next-button ${isSubmitting ? 'disabled' : ''}`}
            onClick={handleRequestAccess}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Submitting...' : 'Send Request Now'}
          </button>
        </div>
      </div>
    </div>
  );
};