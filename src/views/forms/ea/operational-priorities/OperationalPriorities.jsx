import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import '../../../../styles/views/forms/GuidePages.css';
import '../../../../styles/views/forms/OperationalPriorities.css';

export const OperationalPriorities = () => {
  const [selectedOptions, setSelectedOptions] = useState({
    technicianSatisfaction: false,
    serviceQuality: false,
    onboardingEfficiency: false,
    scalability: false,
    performanceMetrics: false,
    serviceGuarantees: false
  });

  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);  

  const handleOptionToggle = (option) => {
    const updatedSelections = {
      ...selectedOptions,
      [option]: !selectedOptions[option]
    };
    
    setSelectedOptions(updatedSelections);
    
    // Get existing data and update with new selection
    const existingData = JSON.parse(localStorage.getItem('eaGuideData') || '{}');
    
    // Convert selections to array of selected priorities
    const selectedPriorities = Object.keys(updatedSelections)
      .filter(key => updatedSelections[key])
      .map(key => {
        // Map keys to more readable labels
        const labelMap = {
          technicianSatisfaction: 'Technician Satisfaction',
          serviceQuality: 'Service Quality',
          onboardingEfficiency: 'Onboarding Efficiency',
          scalability: 'Scalability',
          performanceMetrics: 'Performance Metrics',
          serviceGuarantees: 'Service Guarantees'
        };
        return labelMap[key];
      });
    
    const updatedData = {
      ...existingData,
      priorities: selectedPriorities,
      completedSteps: (existingData.completedSteps || 0) + 1
    };
    
    // Store updated data
    localStorage.setItem('eaGuideData', JSON.stringify(updatedData));
  };

  const priorities = [
    { 
      id: 'technicianSatisfaction', 
      label: 'Technician Satisfaction', 
      description: 'Reduce frustration and improve retention'
    },
    { 
      id: 'serviceQuality', 
      label: 'Service Quality', 
      description: 'Consistently deliver excellent outcomes'
    },
    { 
      id: 'onboardingEfficiency', 
      label: 'Onboarding Efficiency', 
      description: 'Get new technicians productive faster'
    },
    { 
      id: 'scalability', 
      label: 'Scalability', 
      description: 'Support business growth without proportional overhead'
    },
    { 
      id: 'performanceMetrics', 
      label: 'Performance Metrics', 
      description: 'Access real-time service quality indicators'
    },
    { 
      id: 'serviceGuarantees', 
      label: 'Service Guarantees', 
      description: 'Provide verifiable service assurances to customers'
    },
  ];

  const atLeastOneSelected = Object.values(selectedOptions).some(value => value);

  return (
    <div className="enterprise-guide">
      <div className="guide-progress">
        <div className="progress-bar">
          <div className="progress-fill" style={{ width: '90%' }}></div>
        </div>
      </div>
      
      <div className="guide-branding">
        <img src="/logo.svg" alt="Thinkertags Logo" className="guide-logo" />
      </div>

      <div className="guide-content">
        <h1>What matters most to your organization?</h1>
        <p className="guide-subtitle">Select all that apply</p>

        <div className="options-container">
          {priorities.map((priority) => (
            <button
              key={priority.id}
              className={`option-button ${selectedOptions[priority.id] ? 'selected' : ''}`}
              onClick={() => handleOptionToggle(priority.id)}
            >
              <div>
                <strong>{priority.label}</strong>
                <div className="option-description">{priority.description}</div>
              </div>
            </button>
          ))}
        </div>
        <div className="guide-bottom-section">
        <div className="guide-actions">
          <Link 
            to="/f/ea/qualification-results" 
            className={`guide-next-button ${!atLeastOneSelected ? 'disabled' : ''}`}
          >
            See Your Results
          </Link>
        </div>
        </div>
      </div>
    </div>
  );
};