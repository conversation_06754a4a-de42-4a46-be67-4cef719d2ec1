import React, { useState, useEffect, useCallback } from 'react';
import { useParams } from 'react-router-dom';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import AudioPlayer from '../components/blog/AudioPlayer';
import BlogReactions from '../components/blog/BlogReactions';
import RelatedBlogPosts from '../components/blog/RelatedBlogPosts';
import MetaTags from '../components/MetaTags';
import { LoadingLogo } from '../components/LoadingLogo';
import { ensureAbsoluteUrl } from '../utils/imageUtils';
import { isDebugModeEnabled } from '../utils/debugUtils';
import {
  transformImageUrl,
  extractMetadata,
  processAuthorImagePath,
  processPostImages,
  removeMetadataFromContent,
  generateDescription,
  getPreviewImageUrl
} from '../utils/blogUtils';
import {
  createMarkdownRenderers,
  createUrlTransform
} from '../utils/markdownRenderers.jsx';
import '../styles/BlogPost.css';

export const BlogPost = () => {
  const { postId } = useParams();
  const [postContent, setPostContent] = useState('');
  const [postMeta, setPostMeta] = useState({
    title: '',
    author: '',
    date: '',
    authorImage: null,
    bannerImage: null,
    linkPreviewImage: null,
    audioFile: null,
    squareArtwork: null,
    socialMediaArtwork: null,
    imageWidth: null,
    imageHeight: null,
  });
  const [relatedPosts, setRelatedPosts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  // Check if debug_pageLoading is enabled
  const forceLoading = isDebugModeEnabled('debug_pageLoading');
  const [error, setError] = useState(null);
  const [showToast, setShowToast] = useState(false);

  // Function to copy URL to clipboard
  const copyToClipboard = useCallback(() => {
    const url = window.location.href;
    navigator.clipboard.writeText(url).then(() => {
      setShowToast(true);
      // Hide toast after 2 seconds
      setTimeout(() => {
        setShowToast(false);
      }, 2000);
    }).catch(err => {
      console.error('Failed to copy: ', err);
    });
  }, []);

  // Function to handle share
  const handleShare = useCallback(() => {
    const shareUrl = window.location.href;

    if (navigator.share) {
      navigator.share({
        title: postMeta.title,
        url: shareUrl
      }).then(() => {
        console.log('Successfully shared');
      }).catch((error) => {
        console.log('Error sharing:', error);
        // Fallback - copy to clipboard
        copyToClipboard();
      });
    } else {
      // Fallback for browsers that don't support navigator.share
      copyToClipboard();
    }
  }, [postMeta.title, copyToClipboard]);

  useEffect(() => {
    const fetchBlogPost = async () => {
      try {
        setIsLoading(true);
        const slug = postId || 'sample-post';

        // Try new structure first, fall back to old if needed
        let response = await fetch(`/blog/${slug}/${slug}.md`);

        // If new structure fails, try old structure
        if (!response.ok) {
          response = await fetch(`/blog/${slug}.md`);
          if (!response.ok) {
            console.error('Failed to fetch blog post');
            throw new Error(`Failed to fetch blog post: ${response.status}`);
          }
        }

        const markdown = await response.text();
        setPostContent(markdown);
        processMarkdown(markdown, slug);
      } catch (err) {
        console.error('Error fetching blog post:', err);
        setError(err.message);
        // Only set loading to false if we're not forcing loading for debug
        if (!forceLoading) {
          setIsLoading(false);
        }
      }
    };

    // Helper function to process markdown and extract metadata
    const processMarkdown = async (markdown, slug) => {
      // Extract metadata from markdown
      const meta = extractMetadata(markdown);

      // Check for audio file
      const audioFileName = `${slug}-audio.mp3`;
      const audioFilePath = `/blog/${slug}/${audioFileName}`;

      // We'll check if the audio file exists
      fetch(audioFilePath, { method: 'HEAD' })
        .then(response => {
          if (response.ok) {
            // Update state with audio file path if it exists
            setPostMeta(prevMeta => ({
              ...prevMeta,
              audioFile: audioFilePath
            }));
          }
        })
        .catch(err => {
          console.warn('Audio file not found:', err);
        });

      // Process author image path
      const authorImagePath = processAuthorImagePath(meta.authorImagePath);

      // Process banner image path
      const bannerImagePath = meta.bannerImagePath
        ? transformImageUrl(meta.bannerImagePath, slug)
        : null;

      // Process link preview image path
      const linkPreviewImagePath = meta.linkPreviewImagePath
        ? transformImageUrl(meta.linkPreviewImagePath, slug)
        : null;

      // Set initial metadata
      setPostMeta({
        title: meta.title,
        author: meta.author,
        date: meta.date,
        authorImage: authorImagePath,
        bannerImage: bannerImagePath,
        linkPreviewImage: linkPreviewImagePath,
        audioFile: null, // This will be updated by the fetch call if the audio file exists
        squareArtwork: null, // Will be set after processing the image
        socialMediaArtwork: null, // Will be set after processing the image
      });

      // Extract related posts if they exist
      const relatedPostsRegex = /## Related Articles\s+([\s\S]+?)(?=\s*##|$)/;
      const relatedPostsMatch = markdown.match(relatedPostsRegex);

      if (relatedPostsMatch && relatedPostsMatch[1]) {
        // Extract URLs from markdown links using regex
        const linkRegex = /\[(.*?)\]\((.*?)\)/g;
        const relatedPostsSection = relatedPostsMatch[1];
        const relatedPostUrls = [];
        const relatedPostTitles = [];
        let match;

        while ((match = linkRegex.exec(relatedPostsSection)) !== null) {
          relatedPostTitles.push(match[1]);
          relatedPostUrls.push(match[2]);
        }

        setRelatedPosts(relatedPostUrls.map((url, index) => ({
          url,
          title: relatedPostTitles[index]
        })));
      }

      // Process images for dimensions and square artwork
      try {
        const imageResults = await processPostImages(bannerImagePath, authorImagePath);

        // Update metadata with dimensions and artwork if available
        if (imageResults.dimensions || imageResults.squareArtwork || imageResults.socialMediaArtwork) {
          setPostMeta(prevMeta => ({
            ...prevMeta,
            imageWidth: imageResults.dimensions?.width,
            imageHeight: imageResults.dimensions?.height,
            squareArtwork: imageResults.squareArtwork,
            socialMediaArtwork: imageResults.socialMediaArtwork
          }));
        }
      } catch (error) {
        console.error('Error processing images:', error);
      }

      // Only set loading to false if we're not forcing loading for debug
      if (!forceLoading) {
        setIsLoading(false);
      }
    };

    fetchBlogPost();
  }, [postId, forceLoading]);

  // Create custom renderers for markdown components
  const renderers = createMarkdownRenderers(postId);

  if (isLoading || forceLoading) {
    return (
      <div className="blog">
        <LoadingLogo />
      </div>
    );
  }

  if (error) {
    return (
      <div className="blog">
        <div className="blog-post-container">
          <div className="blog-post-error">
            <h2>Error Loading Blog Post</h2>
            <p>{error}</p>
          </div>
        </div>
      </div>
    );
  }

  // Remove the metadata and related articles section from the content before rendering
  let contentWithoutMeta = removeMetadataFromContent(postContent);

  // Remove the Related Articles section if it exists
  const relatedArticlesRegex = /## Related Articles[\s\S]*?(?=\s*##|$)/;
  contentWithoutMeta = contentWithoutMeta.replace(relatedArticlesRegex, '');

  // Remove the References section if it exists
  const referencesRegex = /## References[\s\S]*?(?=\s*##|$)/;
  contentWithoutMeta = contentWithoutMeta.replace(referencesRegex, '');

  // Get the current URL for canonical link
  const currentUrl = window.location.href;

  // Get preview image URL for meta tags
  const previewImageUrl = getPreviewImageUrl(postMeta);

  return (
    <div className="blog">
      {/* Dynamic meta tags for link previews */}
      <MetaTags
        title={postMeta.title}
        description={generateDescription(contentWithoutMeta)}
        imageUrl={previewImageUrl}
        imageWidth={postMeta.imageWidth}
        imageHeight={postMeta.imageHeight}
        url={currentUrl}
        type="article"
        author={postMeta.author}
        siteName="Thinkertags Research and Insights"
        datePublished={postMeta.date ? new Date(postMeta.date).toISOString() : undefined}
        dateModified={postMeta.date ? new Date(postMeta.date).toISOString() : undefined}
      />
      <div className={`copy-toast ${showToast ? 'visible' : ''}`}>Link copied to clipboard</div>
      <div className="blog-post-container">
        {postMeta.bannerImage && (
          <div className="blog-post-banner">
            <img
              src={postMeta.bannerImage}
              alt="Blog post banner"
              className="blog-post-banner-image"
            />
          </div>
        )}

        <div className="blog-post-header">
          <h1 className="blog-post-title">{postMeta.title}</h1>

          <div className="blog-post-meta">
            <div className="blog-post-meta-left">
              <div className="blog-post-author-image-container">
                {postMeta.authorImage ? (
                  <img
                    src={postMeta.authorImage}
                    alt={`${postMeta.author}`}
                    className="blog-post-author-image"
                    onError={(e) => {
                      // If image fails to load, replace with initial
                      e.target.style.display = 'none';
                      e.target.parentNode.innerHTML = `
                        <div
                          class="blog-post-author-image"
                          style="
                            background-color: #0066cc;
                            color: white;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 1.2rem;
                            font-weight: bold
                          "
                        >
                          ${postMeta.author.charAt(0).toUpperCase()}
                        </div>
                      `;
                    }}
                  />
                ) : (
                  <div
                    className="blog-post-author-image"
                    style={{
                      backgroundColor: '#0066cc',
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '1.2rem',
                      fontWeight: 'bold'
                    }}
                  >
                    {postMeta.author.charAt(0).toUpperCase()}
                  </div>
                )}
              </div>
              <div className="blog-post-author-info">
                <span className="blog-post-author-name">{postMeta.author}</span>
                <span className="blog-post-date">Last updated: {postMeta.date}</span>
              </div>
            </div>

            <div className="blog-post-meta-buttons">
              <button
                className="blog-post-action-button"
                onClick={handleShare}
                aria-label="Share Article"
                title="Share Article"
              >
                <img src="/share.svg" alt="Share" />
              </button>
              <button
                className="blog-post-action-button"
                onClick={copyToClipboard}
                aria-label="Copy Link"
                title="Copy Link"
              >
                <img src="/link.svg" alt="Copy Link" />
              </button>
            </div>
          </div>

          {postMeta.audioFile && (
            <div className="blog-post-audio-player">
              <AudioPlayer
                audioSrc={postMeta.audioFile}
                title={postMeta.title}
                author="Thinkertags Research and Insights"
                artworkSrc={postMeta.squareArtwork || ensureAbsoluteUrl(postMeta.bannerImage || postMeta.authorImage || '/logo192.png')}
                skeleton_debug={isDebugModeEnabled('debug_skeleton')}
              />
            </div>
          )}
        </div>

        <div className="blog-post-content">
          {/* Render markdown content */}
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={renderers}
            urlTransform={createUrlTransform(postId)}
          >
            {contentWithoutMeta}
          </ReactMarkdown>

          {/* Blog post reactions component */}
          <BlogReactions
            postId={postId}
            articleTitle={postMeta.title}
            onReactionClick={(reactionData) => {
              // This will be used for API integration in the future
              console.log('Reaction data:', reactionData);
              // Future implementation: API.post('/blog/reactions', reactionData);
            }}
          />
        </div>

        {/* Related blog posts component - moved outside blog-post-content */}
        {relatedPosts.length > 0 && (
          <div className="blog-related-posts-container">
            <RelatedBlogPosts
              currentPostId={postId}
              relatedPosts={relatedPosts}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default BlogPost;
