import React, { useState } from 'react';
import { Auth } from 'aws-amplify';
import '../styles/views/ForgotPassword.css';
import FeedbackMessage from '../components/user/FeedbackMessage';

const ForgotPassword = () =>{
  const [email, setEmail] = useState('');
  const [code, setCode] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [stage, setStage] = useState('request');
  const [loading, setLoading] = useState(false);
  const [dialogMessage, setDialogMessage] = useState('');

  const handleRequestReset = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      await Auth.forgotPassword(email.trim());
      setStage('confirm');
      setDialogMessage('A verification code has been sent to your email.');
    } catch (err) {
      setDialogMessage(err.message, true);
    } finally {
      setLoading(false);
    }
  };

  const handleConfirmReset = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      await Auth.forgotPasswordSubmit(email.trim(), code.trim(), newPassword);
      setDialogMessage('Password reset successfully! You can now log in.');
      setStage('request');
      setEmail('');
      setCode('');
      setNewPassword('');
    } catch (err) {
      setDialogMessage(err.message, true);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="forgot-password-container">
      <div className="forgot-password-panel">
        <h2 className="forgot-password-title">
          {stage === 'request' ? 'Reset Your Password' : 'Confirm Reset'}
        </h2>
        <FeedbackMessage message={dialogMessage} />
        {stage === 'request' ? (
          <form onSubmit={handleRequestReset} className="forgot-password-form">
            <div className="form-group">
              <label htmlFor="email">Email Address</label>
              <input
                className="forgot-password-input"
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                autoComplete="email"
              />
              <button className="forgot-password-button" type="submit" disabled={loading}>
                {loading ? 'Sending...' : 'Request Reset Code'}
              </button>
            </div>
          </form>
        ) : (
          <form onSubmit={handleConfirmReset} className="forgot-password-form">
            <div className="form-group">
              <label htmlFor="code">Verification Code</label>
              <input
                className="forgot-password-input"
                id="code"
                type="text"
                value={code}
                onChange={(e) => setCode(e.target.value)}
                required
                autoComplete="one-time-code"
              />
            </div>
            <div className="form-group">
              <label htmlFor="newPassword">New Password</label>
              <input
                className="forgot-password-input"
                id="newPassword"
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                required
                autoComplete="new-password"
                minLength={8}
              />
            </div>
            <button className="forgot-password-button" type="submit" disabled={loading}>
              {loading ? 'Resetting...' : 'Reset Password'}
            </button>
          </form>
        )}
      </div>
    </div>
  );
};

export { ForgotPassword };