import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { API, Auth } from 'aws-amplify';
import '../styles/views/AssetDocumentView.css';

// Custom header name - must match the backend
const AUTH_TOKEN_HEADER = 'X-Auth-Token';

export function AssetDocumentView() {
  const { assetId, documentId } = useParams();
  const [documentData, setDocumentData] = useState(null);
  const [documentUrl, setDocumentUrl] = useState(null);
  const [fileType, setFileType] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [shareUrl, setShareUrl] = useState('');
  const [authToken, setAuthToken] = useState(null);

  // Define supported image types
  const IMAGE_TYPES = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
  const PDF_TYPES = ['pdf'];

  // Get file extension from filename
  const getFileExtension = (fileName) => {
    if (!fileName) return '';
    return fileName.split('.').pop().toLowerCase();
  };

  const isIOS = () => {
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;
    return /iPad|iPhone|iPod/.test(userAgent) && !window.MSStream;
  };
  
  // Add this useEffect to handle iOS PDF opening
  useEffect(() => {
    if (documentUrl && PDF_TYPES.includes(fileType) && isIOS()) {
      window.open(documentUrl, '_blank');
    }
  }, [documentUrl, fileType]);  

  const safelyUpdateIdToken = async () => {
    try {
      // This will throw an error if not authenticated
      const session = await Auth.currentSession();
      const idToken = session.getIdToken().getJwtToken();
      
      // Only update if we have a valid token
      if (idToken) {
        // Store the token in state for use in request headers
        setAuthToken(idToken);
        console.log('Token updated successfully');
      }
      return true; // User is authenticated
    } catch (err) {
      // User is not authenticated - this is not an error case
      console.log('User not authenticated, proceeding as guest');
      console.error('Error updating token:', err);
      return false; // User is not authenticated
    }
  };  

  useEffect(() => {
    const fetchDocument = async () => {
      try {
        console.log('Fetching document:', assetId, documentId);

        // Ensure the token is updated before making the API call
        await safelyUpdateIdToken(); 
        
        // Get the current session to extract the ID token
        let requestHeaders = {};
        
        try {
          const session = await Auth.currentSession();
          const idToken = session.getIdToken().getJwtToken();
          
          // Add the custom auth header
          requestHeaders[AUTH_TOKEN_HEADER] = idToken;
        } catch (err) {
          console.log('No authenticated session, proceeding as guest');
        }
        
        // Fetch document metadata from API with token in header only
        const response = await API.get('public-api', `/assets/${assetId}/documents/${documentId}`, {
          // No longer using cookies, so withCredentials is not needed
          // Add our custom header
          headers: requestHeaders
        });
        
        // console.log('API response:', response);
        
        // Set the share URL
        setShareUrl(window.location.href);
        
        // Handle the new backend response format (200 OK with URL and FileName)
        if (response && response.URL && response.FileName) {
          console.log('Document has URL and FileName:', response.URL, response.FileName);
          
          // Use the filename directly from the backend
          const fileName = response.FileName;
          
          // Determine file type from name
          const extension = getFileExtension(fileName);
          console.log('Detected file extension:', extension);
          setFileType(extension);
          
          // Set document URL from response
          setDocumentUrl(response.URL);
          
          // Create document object
          setDocumentData({
            name: fileName,
            createdAt: new Date().toISOString(),
            url: response.URL
          });
        } 
        // Fallback for legacy response formats - should be removed once backend migration is complete
        else {
          console.error('Unexpected response format from updated backend:', response);
          setError('Invalid response format from server');
        }
      } catch (err) {
        console.error('Error fetching document:', err);
        setError(`Failed to load document: ${err.message || 'Unknown error'}`);
      } finally {
        setLoading(false);
      }
    };

    fetchDocument();
    
    // Cleanup function to revoke object URLs when component unmounts
    return () => {
      if (documentUrl && documentUrl.startsWith('blob:')) {
        URL.revokeObjectURL(documentUrl);
      }
    };
  }, [assetId, documentId]);

  if (loading) {
    return (
      <div className="asset-document-view loading">
        <div className="loading-spinner"></div>
        <p>Loading document...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="asset-document-view error">
        <div className="error-icon">!</div>
        <h2>Error Loading Document</h2>
        <p>{error}</p>
        {documentUrl && (
          <a href={documentUrl} download={documentData?.name} className="download-button">
            Download Document
          </a>
        )}
        <button onClick={() => window.history.back()} className="back-button">
          Back to Asset
        </button>
      </div>
    );
  }

  if (!documentData) {
    return (
      <div className="asset-document-view not-found">
        <h2>Document Not Found</h2>
        <p>The requested document could not be found.</p>
        <button onClick={() => window.history.back()} className="back-button">
          Back to Asset
        </button>
      </div>
    );
  }

  // Function to handle share
  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: documentData.name,
        url: shareUrl
      }).then(() => {
        console.log('Successfully shared');
      }).catch((error) => {
        console.log('Error sharing:', error);
        // Fallback - copy to clipboard
        navigator.clipboard.writeText(shareUrl).then(() => {
          alert('Link copied to clipboard');
        });
      });
    } else {
      // Fallback for browsers that don't support navigator.share
      navigator.clipboard.writeText(shareUrl).then(() => {
        alert('Link copied to clipboard');
      });
    }
  };
  
  // Function to handle download
  const handleDownload = () => {
    // Create a temporary anchor element
    const a = document.createElement('a');
    a.href = documentUrl;
    a.download = documentData.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  return (
    <div className="asset-document-view">
      <div className="document-header">
        <div className="document-header-info">
          <h1>{documentData.name}</h1>
        </div>
      </div>
      
      <div className="document-viewer-container">
        {documentUrl ? (
          <div className="file-viewer-wrapper">
            {/* Simple Image Viewer */}
            {IMAGE_TYPES.includes(fileType) ? (
              <div className="image-viewer">
                <img 
                  src={documentUrl} 
                  alt={documentData.name}
                  onError={(e) => {
                    console.error('Image failed to load');
                    e.target.style.display = 'none';
                    setError('Image failed to load. You can download the file instead.');
                  }}
                />
              </div>
            ) : PDF_TYPES.includes(fileType) ? (
              isIOS() ? (
                // For iOS devices, show a message that PDF is opening in a new tab
                <div className="ios-pdf-viewer">
                  <p>PDF is opening in a new tab...</p>
                  <button onClick={() => window.open(documentUrl, '_blank')} className="action-button">
                    Open PDF Again
                  </button>
                </div>
              ) : (
                // For non-iOS devices, use iframe as before
                <iframe 
                  src={documentUrl} 
                  title={documentData.name}
                  width="100%" 
                  height="100%" 
                  style={{border: 'none'}}
                />
              )
            ) : (
              // Default for other file types - just offer download
              <div className="unsupported-format">
                <p>Preview not available for {fileType.toUpperCase()} files</p>
                <p>Please download the file to view its contents</p>
                <button onClick={handleDownload} className="download-button">
                  Download {documentData.name}
                </button>
              </div>
            )}
            
            {/* Action buttons overlay */}
            <div className="document-actions-overlay">
              <div className="document-actions-container">
                <button onClick={handleShare} className="action-button share-button">
                  Share
                </button>
                {documentUrl && (
                  <button onClick={handleDownload} className="action-button download-button">
                    Download
                  </button>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="document-error">
            <p>Unable to display document content</p>
            <p>Document URL is missing</p>
          </div>
        )}
      </div>
    </div>
  );
}