import React, { useState, useEffect } from 'react';
import { Auth, API } from "aws-amplify";
import { Link } from 'react-router-dom';
import * as XLSX from 'xlsx';

import '../styles/AssetWorkbench.css';
import { ExampleTemplates } from '../components/assetsWorkbench/ExampleTemplates.jsx';
import UploadedFileDetails from '../components/assetsWorkbench/UploadedFileDetails';
import { initAssetIdsDB, loadExampleData } from '../utils/assetUtils';

const AssetWorkbench = () => {
    const [uploadedFile, setUploadedFile] = useState(null);
    const [exampleTemplateData, setExampleTemplateData] = useState([['', '', '', '', '', '', '']]);
    const [validationStatus, setValidationStatus] = useState(null);
    const [validatedRows, setValidatedRows] = useState([]);
    const [acknowledgeUpdate, setAcknowledgeUpdate] = useState(false);
    const [isLoadingTemplate, setIsLoadingTemplate] = useState(false);
    const [isLoadingIds, setIsLoadingIds] = useState(false);
    const [activeMode, setActiveMode] = useState('new'); // 'edit', 'import', or 'new'
    const [showSourceSelector, setShowSourceSelector] = useState(false);

    // Progress banner state
    const [showProgressBanner, setShowProgressBanner] = useState(false);
    const [progressStatus, setProgressStatus] = useState('progress'); // 'progress' or 'success'
    const [progressPercentage, setProgressPercentage] = useState(0);

    // Fetch and store asset IDs when component mounts
    useEffect(() => {
        const fetchAndStoreAssetIds = async () => {
            setIsLoadingIds(true);
            try {
                // 1. Get the session token
                const session = await Auth.currentSession();
                const headers = {
                    Authorization: `Bearer ${session.getAccessToken().getJwtToken()}`,
                    'id-Token': session.getIdToken().getJwtToken(),
                };

                // 2. Fetch asset IDs
                const response = await API.get('api', '/asset-ids', { headers });

                // 3. Store in IndexedDB
                if (response && response.ids) {
                    const db = await initAssetIdsDB();
                    const tx = db.transaction('assetIds', 'readwrite');
                    const store = tx.objectStore('assetIds');

                    // Clear existing entries and add new ones
                    await store.clear();

                    // Strip UUID prefix (format: "uuid-assetId") and store just the asset ID
                    for (const fullId of response.ids) {
                        // Find the position after the UUID (36 chars) and hyphen
                        const matches = fullId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}-(.*)/);
                        if (matches && matches[1]) {
                            const assetId = matches[1];
                            await store.add({ id: assetId });
                        } else {
                            // If pattern doesn't match, store the original ID as fallback
                            await store.add({ id: fullId });
                        }
                    }

                    await tx.done;
                    console.log(`Stored ${response.ids.length} asset IDs in IndexedDB`);
                }
            } catch (error) {
                console.error('Error fetching or storing asset IDs:', error);
            } finally {
                setIsLoadingIds(false);
            }
        };

        fetchAndStoreAssetIds();
    }, []); // Empty dependency array means this runs once on mount

    // Handle closing the progress banner
    const handleCloseBanner = () => {
        setShowProgressBanner(false);
        setProgressPercentage(0);
        setProgressStatus('progress');
    };

    // Simulate progress updates
    const simulateProgress = () => {
        let progress = 0;
        const interval = setInterval(() => {
            progress += 10;
            setProgressPercentage(progress > 90 ? 90 : progress); // Cap at 90% until complete
            if (progress >= 90) {
                clearInterval(interval);
            }
        }, 300);
        return interval;
    };

    const PostBlueprints = async () => {
        // First, validate all rows including empty ones
        if (uploadedFile || exampleTemplateData.length > 0) {
            // Get a reference to the UploadedFileDetails component to validate
            const validateAllRows = () => {
                // Filter out completely empty rows
                const nonEmptyRows = exampleTemplateData.filter(row =>
                    row.some(cell => cell !== '')
                );

                if (nonEmptyRows.length === 0) {
                    alert('Please add at least one row with data before saving.');
                    return false;
                }

                // Check if any row is missing an ID
                const missingIds = nonEmptyRows.some(row => !row[0]);
                if (missingIds) {
                    alert('All rows must have an Asset ID. Please fill in all required fields.');
                    return false;
                }

                return true;
            };

            if (!validateAllRows()) {
                return;
            }
        }

        // Show progress banner and start progress animation
        setShowProgressBanner(true);
        setProgressStatus('progress');
        setProgressPercentage(10);

        // Simulate gradual progress updates
        const progressInterval = simulateProgress();

        const session = await Auth.currentSession();
        try {
            // Filter out completely empty rows from validatedRows
            const nonEmptyValidatedRows = validatedRows.filter(row =>
                row.assetId || row.name || row.publicAction || row.privateAction ||
                row.address || row.latitude || row.longitude
            );

            // Auto-fill empty Public URL fields and provide default names for empty name fields
            const processedRows = nonEmptyValidatedRows.map(row => {
                // Create a copy to modify
                let updatedRow = { ...row };
                
                // If publicAction (Public URL) is empty but assetId exists, auto-fill it
                if (!updatedRow.publicAction && updatedRow.assetId) {
                    updatedRow.publicAction = `https://thinkertags.com`;
                }
                
                // If name is empty but assetId exists, set a default name using the assetId
                if ((!updatedRow.name || updatedRow.name === '') && updatedRow.assetId) {
                    updatedRow.name = `Asset ${updatedRow.assetId}`;
                }
                
                return updatedRow;
            });

            const response = await API.post('api', `/assets`, {
                headers: {
                    Authorization: `Bearer ${session.getAccessToken().getJwtToken()}`,
                    'id-Token': session.getIdToken().getJwtToken()
                },
                body: {
                    blueprints: processedRows
                }
            });

            if (response) {
                console.log(response);
                // Refresh the asset IDs cache after posting new assets
                const db = await initAssetIdsDB();
                const tx = db.transaction('assetIds', 'readwrite');
                const store = tx.objectStore('assetIds');

                // Add newly created assets to the store
                for (const row of validatedRows) {
                    if (row.assetId) {
                        // When adding new assets, we're already using the correct ID format
                        // (without UUID prefix) because they come from the spreadsheet
                        await store.put({ id: row.assetId });
                    }
                }

                await tx.done;

                // Stop progress simulation and show success
                clearInterval(progressInterval);
                setProgressPercentage(100);
                setProgressStatus('success');

                // Auto-hide banner after 5 seconds
                setTimeout(() => {
                    handleCloseBanner();
                }, 5000);
            } else {
                console.log('Failed to upload blueprints');
                // Stop progress simulation and show error
                clearInterval(progressInterval);
                setProgressStatus('error');

                // Keep the error banner visible until manually closed
            }
        } catch (error) {
            console.error('Error uploading blueprints:', error);
            // Stop progress simulation and show error
            clearInterval(progressInterval);
            setProgressStatus('error');
        }
    };

    const onDrop = (acceptedFiles) => {
        console.log(acceptedFiles);
        const file = acceptedFiles[0];
        if (!file) return;

        const validExtensions = ['.xlsx', '.xls', '.csv'];

        if (file.size > 3 * 1024 * 1024) {
            alert('File size exceeds the 3MB limit.');
            return;
        }

        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        if (!validExtensions.includes(fileExtension)) {
            alert(`Skipped '${file.name}' because an invalid file extension was provided.`);
            return;
        }

        setExampleTemplateData([]);
        setUploadedFile(file);
        setValidationStatus(null);
        setActiveMode('import'); // Set mode to import for file uploads
    };

    const handleTryExample = async (templateId, preloadedData = null) => {
        setIsLoadingTemplate(true);
        try {
          // If we have preloaded data (from existing assets), use it directly
          if (preloadedData) {
            setExampleTemplateData(preloadedData);
            setUploadedFile(null);
            setValidationStatus(null);
            setActiveMode('edit'); // Set mode to edit for existing assets
          } else {
            // Otherwise load from template file
            const templateData = await loadExampleData(templateId);
            if (templateData && templateData.length > 0) {
              setExampleTemplateData(templateData);
              setUploadedFile(null); // Clear the actual file since we're using example data
              setValidationStatus(null);
              setActiveMode('import'); // Set mode to import for new assets
            } else {
              console.error(`Failed to load template: ${templateId}`);
            }
          }
        } catch (error) {
          console.error(`Error loading template ${templateId}:`, error);
        } finally {
          setIsLoadingTemplate(false);
        }
      };

    const handleClearFile = () => {
        setUploadedFile(null);
        setExampleTemplateData([['', '', '', '', '', '', '']]);
        setValidationStatus(null);
        setValidatedRows([]);
        setActiveMode('new'); // Reset to new mode
        setShowSourceSelector(true); // Show source selector
    };

    return (
        <div className='asset-workbench'>
            {/* Progress Banner */}
            {showProgressBanner && (
                <div className={`asset-workbench-progress-banner ${progressStatus}`}>
                    <div className="asset-workbench-progress-content">
                        <div className="asset-workbench-progress-message">
                            {progressStatus === 'progress' && 'Saving asset changes...'}
                            {progressStatus === 'success' && 'Assets saved successfully!'}
                            {progressStatus === 'error' && 'Error saving assets. Please try again.'}
                        </div>
                        {progressStatus === 'progress' && (
                            <div className="asset-workbench-progress-bar-container">
                                <div
                                    className="asset-workbench-progress-bar"
                                    style={{ width: `${progressPercentage}%` }}
                                ></div>
                            </div>
                        )}
                        {progressStatus === 'success' && (
                            <div className="asset-workbench-progress-icon success">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                                </svg>
                            </div>
                        )}
                        {progressStatus === 'error' && (
                            <div className="asset-workbench-progress-icon error">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <line x1="15" y1="9" x2="9" y2="15"></line>
                                    <line x1="9" y1="9" x2="15" y2="15"></line>
                                </svg>
                            </div>
                        )}
                    </div>
                    <button className="asset-workbench-progress-close" onClick={handleCloseBanner}>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                    </button>
                </div>
            )}

            <div className="asset-workbench-header-container">
                <Link to="/tags" className="back-arrow">
                    <img
                        src="/arrow_back.svg"
                        alt="Arrow Back"
                        className="backarrow"
                    />
                    {' '}Tags
                </Link>
            </div>
            <div className='asset-workbench-panel'>
                <div className='asset-workbench-panel-body'>
                    <div className='asset-workbench-header-block-group'>
                        <div className='asset-workbench-header-content'>
                            <h1>Asset Workbench</h1>
                            <div className='asset-workbench-toggle-title-body'>
                                Manage your Thinkertag assets efficiently. Edit, import, and export in bulk.
                            </div>
                        </div>
                    </div>

                    <div className='asset-workbench-split-line'></div>

                    {/* Asset Sources Section */}
                    {showSourceSelector && (
                        <ExampleTemplates
                            onTryExample={(templateId, preloadedData) => {
                                handleTryExample(templateId, preloadedData);
                                setShowSourceSelector(false);
                            }}
                            onFileUpload={(files) => {
                                onDrop(files);
                                setShowSourceSelector(false);
                            }}
                        />
                    )}

                    {/* Loading indicator */}
                    {isLoadingTemplate && (
                        <div className="asset-workbench-loading-template">
                            <p>Loading assets...</p>
                        </div>
                    )}

                    {/* Data Editor Section */}
                    {!isLoadingTemplate && !showSourceSelector && (
                        <div className="asset-workbench-editor-section">
                            <div className="asset-workbench-editor-header">
                                <h2>
                                    {activeMode === 'edit' ? 'Edit Existing Assets' :
                                     activeMode === 'import' ? 'Import New Assets' : 'Create New Assets'}
                                </h2>
                                <div className="asset-workbench-editor-actions">
                                    <button
                                        className="asset-workbench-source-button"
                                        onClick={() => setShowSourceSelector(true)}
                                    >
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="source-icon">
                                            <path d="M7 16l-4-4 4-4"></path>
                                            <path d="M17 8l4 4-4 4"></path>
                                            <path d="M3 12h18"></path>
                                        </svg>
                                        Change Source
                                    </button>
                                </div>
                            </div>
                            <UploadedFileDetails
                                file={uploadedFile}
                                setUploadedFile={handleClearFile}
                                setValidatedRows={setValidatedRows}
                                setValidationStatus={setValidationStatus}
                                initialData={exampleTemplateData}
                                allowAddRows={true}
                            />
                        </div>
                    )}
                </div>
            </div>
            <div className="asset-workbench-fixed-footer">
                <div className="asset-workbench-fixed-footer-content">
                    <div className="asset-workbench-fixed-footer-file">
                        {(uploadedFile || exampleTemplateData.length > 0) && (
                            <>
                                <label className="asset-workbench-update-acknowledge-label">
                                    <input
                                        type="checkbox"
                                        checked={acknowledgeUpdate}
                                        onChange={(e) => setAcknowledgeUpdate(e.target.checked)}
                                    />
                                    <span>
                                        {activeMode === 'edit'
                                            ? 'I acknowledge that these changes will update my assets with immediate effect'
                                            : 'I acknowledge that existing assets with the same ID will be updated with immediate effect'}
                                    </span>
                                </label>
                            </>
                        )}
                    </div>
                    <button
                        className="asset-workbench-fixed-footer-button"
                        onClick={PostBlueprints}
                        disabled={validationStatus !== 'complete' || !acknowledgeUpdate}
                    >
                        {activeMode === 'edit' ? 'Save Changes' : 'Save Assets'}
                    </button>
                </div>
            </div>
        </div>
    );
};

export { AssetWorkbench };