import { useState } from "react";
import React from 'react';
import { useCookies } from 'react-cookie';
import { Auth, API } from "aws-amplify";
import { useNavigate } from 'react-router-dom';
import '../styles/Login.css';
import ImageCarousel from "../components/ImageCarousel";
// import Indicator from "../components/Indicator";
import images from "../components/imageData";

export default function Login({ setScreen, setUser }) {
  const [cookies, setCookie] = useCookies(['idToken']);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [currentIndex, setCurrentIndex] = useState(0);
  const navigate = useNavigate(); 

  const handleSubmit = async (event) => {
    event.preventDefault();
    await handleLogin();
  };

  const handleLogin = async () => {
    try {
      
      // Step 1: Authenticate user
      const authUser = await Auth.signIn(email, password);
      const session = await Auth.currentSession();
      const userInfo = await Auth.currentUserInfo();

      if (session) {
        const idToken = session.getIdToken().getJwtToken();
        const accessToken = session.getAccessToken().getJwtToken();
  
        // Set cookie for auth
        setCookie('idToken', idToken, {
          path: '/',
          domain: '.thinkertags.com',
          sameSite: 'none',
          secure: true,
          maxAge: 24 * 60 * 60, // Expires in 1 day
        });
        
        // Step 2: Fetch user role and assets via API before navigating
        const username = authUser.username;
        const userData = await API.get('api', `/users/${encodeURIComponent(username)}`, {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'id-Token': idToken,
          }
        });
        
        // Step 3: Create complete user object with role and assets
        const completeUserInfo = {
          ...userInfo,
          role: userData?.role || '',
          assets: userData?.assets || []
        };
        
        // Step 4: Set user state with complete data
        setUser(completeUserInfo);
        
        // Step 5: Navigate only after we have complete user data
        navigate('/tags');
      } else {
        console.error('No session found');
      }
    } catch (err) {
      console.log(err);
    }
  };

  return (
    <div className="login-container">
      <ImageCarousel images={images} onChange={setCurrentIndex}/>
      {/* <Indicator currentIndex={currentIndex} count={images.length} />     */}
      <div className="login-panel">
        <div className="login-group">
          <div className="welcome-back">Welcome back!</div>
          <form className="form" onSubmit={handleSubmit}>
          <label htmlFor="email">Email Address</label>
          <input
            className="login-input"
            name="username"
            id="email"
            type="email"
            placeholder=""
            autoComplete="username"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />

          <label htmlFor="password">Password</label>
          <input
            className="login-input"
            name="password"
            id="password"
            type="password"
            placeholder=""
            autoComplete="current-password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
          />
          
          <button className="login-button" type="submit">Login</button>
          <div className="forgot-password-link-container">
            <a href="/forgot-password" className="forgot-password-link">
              Forgot password?
            </a>
          </div>
        </form>
        </div>
      </div>
    </div>
  );
}