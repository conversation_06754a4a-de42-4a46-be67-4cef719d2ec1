import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import Skeleton from 'react-loading-skeleton';

import 'react-loading-skeleton/dist/skeleton.css';

import '../styles/TagPage.css';
import '../styles/QRPreviewPanel.css';

import { ToggleSwitch } from '../components/ToggleSwitch';
import { ButtonDiv } from '../components/tag/ButtonDiv';
import { MapComponent } from '../components/MapComponent';
import { Auth, API } from 'aws-amplify';
import { TagActivity } from '../components/TagActivity';
import RedirectAuth from '../components/tag/RedirectAuth';
import ConfirmationDialog from '../components/ConfirmationDialog';
import { ConnectToAsset } from '../components/tag/ConnectToAsset';
import { ThinkertagsPreviewPanel } from '../components/tag/ThinkertagsPreviewPanel';

/* ----------------------------------
 * Top-Level Constants & Utilities
 * ---------------------------------- */
const PROTOCOLS = [
  'https://', 'http://', 'ws://', 'wss://',
  'ftp://', 'sftp://', 'ftps://',
  'file://', 'smb://', 'nfs://',
  'mailto:', 'tel:', 'sms:', 'facetime:', 'callto:', 'sip:',
  'rtsp://', 'rtmp://', 'spotify:',
  'git://', 'svn://',
  'maps:', 'geo:', 'waze://',
  'market://', 'itms://', 'itms-apps://',
  'steam://', 'discord://', 'slack://', 'teams://', 'zoommtg://', 'meet:', 'skype:', 'whatsapp://', 'telegram://', 'signal://',
  'ldap://', 'jdbc:', 'odbc:',
  'bitcoin:', 'ethereum:',
  'fmp://', 'fmp12://',
];

function ensureProtocol(url) {
  if (!url) return url;
  const trimmedUrl = url.trim();
  if (!trimmedUrl) return trimmedUrl;

  const hasProtocol = PROTOCOLS.some(protocol =>
    trimmedUrl.toLowerCase().startsWith(protocol)
  );
  if (hasProtocol) {
    return PROTOCOLS.reduce((acc, protocol) => {
      const regex = new RegExp(`${protocol}.*?${protocol}`, 'i');
      return acc.replace(regex, protocol);
    }, trimmedUrl);
  }
  return `https://${trimmedUrl}`;
}

/* ------------------------------------------------------------------
 * Centralized Error Handling Utility
 * ------------------------------------------------------------------ */
function createErrorHandler(setErrorMsg) {
  return function handleError(error, userMessage = 'An error occurred. Please try again.') {
    console.error(userMessage, error);
    setErrorMsg(userMessage);
  };
}

/* ----------------------------------
 * Custom Hook: useSessionTokens
 * ---------------------------------- */
function useSessionTokens() {
  const [tokens, setTokens] = useState(null);
  const [isSessionLoading, setIsSessionLoading] = useState(true);
  const [sessionError, setSessionError] = useState(null);

  useEffect(() => {
    let ignore = false;
    (async () => {
      try {
        const session = await Auth.currentSession();
        if (!ignore) {
          setTokens({
            accessToken: session.getAccessToken().getJwtToken(),
            idToken: session.getIdToken().getJwtToken(),
          });
        }
      } catch (error) {
        if (!ignore) {
          setSessionError(error);
        }
      } finally {
        if (!ignore) {
          setIsSessionLoading(false);
        }
      }
    })();
    return () => {
      ignore = true;
    };
  }, []);

  return { tokens, isSessionLoading, sessionError };
}

function getAuthorizedHeaders(tokens) {
  return {
    Authorization: `Bearer ${tokens?.accessToken}`,
    'id-Token': tokens?.idToken,
  };
}

async function reverseGeocode(latitude, longitude) {
  const apiKey = import.meta.env.VITE_API_KEY_GEOAMPIFY;
  const url = `https://api.geoapify.com/v1/geocode/reverse?lat=${latitude}&lon=${longitude}&format=json&apiKey=${apiKey}`;

  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    if (!data.results || !data.results.length) {
      return '';
    }
    return data.results[0].formatted;
  } catch (error) {
    console.error('Error during reverse geocoding:', error.message);
    return '';
  }
}

/* ----------------------------------
 * Main Component: TagPage
 * ---------------------------------- */
export function TagPage() {
  const { tagId } = useParams();
  const navigate = useNavigate();
  const { tokens, isSessionLoading, sessionError } = useSessionTokens();
  const [isLoading, setIsLoading] = useState(true);
  const [errorMsg, setErrorMsg] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [tagStyle, setTagStyle] = useState('skeleton');
  const [tagData, setTagData] = useState({
    tag: [],
    publicUrl: '',
    privateUrl: '',
    customName: '',
    publicActive: false,
    privateActive: false,
    selectedAssetId: {},
    updateLocationOnPublicScan: false,
    updateLocationOnPrivateScan: false,
  });
  const [originalTagData, setOriginalTagData] = useState({});
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isAssetLinked, setIsAssetLinked] = useState(false);
  // Add ref to track isAssetLinked state
  const isAssetLinkedRef = useRef(isAssetLinked);
  const [hasAssetDataChanges, setHasAssetDataChanges] = useState(false);
  const [activeSegment, setActiveSegment] = useState('Settings');
  const [isLocationEnabled, setIsLocationEnabled] = useState(false);
  const [coordinates, setCoordinates] = useState({ latitude: null, longitude: null });
  const [address, setAddress] = useState('');
  const handleError = useCallback(createErrorHandler(setErrorMsg), [setErrorMsg]);
  const [mapRefreshKey, setMapRefreshKey] = useState(0);
  const [assetTags, setAssetTags] = useState([]);
  const [isOperatorUser, setIsOperatorUser] = useState(false);

  // Add state for release confirmation dialog
  const [showReleaseConfirmation, setShowReleaseConfirmation] = useState(false);

  // Add these state variables to track URL locking
  const [isPublicUrlLocked, setIsPublicUrlLocked] = useState(false);
  const [isPrivateUrlLocked, setIsPrivateUrlLocked] = useState(false);
  const [connectedAssetId, setConnectedAssetId] = useState(null);

  // Add product page URL for locked fields
  const productPageUrl = connectedAssetId ? `https://thinkertags.com/assets/${connectedAssetId}/storefront/view` : '';
  
  // Update ref when isAssetLinked changes
  useEffect(() => {
    isAssetLinkedRef.current = isAssetLinked;
  }, [isAssetLinked]);

  /* ------------------------------------------------------------------
   * Effect: Check if logged in user belongs to Operator group
   * ------------------------------------------------------------------ */
  useEffect(() => {
    if (!tokens) return;
    // Get current authenticated user
    Auth.currentAuthenticatedUser()
      .then(user => {
        const username = user.username;
        return API.get('api', `/users/${encodeURIComponent(username)}`, {
          headers: getAuthorizedHeaders(tokens)
        });
      })
      .then(response => {
        if (response && response.role === 'Operator') {
          setIsOperatorUser(true);
        } else {
          setIsOperatorUser(false);
        }
      })
      .catch(err => {
        console.error('Error fetching user role:', err);
      });
  }, [tokens]);

  /* ------------------------------------------------------------------
   * Save Helper Functions
   * ------------------------------------------------------------------ */
  const saveAssetSettings = useCallback(
    async (assetId, { publicUrl, privateUrl, locationData }) => {
      if (!tokens) {
        handleError(null, 'No tokens available for asset settings update.');
        return false;
      }
      if (!assetId) {
        handleError(null, 'No asset ID provided for asset settings update.');
        return false;
      }

      try {
        const payload = { 
          publicAction: publicUrl || '', 
          privateAction: privateUrl || '' 
        };
        
        // Add location data to payload if provided
        if (locationData) {
          payload.latitude = locationData.latitude;
          payload.longitude = locationData.longitude;
          payload.address = locationData.address;
          payload.isLocationEnabled = locationData.isLocationEnabled;
        }
        
        const res = await API.post('api', `/assets/${assetId}`, {
          body: payload,
          headers: getAuthorizedHeaders(tokens),
        });
        if (res) return true;
        return false;
      } catch (err) {
        handleError(err, 'Error updating asset settings.');
        return false;
      }
    },
    [tokens, handleError]
  );

  const saveTagData = useCallback(
    async (override = null) => {
      if (!tagId) {
        handleError(null, 'No Tag ID provided for saving tag data.');
        return false;
      }
      if (!tokens) {
        handleError(null, 'No tokens available for saving tag data.');
        return false;
      }

      try {
        const finalData = override || tagData;

        const payload = {
          public: { url: finalData.publicUrl || '' },
          private: { url: finalData.privateUrl || '' },
          customName: finalData.customName,
          assetId: finalData.selectedAssetId.id || '',
          assetName: finalData.selectedAssetId.name || '',
          ...(isLocationEnabled && coordinates.latitude && coordinates.longitude && {
            latitude: coordinates.latitude,
            longitude: coordinates.longitude,
          }),
          ...(isLocationEnabled && address && { address }),
          updateLocationOnPublicScan: !!finalData.updateLocationOnPublicScan,
          updateLocationOnPrivateScan: !!finalData.updateLocationOnPrivateScan,
          isLocationEnabled,
        };

        const response = await API.post('api', `/tags/${tagId}`, {
          body: payload,
          headers: getAuthorizedHeaders(tokens),
        });

        if (response) {
          setHasUnsavedChanges(false);
          setOriginalTagData({
            ...finalData,
            isLocationEnabled,
            coordinates,
            address,
          });
          return true;
        }
      } catch (error) {
        handleError(error, 'Error saving tag data.');
      }
      return false;
    },
    [tagData, tagId, tokens, isLocationEnabled, coordinates, address, handleError]
  );

  const saveAssetData = useCallback(
    async (assetId, operation) => {
      if (!tokens) {
        handleError(null, 'No tokens available for saving asset data.');
        return false;
      }
      if (!assetId) {
        handleError(null, `No asset ID provided to ${operation} the tag.`);
        return false;
      }

      try {
        const bodyData = { tagId, operation };
        const response = await API.post('api', `/assets/${assetId}/updateTagIds`, {
          body: bodyData,
          headers: getAuthorizedHeaders(tokens),
        });
        if (response) return true;
      } catch (err) {
        handleError(err, `Failed to ${operation} tag from asset.`);
      }
      return false;
    },
    [tokens, tagId, handleError]
  );

  const fetchAssetData = useCallback(
    async (assetId) => {
      if (!tokens) {
        handleError(null, 'No tokens available for fetching asset data.');
        return null;
      }
      if (!assetId) {
        handleError(null, 'No asset ID provided for fetching asset data.');
        return null;
      }

      try {
        const res = await API.get('api', `/assets/${assetId}`, {
          headers: getAuthorizedHeaders(tokens),
        });
        if (res) {
          // Check if URLs should be locked based on storefront settings
          const isStorefrontEnabled = res.storefrontEnabled === true;
          const isPublicLocked = isStorefrontEnabled && res.publicStorefrontEnabled === true;
          const isPrivateLocked = isStorefrontEnabled && res.privateStorefrontEnabled === true;

          // Set lock states
          setIsPublicUrlLocked(isPublicLocked);
          setIsPrivateUrlLocked(isPrivateLocked);
          setConnectedAssetId(assetId);

          // Get the correct URLs based on lock state
          const productUrl = `https://thinkertags.com/assets/${assetId}/storefront/view`;
          const publicUrl = isPublicLocked ? productUrl : (res.publicAction || '');
          const privateUrl = isPrivateLocked ? productUrl : (res.privateAction || '');

          // Get location data from the asset if available
          if (res.isLocationEnabled !== undefined || res.latitude || res.longitude) {
            setIsLocationEnabled(!!res.isLocationEnabled);
            if (res.latitude && res.longitude) {
              setCoordinates({ 
                latitude: res.latitude, 
                longitude: res.longitude 
              });
              if (res.address) {
                setAddress(res.address.replace(/\n/g, ', '));
              }
              setMapRefreshKey(prev => prev + 1);
            }
          }

          setTagData(prev => ({
            ...prev,
            publicUrl: publicUrl,
            privateUrl: privateUrl,
            publicActive: !!res.publicAction || isPublicLocked,
            privateActive: !!res.privateAction || isPrivateLocked,
            selectedAssetId: {
              ...prev.selectedAssetId,
              id: assetId,
              name: res.name || '',
              description: res.description || '',
            },
          }));

          setIsAssetLinked(true);
          if (Array.isArray(res.tagIds)) {
            setAssetTags(res.tagIds);
          } else {
            setAssetTags([]);
          }
          return res;
        }
      } catch (err) {
        handleError(err, 'Error fetching asset data.');
      }
      return null;
    },
    [tokens, handleError]
  );

  /* ------------------------------------------------------------------
   * Handlers
   * ------------------------------------------------------------------ */
  const handleSegmentChange = useCallback((segment) => {
    setActiveSegment(segment);
  }, []);

  const handleItemSelect = useCallback(
    async (item) => {
      setIsLoading(true);
      try {
        await fetchAssetData(item.id);

        setTagData(prev => ({
          ...prev,
          selectedAssetId: {
            id: item.id,
            name: item.name,
            description: item.category,
          },
          customName: item.name,
        }));

        setIsAssetLinked(true);
        setHasUnsavedChanges(true);
        setHasAssetDataChanges(false);
      } catch (err) {
        handleError(err, 'Error selecting asset item.');
      } finally {
        setIsLoading(false);
      }
    },
    [fetchAssetData, handleError]
  );

  const handleAssetClear = useCallback(() => {
    // Reset asset connection and URL locks
    setTagData(prev => ({
      ...prev,
      selectedAssetId: { id: '', name: '' },
      publicUrl: '',
      privateUrl: '',
      publicActive: false,
      privateActive: false,
    }));
    setIsAssetLinked(false);
    setHasUnsavedChanges(true);
    setAssetTags([]);

    // Reset URL locks
    setIsPublicUrlLocked(false);
    setIsPrivateUrlLocked(false);
    setConnectedAssetId(null);
  }, []);

  // Function to toggle asset link status
  const handleToggleAssetLink = useCallback((isChecked) => {
    setIsAssetLinked(isChecked);
    if (!isChecked) {
      handleAssetClear();
    }
  }, [handleAssetClear]);

  // Handle public URL input changes - respect locks
  const handlePublicUrlChange = useCallback(
    (e) => {
      // Skip changes if the field is locked
      if (isPublicUrlLocked) return;

      setTagData(prev => ({
        ...prev,
        publicUrl: e.target.value,
      }));
      setHasUnsavedChanges(true);
      if (isAssetLinkedRef.current) setHasAssetDataChanges(true);
    },
    [isPublicUrlLocked]
  );

  // Handle public URL blur event
  const handlePublicUrlBlur = useCallback(
    (e) => {
      if (!isPublicUrlLocked) {
        const newValue = ensureProtocol(e.target.value);
        setTagData(prev => ({ ...prev, publicUrl: newValue }));
      }
    },
    [isPublicUrlLocked]
  );

  // Handle private URL input changes - respect locks
  const handlePrivateUrlChange = useCallback(
    (e) => {
      // Skip changes if the field is locked
      if (isPrivateUrlLocked) return;

      setTagData(prev => ({
        ...prev,
        privateUrl: e.target.value,
      }));
      setHasUnsavedChanges(true);
      if (isAssetLinkedRef.current) setHasAssetDataChanges(true);
    },
    [isPrivateUrlLocked]
  );

  // Handler for private URL onBlur
  const handlePrivateUrlBlur = useCallback(
    (e) => {
      if (!isPrivateUrlLocked) {
        const newValue = ensureProtocol(e.target.value);
        setTagData(prev => ({ ...prev, privateUrl: newValue }));
      }
    },
    [isPrivateUrlLocked]
  );

  const handleInputChange = useCallback(
    (e) => {
      const { name, value } = e.target;
      setTagData(prev => ({ ...prev, [name]: value }));
      setHasUnsavedChanges(true);

      if (isAssetLinkedRef.current && (name === 'publicUrl' || name === 'privateUrl')) {
        setHasAssetDataChanges(true);
      }
    },
    []
  );

  // Handle toggling private redirect - respect locks
  const handlePrivateRedirectToggle = useCallback(
    (isChecked) => {
      if (isPrivateUrlLocked && isChecked === false) {
        // Cannot disable a locked redirect
        return;
      }

      setTagData(prev => ({
        ...prev,
        privateActive: isChecked,
        privateUrl: isChecked ? prev.privateUrl : '',
      }));
      setHasUnsavedChanges(true);
      if (isAssetLinkedRef.current) {
        if (!isChecked || (isChecked && tagData.privateUrl)) {
          setHasAssetDataChanges(true);
        }
      }
    },
    [isPrivateUrlLocked, tagData.privateUrl]
  );

  const handleManualUpdateLocation = useCallback(() => {
    if (!navigator.geolocation) {
      handleError(null, 'Geolocation is not supported by this browser.');
      return;
    }

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const latitudeVal = position.coords.latitude;
        const longitudeVal = position.coords.longitude;

        setCoordinates({ latitude: latitudeVal, longitude: longitudeVal });
        setHasUnsavedChanges(true);
        
        // If connected to an asset, mark location changes for asset update
        if (isAssetLinkedRef.current) {
          setHasAssetDataChanges(true);
        }

        try {
          const foundAddress = await reverseGeocode(latitudeVal, longitudeVal);
          setAddress(foundAddress);
          setMapRefreshKey(prev => prev + 1);
        } catch (err) {
          handleError(err, 'Could not determine address from latitude/longitude.');
        }
      },
      (error) => {
        handleError(error, 'Unable to retrieve your location. Please try again.');
      }
    );
  }, [handleError]);

  const handleToggleLocation = useCallback(() => {
    setIsLocationEnabled(prev => !prev);
    setHasUnsavedChanges(true);
    
    // If connected to an asset, mark location changes for asset update
    if (isAssetLinkedRef.current) {
      setHasAssetDataChanges(true);
    }
  }, []);

  // New handler to open map with coordinates
  const handleOpenMap = useCallback(() => {
    if (coordinates.latitude && coordinates.longitude) {
      // Detect device types
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
      const isAndroid = /Android/.test(navigator.userAgent);
      const isMobile = isIOS || isAndroid || /Mobi|Mobile/i.test(navigator.userAgent);

      if (isIOS) {
        // Use Apple Maps URL scheme for iOS devices
        window.location.href = `maps:?q=${coordinates.latitude},${coordinates.longitude}`;
      } else if (isAndroid) {
        // Use geo: URI scheme for Android devices (opens native Maps app)
        window.location.href = `geo:${coordinates.latitude},${coordinates.longitude}?q=${coordinates.latitude},${coordinates.longitude}`;
      } else {
        // For desktop: open in new tab
        if (!isMobile) {
          window.open(`https://maps.google.com/?q=${coordinates.latitude},${coordinates.longitude}`, '_blank');
        } else {
          // Fallback for other mobile devices
          window.location.href = `https://maps.google.com/?q=${coordinates.latitude},${coordinates.longitude}`;
        }
      }
    }
  }, [coordinates]);

  const handleSave = useCallback(async () => {
    setIsSaving(true);
    setErrorMsg('');

    try {
      const finalPublicUrl = ensureProtocol(tagData.publicUrl);
      const finalPrivateUrl = ensureProtocol(tagData.privateUrl);

      // Prepare location data
      const locationData = isLocationEnabled ? {
        latitude: coordinates.latitude,
        longitude: coordinates.longitude,
        address,
        isLocationEnabled
      } : {
        isLocationEnabled: false
      };

      const hadAssetBefore = originalTagData?.selectedAssetId?.id;
      const hasAssetNow = tagData?.selectedAssetId?.id;
      const isAssetSwitch =
        hadAssetBefore && hasAssetNow && hadAssetBefore !== hasAssetNow;

      if (isAssetSwitch) {
        await saveAssetData(hadAssetBefore, 'remove');
        if (hasAssetDataChanges) {
          await saveAssetSettings(tagData.selectedAssetId.id, {
            publicUrl: finalPublicUrl,
            privateUrl: finalPrivateUrl,
            locationData
          });
        }
        await saveTagData({
          ...tagData,
          publicUrl: finalPublicUrl,
          privateUrl: finalPrivateUrl,
        });
        const result = await saveAssetData(tagData.selectedAssetId.id, 'add');
        if (result) {
          // Update the local list to include the current tag
          setAssetTags(prev => {
            if (!prev.includes(tagId)) {
              return [...prev, tagId];
            }
            return prev;
          });
        }
      }
      else if (!hadAssetBefore && hasAssetNow) {
        if (hasAssetDataChanges) {
          await saveAssetSettings(tagData.selectedAssetId.id, {
            publicUrl: finalPublicUrl,
            privateUrl: finalPrivateUrl,
            locationData
          });
        }
        await saveTagData({
          ...tagData,
          publicUrl: finalPublicUrl,
          privateUrl: finalPrivateUrl,
        });
        const result = await saveAssetData(tagData.selectedAssetId.id, 'add');
        if (result) {
          // Update the local list to include the current tag
          setAssetTags(prev => {
            if (!prev.includes(tagId)) {
              return [...prev, tagId];
            }
            return prev;
          });
        }
      }
      else if (hadAssetBefore && !hasAssetNow) {
        await saveAssetData(hadAssetBefore, 'remove');
        await saveTagData({
          ...tagData,
          publicUrl: finalPublicUrl,
          privateUrl: finalPrivateUrl,
        });
      }
      else {
        if (isAssetLinkedRef.current && hasAssetDataChanges) {
          await saveAssetSettings(tagData.selectedAssetId.id, {
            publicUrl: finalPublicUrl,
            privateUrl: finalPrivateUrl,
            locationData
          });
        }
        await saveTagData({
          ...tagData,
          publicUrl: finalPublicUrl,
          privateUrl: finalPrivateUrl,
        });
      }

      setOriginalTagData({
        ...tagData,
        publicUrl: finalPublicUrl,
        privateUrl: finalPrivateUrl,
        isLocationEnabled,
        coordinates,
        address,
      });
      setHasUnsavedChanges(false);
      setHasAssetDataChanges(false);
    } catch (error) {
      handleError(error, 'Error saving changes. Please try again.');
    } finally {
      setIsSaving(false);
    }
  }, [
    tagData,
    originalTagData,
    coordinates,
    address,
    isLocationEnabled,
    hasAssetDataChanges,
    saveAssetSettings,
    saveAssetData,
    saveTagData,
    handleError,
    tagId,
  ]);

  const releaseTag = useCallback(async () => {
    if (!tagId) {
      handleError(null, 'No Tag ID provided for releasing the tag.');
      return;
    }
    if (!tokens) {
      handleError(null, 'No tokens available for releasing the tag.');
      return;
    }

    try {
      // Get the connected asset ID from the current tag data
      const connectedAssetId = tagData.selectedAssetId?.id;
      
      // If tag is connected to an asset, remove it from that asset first
      if (connectedAssetId) {
        const bodyRemoveFromAsset = { tagId, operation: 'remove' };
        await API.post('api', `/assets/${connectedAssetId}/updateTagIds`, {
          headers: getAuthorizedHeaders(tokens),
          body: bodyRemoveFromAsset,
        });
      }
      
      // Release the tag
      const res = await API.post('api', `/tags/${tagId}/releaseTag`, {
        headers: getAuthorizedHeaders(tokens),
      });
      
      if (res) {
        navigate('/tags');
      } else {
        handleError(null, 'Failed to release tag.');
      }
    } catch (error) {
      handleError(error, 'Error releasing tag.');
    }
  }, [tokens, tagId, tagData, navigate, handleError]);

  /* ------------------------------------------------------------------
   * Effects: Fetch Tag Data on Mount
   * ------------------------------------------------------------------ */
  useEffect(() => {
    if (isSessionLoading) return;
    if (sessionError) {
      handleError(sessionError, 'Failed to get auth session. Please refresh.');
      return;
    }
    if (!tagId) {
      handleError(null, 'No Tag ID found in URL.');
      return;
    }
    if (!tokens) {
      return;
    }

    let ignore = false;
    (async () => {
      setIsLoading(true);
      try {
        const res = await API.get('api', `/tags/${tagId}`, {
          headers: getAuthorizedHeaders(tokens),
        });
        if (res && !ignore) {
          if (res.assetId) {
            const assetData = await fetchAssetData(res.assetId);

            const newTagData = {
              tag: res,
              publicUrl: assetData?.publicAction || '',
              privateUrl: assetData?.privateAction || '',
              publicActive: !!assetData?.publicAction,
              privateActive: !!assetData?.privateAction,
              customName: res.customName || '',
              selectedAssetId: {
                name: res.assetName || '',
                id: res.assetId || '',
                description: assetData?.description || '',
              },
              style: res.style || '',
              updateLocationOnPublicScan: !!res.updateLocationOnPublicScan,
              updateLocationOnPrivateScan: !!res.updateLocationOnPrivateScan,
            };

            setTagData(newTagData);
            setOriginalTagData(newTagData);
            setIsAssetLinked(true);

            // Get location data from asset if available
            if (assetData && (assetData.isLocationEnabled || assetData.latitude || assetData.longitude)) {
              setIsLocationEnabled(!!assetData.isLocationEnabled);
              if (assetData.latitude && assetData.longitude) {
                setCoordinates({ 
                  latitude: assetData.latitude, 
                  longitude: assetData.longitude 
                });
                if (assetData.address) {
                  setAddress(assetData.address.replace(/\n/g, ', '));
                }
                setMapRefreshKey(prev => prev + 1);
              }
            } else if (typeof res.isLocationEnabled === 'boolean') {
              setIsLocationEnabled(res.isLocationEnabled);
              if (res.isLocationEnabled && (res.latitude == null || res.longitude == null)) {
                handleManualUpdateLocation();
              }
              if (res.latitude != null && res.longitude != null) {
                setCoordinates({ latitude: res.latitude, longitude: res.longitude });
                if (res.address) {
                  setAddress(res.address.replace(/\n/g, ', '));
                }
              }
            } else if (res.latitude != null && res.longitude != null) {
              setIsLocationEnabled(true);
              setCoordinates({ latitude: res.latitude, longitude: res.longitude });
              if (res.address) {
                setAddress(res.address.replace(/\n/g, ', '));
              }
            }
            setTagStyle(res.style || 'gold');
          } else {
            const newTagData = {
              tag: res,
              publicUrl: res.public?.url || '',
              privateUrl: res.private?.url || '',
              publicActive: !!res.public?.url,
              privateActive: !!res.private?.url,
              customName: res.customName || '',
              selectedAssetId: { name: '', id: '' },
              style: res.style || '',
              updateLocationOnPublicScan: !!res.updateLocationOnPublicScan,
              updateLocationOnPrivateScan: !!res.updateLocationOnPrivateScan,
            };
            setTagData(newTagData);
            setOriginalTagData(newTagData);
            setIsAssetLinked(false);

            // Reset URL locks when not linked to an asset
            setIsPublicUrlLocked(false);
            setIsPrivateUrlLocked(false);
            setConnectedAssetId(null);

            if (typeof res.isLocationEnabled === 'boolean') {
              setIsLocationEnabled(res.isLocationEnabled);
              if (res.isLocationEnabled && (res.latitude == null || res.longitude == null)) {
                handleManualUpdateLocation();
              }
              if (res.latitude != null && res.longitude != null) {
                setCoordinates({ latitude: res.latitude, longitude: res.longitude });
                if (res.address) {
                  setAddress(res.address.replace(/\n/g, ', '));
                }
              }
            } else if (res.latitude != null && res.longitude != null) {
              setIsLocationEnabled(true);
              setCoordinates({ latitude: res.latitude, longitude: res.longitude });
              if (res.address) {
                setAddress(res.address.replace(/\n/g, ', '));
              }
            }
            setTagStyle(res.style || 'gold');
          }
        }
      } catch (err) {
        if (!ignore) handleError(err, 'Error fetching tag data.');
      } finally {
        if (!ignore) setIsLoading(false);
      }
    })();

    return () => {
      ignore = true;
    };
  }, [
    tagId,
    tokens,
    isSessionLoading,
    sessionError,
    fetchAssetData,
    handleError,
    handleManualUpdateLocation,
  ]);

  /* ------------------------------------------------------------------
   * Derived UI Helpers
   * ------------------------------------------------------------------ */
  const selectedAssetFromTag = useMemo(() => {
    if (tagData.selectedAssetId?.id) {
      return {
        id: tagData.selectedAssetId.id,
        name: tagData.selectedAssetId.name,
        category: tagData.selectedAssetId.description || 'Uncategorized',
      };
    }
    return null;
  }, [tagData.selectedAssetId]);

  const getFooterButtonText = useCallback(() => {
    if (isSaving) return 'Saving...';
    if (!hasUnsavedChanges) return 'Saved';
    if (isAssetLinkedRef.current && hasAssetDataChanges) return 'Save Changes to Asset';
    return 'Update';
  }, [isSaving, hasUnsavedChanges, hasAssetDataChanges]);

  const handleFooterButtonClick = useCallback(() => {
    handleSave();
  }, [handleSave]);

  /* ------------------------------------------------------------------
   * Render
   * ------------------------------------------------------------------ */
  return (
    <div className="settings-container">
      {/* Header */}
      <div className="settings-header-container">
        <Link to="/tags" className="back-arrow">
          <img
            src="/arrow_back.svg"
            alt="Arrow Back"
            className="backarrow"
          />
          {' '}Tags
        </Link>
      </div>

      {/* Main Panel */}
      <div className="settings-panel">
        {/* Use the new ThinkertagsPreviewPanel component */}
        <ThinkertagsPreviewPanel
          isLoading={isLoading}
          tagStyle={tagStyle}
          tagId={tagId}
          tagName={tagData.customName}
          publicUrl={tagData.publicUrl}
          isPublicUrlLocked={isPublicUrlLocked}
          onNameChange={handleInputChange}
          onPublicUrlChange={handlePublicUrlChange}
          onPublicUrlBlur={handlePublicUrlBlur}
        />

        {/* Tabs: Settings | Scans */}
        <ToggleSwitch
          options={['Settings', 'Scans']}
          onChange={handleSegmentChange}
          activeOption={activeSegment}
          specialRounded={['Settings', 'Scans']}
        />

        {activeSegment === 'Settings' && (
          <div className="settings-tab-one-content">
            <div className="toggle-text-container">
              <div className="settings-input-group-h3">Options</div>
            </div>

            {/* Using the RedirectAuth component */}
            <RedirectAuth
              isLoading={isLoading}
              isActive={tagData.privateActive}
              isUrlLocked={isPrivateUrlLocked}
              url={tagData.privateUrl}
              productPageUrl={productPageUrl}
              onToggle={handlePrivateRedirectToggle}
              onUrlChange={handlePrivateUrlChange}
              onUrlBlur={handlePrivateUrlBlur}
              ensureProtocol={ensureProtocol}
            />

            {/* Using the ConnectToAsset component */}
            <ConnectToAsset
              isLoading={isLoading}
              isAssetLinked={isAssetLinked}
              selectedAsset={selectedAssetFromTag}
              assetTags={assetTags}
              onToggleAssetLink={handleToggleAssetLink}
              onItemSelect={handleItemSelect}
              onAssetClear={handleAssetClear}
              hasUnsavedChanges={hasUnsavedChanges}
              setHasUnsavedChanges={setHasUnsavedChanges}
            />

            {/* Tag Location */}
            {isLoading ? (
              <div style={{ marginBottom: '15px', marginTop: '7.5px' }}>
                <Skeleton
                  height={66}
                  borderRadius={10}
                  baseColor="#ebebeb"
                  highlightColor="#ffffff"
                />
              </div>
            ) : (
              <div className="settings-input-group-two">
                <div
                  className={`toggle-div-collapsable-1-sub-1 ${
                    tagData.isLocationEnabled ? 'expanded' : ''
                  }`}
                >
                  <div className="toggle-text-container-parent">
                    <div className="link-primary-icon">
                      <img
                        src="/location.svg"
                        alt="Location"
                        width="16"
                        height="16"
                      />
                    </div>
                    <div className="toggle-text-container">
                      <div className="settings-input-group-h4">Tag Location</div>
                      <div className="settings-input-group-h4-sub">
                        Map and manage your tags effortlessly
                      </div>
                    </div>
                  </div>

                  <label className="switch">
                    <input
                      type="checkbox"
                      checked={isLocationEnabled}
                      onChange={handleToggleLocation}
                    />
                    <span className="slider round" />
                  </label>
                </div>

                <div
                  className={`location-collapse-container ${
                    isLocationEnabled ? 'expanded' : ''
                  }`}
                >
                  <div
                    style={{
                      borderTop: '1px solid #E5E7EB',
                      margin: '12px 0',
                    }}
                  />
                  <div className="location-details-row" style={{ transform: 'translateZ(0)' }}>
                    <div
                      className="location-info-column"
                      onClick={handleOpenMap}
                      role="button"
                      aria-label="Open location in maps"
                      tabIndex={coordinates.latitude && coordinates.longitude ? 0 : -1}
                    >
                      <div className="address-line">
                        {address || 'No address available'}
                      </div>
                      {coordinates.latitude && coordinates.longitude && (
                        <div className="coords-line">
                          lat: {coordinates.latitude.toFixed(6)} | long: {coordinates.longitude.toFixed(6)}
                        </div>
                      )}
                    </div>
                    <button
                      className="update-location-button"
                      onClick={handleManualUpdateLocation}
                    >
                      Update
                    </button>
                  </div>

                  {coordinates.latitude && coordinates.longitude && (
                    <div
                      style={{
                        borderTop: '1px solid #E5E7EB',
                        margin: '12px 0',
                      }}
                    />
                  )}

                  {/* Map display */}
                  {coordinates.latitude && coordinates.longitude && (
                    <div className="map-container expanded">
                      <MapComponent
                        key={mapRefreshKey}
                        lat={coordinates.latitude}
                        long={coordinates.longitude}
                        mapStyle="standardLight"
                      />
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* More / Release Tag */}
            <div>
              <div className="settings-input-group-h3">More</div>
              <ButtonDiv
                data={{
                  title: 'Release Tag',
                  description: '',
                  iconExpand: 'expand_more',
                  iconCollapse: 'expand_less',
                  subTitle: '',
                  subDescription: isOperatorUser ? 'Admins and Managers can release Tags' : 'Release this tag back into the wild.',
                  buttonText: isOperatorUser ? 'Release Tag (Disabled)' : 'Release Now',
                  isInitiallyExpanded: false,
                }}
                onButtonClick={() => {
                  if (!isOperatorUser) {
                    // Open the confirmation dialog instead of immediately releasing
                    setShowReleaseConfirmation(true);
                  }
                }}
                disabled={isOperatorUser}
              />
            </div>
          </div>
        )}

        {activeSegment === 'Scans' && (
          <div className="settings-tab-two-content">
            <TagActivity tagId={String(tagId)} />
          </div>
        )}
      </div>

      {/* Footer Save Button */}
      {activeSegment === 'Settings' && (
        <div className="settings-footer">
          <button
            className={`settings-footer-button ${isSaving ? 'saving' : ''}`}
            onClick={handleFooterButtonClick}
            disabled={isSaving || !hasUnsavedChanges}
          >
            {getFooterButtonText()}
          </button>
        </div>
      )}

      {/* Confirmation Dialog for tag release */}
      <ConfirmationDialog
        isOpen={showReleaseConfirmation}
        title="Release Tag"
        message="Are you sure you want to release this tag? This action cannot be undone."
        confirmText="Release"
        cancelText="Cancel"
        onConfirm={() => {
          setShowReleaseConfirmation(false);
          releaseTag();
        }}
        onCancel={() => setShowReleaseConfirmation(false)}
        isDanger={true}
      />
    </div>
  );
}