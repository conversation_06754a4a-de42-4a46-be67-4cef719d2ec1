import React, { useState, useEffect, useRef } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { Auth, API } from "aws-amplify";
import '../styles/TagPage.css';
import '../styles/TagPageClaim.css';
import '../styles/QRPreviewPanel.css';
import { Thinkertag } from '../components/Thinkertag';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import { Input } from '../components/Input';



const TagPageClaim = () => {
  const { tagId } = useParams();
  const navigate = useNavigate();
  const [tagStyle, setTagStyle] = useState("skeleton");
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [tagData, setTagData] = useState({
    tag: [],
    publicUrl: 'https://thinkertags.com',
    privateUrl: '',
    customName: '⭐ My Thinkertag',
    publicActive: false,
    privateActive: false,
    selectedAssetId: {}
  });

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []); // Empty dependency array means this runs once when component mounts

  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const searchTimeout = useRef(null);
  const isInitialFetch = useRef(true);

  const [activeSegment, setActiveSegment] = useState('Tag'); // Default to 'Tag'
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState(['', '', '', '', '', '']);
  const verificationInputRefs = [useRef(), useRef(), useRef(), useRef(), useRef(), useRef()];
  const [password, setPassword] = useState('');
  const [signupStep, setSignupStep] = useState('email-password'); // Changed from 'email' to 'email-password'
  const [signupError, setSignupError] = useState('');
  const [isCodeValid, setIsCodeValid] = useState(false);

  const [debugMode, setDebugMode] = useState(false);

  const verificationSectionRef = useRef(null);

  useEffect(() => {
    if (signupStep === 'verify') {
      verificationSectionRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [signupStep]);


  const checkVerificationCode = async (code) => {
    try {
      // Just check the code without completing signup
      await Auth.confirmSignUp(email, code, { forceAliasCreation: false });
      setIsCodeValid(true);
      setSignupError('');
    } catch (error) {
      setIsCodeValid(false);
      setSignupError(error.message);
    }
  };

  const handleVerifyCode = async () => {
    const code = verificationCode.join('');
    if (code.length !== 6) {
      setSignupError('Please enter the complete 6-digit verification code');
      return;
    }
  
    try {
      setIsSubmitting(true);
    
      await Auth.signIn(email, password);
      setIsLoggedIn(true);
    
      const claimSuccess = await ClaimTag();
    
      if (claimSuccess) {
        
        await handleSave();
        
        window.location.href = `/tags`;
      } else {
        throw new Error('Failed to claim the tag');
      }
    } catch (error) {
      console.error('Error during verification process:', error);
      setSignupError(error.message || 'An error occurred during the verification process');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCodeChange = (index, value) => {
    if (!/^\d*$/.test(value)) return; // Only allow digits

    // Handle pasting of complete verification code
    if (value.length > 1) {
      const pastedCode = value.slice(0, 6).split('');
      const newCode = [...verificationCode];
      
      // Fill in the verification code array with the pasted digits
      pastedCode.forEach((digit, i) => {
        if (i < 6) {
          newCode[i] = digit;
        }
      });
      
      setVerificationCode(newCode);
      setSignupError('');

      // Check if we have a complete code after pasting
      if (pastedCode.length === 6) {
        verificationInputRefs[5].current?.focus();
        checkVerificationCode(pastedCode.join(''));
      } else {
        // Focus the next empty input
        const nextEmptyIndex = pastedCode.length;
        if (nextEmptyIndex < 6) {
          verificationInputRefs[nextEmptyIndex].current?.focus();
        }
      }
      return;
    }

    // Handle single digit input
    const newCode = [...verificationCode];
    newCode[index] = value;
    setVerificationCode(newCode);
    setSignupError('');

    // Auto-focus next input
    if (value && index < 5) {
      verificationInputRefs[index + 1].current?.focus();
    }

    // Check if we have a complete code after adding a single digit
    const updatedCode = [...newCode];
    updatedCode[index] = value;
    if (updatedCode.every(digit => digit !== '') && updatedCode.length === 6) {
      checkVerificationCode(updatedCode.join(''));
    }
  };

  const handleCodePaste = (event, index) => {
    event.preventDefault();
    const pastedData = event.clipboardData.getData('text');
    
    // Check if pasted content contains only digits
    if (!/^\d*$/.test(pastedData)) return;
    
    handleCodeChange(index, pastedData);
  };

  const handleKeyDown = (index, e) => {
    // Handle backspace
    if (e.key === 'Backspace' && !verificationCode[index] && index > 0) {
      verificationInputRefs[index - 1].current?.focus();
    }
  };

  const isEmailValid = (email) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  const handleSendVerificationCode = async () => {
    setSignupError('');
    if (password.length < 8) {
      setSignupError('Password must be at least 8 characters long');
      return;
    }
  
    try {
      if (debugMode) {
        // For debug mode, skip the actual sign-up process and set the verification code to '123456'
        setVerificationCode(['1', '2', '3', '4', '5', '6']);
        setSignupStep('verify');
      } else {
        await Auth.signUp({
          username: email,
          password: password,
          attributes: {
            email,
          },
        });
        setSignupStep('verify');
      }
    } catch (error) {
      if (error.code === 'UsernameExistsException') {
        try {
          await Auth.resendSignUp(email);
          setSignupStep('verify');
        } catch (resendError) {
          setSignupError(resendError.message);
        }
      } else {
        setSignupError(error.message);
      }
    }
  };

  const ensureProtocol = (url) => {
    if (!url) return url;

    const trimmedUrl = url.trim();
    if (!trimmedUrl) return trimmedUrl;

    // Common protocols to check for
    const protocols = [
      // Web protocols
      'https://',
      'http://',
      'ws://',
      'wss://',

      // File transfer
      'ftp://',
      'sftp://',
      'ftps://',

      // File systems
      'file://',
      'smb://',
      'nfs://',

      // Email and communication
      'mailto:',
      'tel:',
      'sms:',
      'facetime:',
      'callto:',
      'sip:',

      // Media streaming
      'rtsp://',
      'rtmp://',
      'spotify:',

      // Version control
      'git://',
      'svn://',

      // Maps and location
      'maps:',
      'geo:',
      'waze://',

      // Mobile deep linking
      'market://', // Android Play Store
      'itms://',   // iOS App Store
      'itms-apps://',

      // Specialized protocols
      'steam://',
      'discord://',
      'slack://',
      'teams://',
      'zoommtg://',
      'meet:',
      'skype:',
      'whatsapp://',
      'telegram:',
      'signal:',

      // Enterprise/Database
      'ldap://',
      'jdbc:',
      'odbc:',

      // Payment
      'bitcoin:',
      'ethereum:',

      // Your custom protocol
      'fmp://',
      'fmp12://'
    ];

    // Check if URL already starts with any protocol
    const hasProtocol = protocols.some(protocol =>
      trimmedUrl.toLowerCase().startsWith(protocol)
    );

    // Remove any accidental double protocols
    // e.g., "https://http://example.com" → "https://example.com"
    if (hasProtocol) {
      const urlWithoutDoubleProtocols = protocols.reduce((url, protocol) => {
        const regex = new RegExp(`${protocol}.*?${protocol}`, 'i');
        return url.replace(regex, protocol);
      }, trimmedUrl);
      return urlWithoutDoubleProtocols;
    }

    // Add https:// if no protocol is present
    return `https://${trimmedUrl}`;
  };




  const handleInputChange = (event, newValue, reason) => {
    if (event.target) {
      // Handle input change for regular input fields
      const { name, value } = event.target;
      setTagData(prevState => ({
        ...prevState,
        [name]: value
      }));
      setHasUnsavedChanges(true);
    } else if (reason === 'input') {
      // Handle input change for Autocomplete
      const value = newValue ?? '';
      setSearchTerm(value);

      if (value.length > 1) {
        if (searchTimeout.current) {
          clearTimeout(searchTimeout.current);
        }
        searchTimeout.current = setTimeout(() => {
          fetchAssetsBySearch(value);
        }, 1000);
      }
    }
  };

  const saveTagData = async () => {
    const session = await Auth.currentSession();
    const data = {
      public: { url: tagData.publicUrl || '' },
      private: { url: tagData.privateUrl || '' },
      customName: tagData.customName,
      assetId: tagData.selectedAssetId.id || '',
      assetName: tagData.selectedAssetId.name || ''
    };
  
    const response = await API.post('api', `/tags/${tagId}`, {
      body: data,
      headers: {
        Authorization: `Bearer ${session.getAccessToken().getJwtToken()}`,
        'id-Token': session.getIdToken().getJwtToken()
      },
    });
  
    if (response) {
      console.log('Successfully saved tag data.');
      setHasUnsavedChanges(false);
      setOriginalTagData({ ...tagData });
    } else {
      console.log('Failed to save tag data');
    }
  };

  const handleSave = async () => {
    try {
      await saveTagData();
    } catch (error) {
      console.error('Error saving tag data:', error);
    }
  };


  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        const user = await Auth.currentAuthenticatedUser();
        setIsLoggedIn(true);
      } catch (error) {
        setIsLoggedIn(false);
      }
    };

    const fetchTagStyle = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`https://api.thinkertags.com/tags/${tagId}/claim`);
        if (response.ok) {
          const data = await response.json();
          setTagStyle(data.style || "gold");
        } else {
          console.warn('Failed to fetch tag style, defaulting to gold');
          setTagStyle("gold");
        }
      } catch (error) {
        console.error('Error fetching tag style:', error);
        console.warn('Defaulting to gold due to error');
        setTagStyle("gold");
      } finally {
        setIsLoading(false);
      }
    };

    fetchTagStyle();
    checkAuthStatus();
  }, [tagId]);

  const ClaimTag = async () => {
    const session = await Auth.currentSession();
    try {
      const response = await API.post('api', `/tags/${tagId}/claim`, {
        headers: {
          Authorization: `Bearer ${session.getAccessToken().getJwtToken()}`,
          'id-Token': session.getIdToken().getJwtToken()
        }
      });
  
      if (response) {
        console.log(response);
        return true; // Return true if the claim is successful
      } else {
        console.log('Failed to claim tag');
        return false; // Return false if the claim fails
      }
    } catch (error) {
      console.error('Error claiming tag:', error);
      throw error; // Rethrow the error to be caught in the calling function
    }
  };


  useEffect(() => {

        //Hide logo
        const isometricNavbar = document.querySelector('.isometric-navbar');
        const logoContainer = document.querySelector('.logo-container');
        if (logoContainer) {
          logoContainer.style.display = 'none';
        }
        if (isometricNavbar) {
          isometricNavbar.style.display = 'none';
        }


    const fetchTag = async () => {
      const session = await Auth.currentSession();
      const response = await API.get("api", `/tags/${tagId}`, {
        headers: {
          Authorization: `Bearer ${(await Auth.currentSession()).getAccessToken().getJwtToken()}`,
          'id-Token': session.getIdToken().getJwtToken()
        }
      });
      if (response) {
        const newTagData = {
          tag: response,
          publicUrl: response.public?.url || '',
          privateUrl: response.private?.url || '',
          publicActive: !!response.public?.url,
          privateActive: !!response.private?.url,
          customName: response.customName || "",
          selectedAssetId: {
            name: response.assetName || "",
            id: response.assetId || ""
          },
          style: response.style || ""
        };
        setTagData(newTagData);
        setOriginalTagData(newTagData);

        if (response.assetName) {
          setSearchTerm(response.assetName);
        }
        setTimeout(() => {
          isInitialFetch.current = false;
        }, 1000);
      }
    };

    fetchTag();
    
  }, [tagId]);

  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        await Auth.currentSession();
        setIsLoggedIn(true);
      } catch (error) {
        setIsLoggedIn(false);
        console.log('User not authenticated:', error);
      }
    };

    checkAuthStatus();
  }, []);



  const renderEmailPasswordSection = (disabled = false) => (
    <div className='settings-input-group-two'>
      <label>Email Address</label>
      <div className="email-input-container">
        <input
          type='email'
          className={`input ${disabled ? 'opacity-50' : ''}`}
          name='email'
          placeholder='Enter your email address'
          value={email}
          onChange={(e) => {
            setEmail(e.target.value);
            setSignupError('');
          }}
          disabled={disabled}
        />
        {email && !disabled && (
          <button
            className="clear-input-button"
            onClick={() => setEmail('')}
          >
            ×
          </button>
        )}
      </div>

      <label className="mt-4">New Password</label>
      <div className="password-input-container">
        <input
          type='password'
          className={`input ${disabled ? 'opacity-50' : ''}`}
          name='password'
          placeholder='Create a strong password'
          value={password}
          onChange={(e) => {
            setPassword(e.target.value);
            setSignupError('');
          }}
          disabled={disabled}
        />
      </div>
      {/* <small>
        Password must be at least 8 characters long
      </small> */}
      {signupError && (
        <div className="text-red-500 text-sm mt-2">
          {signupError}
        </div>
      )}
    </div>
  );

  const renderVerificationSection = () => (
    <>
      {renderEmailPasswordSection(true)}
      <div className='settings-input-group-two mt-8 border-t pt-8'>
        <div className='settings-input-group-h4'>Verification Code</div>
        <p className="verification-text">
          Enter the 6-digit verification code sent to {email}
        </p>
        <div className="verification-code-container">
          {verificationCode.map((digit, index) => (
            <input
              key={index}
              ref={verificationInputRefs[index]}
              type="tel"
              inputMode="numeric"
              pattern="[0-9]*"
              maxLength={6}
              value={digit}
              onChange={(e) => handleCodeChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              onPaste={(e) => handleCodePaste(e, index)}
              className={`verification-input ${isCodeValid ? 'border-green-500' : ''}`}
              autoComplete="one-time-code"
              disabled={isCodeValid} // Disable the inputs if the code is valid
            />
          ))}
        </div>
        {signupError && (
          <div className="text-red-500 text-sm mt-2">
            {signupError}
          </div>
        )}
        {isCodeValid && (
          <div className="text-green-500 text-xs mt-1 mb-3 verification-message">
            Verification complete! Click the button below to complete signup.
          </div>
        )}
      </div>
    </>
  );

  // Update the footer button logic
  const getFooterButtonText = () => {
    if (!isLoggedIn) {
      switch (signupStep) {
        case 'email-password':
          return 'Continue';
        case 'verify':
          return isCodeValid ? 'Save Now' : 'Verify Code';
        default:
          return 'Continue';
      }
    }
    return hasUnsavedChanges ? 'Save Changes' : 'Saved';
  };

  const handleFooterButtonClick = () => {
    if (!isLoggedIn) {
      switch (signupStep) {
        case 'email-password':
          handleSendVerificationCode();
          break;
        case 'verify':
          handleVerifyCode();
          break;
      }
    } else {
      console.log('Save changes');
      handleSave();
    }
  };

  const renderFooterButton = () => {
    if (signupStep === 'verify' && !isCodeValid) {
      return null;
    }
  
    return (
      <div className=''>
        <button
          className={`settings-footer-button ${isSubmitting ? 'opacity-50 cursor-not-allowed bg-blue-400' : ''}`}
          onClick={handleFooterButtonClick}
          disabled={(!isLoggedIn && !isEmailValid(email)) || isSubmitting}
        >
          {isSubmitting ? 'Loading...' : getFooterButtonText()}
        </button>
      </div>
    );
  };

  return (
    <div className='settings-container'>


      <div className='settings-panel'>
      <div className="claim-page-title">Configure your first tag!</div>
      <div className='thinkertag-preview-panel-claim'>

<div className='thinkertag-preview-qr-wrapper'>
  <Thinkertag
    value={tagStyle}
    tagId={tagId}
    url={`https://api.thinkertags.com/${tagId}`}
    size='small'
  />
</div>



<div className='settings-content-wrapper'>
  {isLoading ? (
    <div className='input-group'>
      <label>Tag Name</label>
      <div style={{ paddingTop: '1.5px' }}>
        <Skeleton height={41.5} borderRadius={3} />
      </div>
    </div>
  ) : (
    <div className='input-group'>
      <label>Tag Name</label>
      <Input
        className="input-v2"
        name="customName"
        placeholder="My ThinkerTag #1"
        value={tagData.customName}
        onChange={handleInputChange}
        autoComplete="off"
        dataFormType="other"
      />
    </div>
  )}

  <div className="divider">
    <div className="divider-right"></div>
  </div>


  {isLoading ? (
    <div className='input-group'>
      <label>Redirect</label>
      <div style={{ paddingTop: '1.5px' }}>
        <Skeleton height={41.5} borderRadius={3} />
      </div>
    </div>
  ) : (
    <div className='input-group'>
      <label>Redirect</label>
      <Input
        className="input-v2"
        name="publicUrl"
        placeholder="https://manage.mywebsite.com"
        value={tagData.publicUrl}
        type="url"
        enforceProtocol={true}
        onChange={(event) => {
          const { value } = event.target;
          setTagData(prevState => ({
            ...prevState,
            publicUrl: value
          }));
          setHasUnsavedChanges(true);
        }}
        onBlur={(event) => {
          setTimeout(() => {
            const newValue = ensureProtocol(event.target.value);
            setTagData(prevState => ({
              ...prevState,
              publicUrl: newValue
            }));
          }, 150);
        }}
      />
    </div>
  )}


</div>

</div>

        


        {activeSegment === 'Tag' && (
          <div className='settings-tab-one-content'>



            {(
              <>
                {signupStep === 'email-password' && renderEmailPasswordSection(false)}
                {signupStep === 'verify' && 
                (<div ref={verificationSectionRef}>
                  {renderVerificationSection()}
                </div>)
                }
              </>
            )}
          </div> // End of settings-tab-one-content
        )}
      </div>
      {renderFooterButton()}
      {/* <div className='settings-footer'>
        
      </div> */}

    </div>
  );
};

export { TagPageClaim };