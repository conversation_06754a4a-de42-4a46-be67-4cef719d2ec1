import React, { useState } from 'react';
import { Auth } from 'aws-amplify';
import { useNavigate } from 'react-router-dom';  


const Signup = ({ setScreen }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [code, setCode] = useState('');
  const [verifying, setVerifying] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = (e) => {
    e.preventDefault();
    if (verifying) {
      Auth.confirmSignUp(email, code)
        .then(() => {
          alert("Verification successful! You can now log in.");
          navigate ('/');
        })
        .catch((e) => alert(e.message));
    } else {
      Auth.signUp({
        username: email,
        password,
      })
        .then(() => {
          alert("Sign-up successful! Please check your email for the verification code.");
          setVerifying(true);
        })
        .catch((e) => alert(e.message));
    }
  };

  return (
    <div>
      <form onSubmit={handleSubmit}>
        <input
          type="email"
          placeholder="Email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          autoComplete="email"
        />
        <input
          type="password"
          placeholder="Password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
          autoComplete="new-password"
        />
        {verifying && (
          <input
            type="text"
            placeholder="Verification Code"
            value={code}
            onChange={(e) => setCode(e.target.value)}
            required
            autoComplete="one-time-code"
          />
        )}
        <button type="submit">
          {verifying ? "Verify" : "Sign up"}
        </button>
      </form>
      <span onClick={() => navigate ('/login')}>
        Already have an account? Login
      </span>
    </div>
  );
};

export default Signup;