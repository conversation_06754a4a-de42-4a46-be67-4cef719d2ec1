import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Auth, API } from 'aws-amplify';
import Skeleton from 'react-loading-skeleton';
import { useCookies } from 'react-cookie';
import { ToggleSwitch } from '../components/ToggleSwitch';
import '../styles/TagList.css';

export const AssetList = () => {
  const navigate = useNavigate();
  const [cookies] = useCookies(['location_mode']);
  const [assets, setAssets] = useState([]);
  const [assetCount, setAssetCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [activeFilter, setActiveFilter] = useState(null); // null, 'noTags', or 'withTags'
  const [loading, setLoading] = useState(true);
  const [noResults, setNoResults] = useState(false);

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);
    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Fetch assets based on debounced search
  useEffect(() => {
    const fetchAssets = async () => {
      setLoading(true);
      setNoResults(true);

      try {
        const session = await Auth.currentSession();
        const headers = {
          Authorization: `Bearer ${session.getAccessToken().getJwtToken()}`,
          'id-Token': session.getIdToken().getJwtToken(),
        };

        // Build query parameters
        const queryParams = new URLSearchParams();

        // If user typed something, pass it along
        if (debouncedSearchTerm) {
          queryParams.append('search', debouncedSearchTerm);
        }

        // Add filter to query params if active
        if (activeFilter === 'noTags') {
          queryParams.append('tagsCount', '0');
        } else if (activeFilter === 'withTags') {
          queryParams.append('tagsCount', '1');
        }

        // Here we request a big limit to get a "full page"
        queryParams.append('limit', '20');

        const url = `/assets${queryParams.toString() ? `?${queryParams.toString()}` : ''
          }`;

        const response = await API.get('api', url, { headers });

        if (response && response.items) {
          let fetchedAssets = response.items;

          // Sort by descending tagsCount
          fetchedAssets.sort((a, b) => ((b.tagsCount || 0) - (a.tagsCount || 0)));

          // No need to filter client-side since we're sending the filter in the API query params
          // But we'll keep this as a fallback in case the API doesn't support filtering
          if (activeFilter === 'noTags' && !queryParams.has('tagsCount')) {
            fetchedAssets = fetchedAssets.filter(
              (g) => (g.tagsCount || 0) === 0
            );
          } else if (activeFilter === 'withTags' && !queryParams.has('tagsCount')) {
            fetchedAssets = fetchedAssets.filter(
              (g) => (g.tagsCount || 0) === 1
            );
          }

          setAssets(fetchedAssets);
          setNoResults(fetchedAssets.length === 0);
        } else {
          setAssets([]);
          setNoResults(true);
        }
      } catch (error) {
        console.error('Error fetching assets:', error);
        setNoResults(true);
      } finally {
        setLoading(false);
      }
    };

    fetchAssets();
  }, [
    debouncedSearchTerm,
    cookies.location_mode,
    activeFilter,
  ]);

  // Fetch asset count once on mount (cached in state)
  useEffect(() => {
    const fetchAssetCount = async () => {
      try {
        const session = await Auth.currentSession();
        const headers = {
          Authorization: `Bearer ${session.getAccessToken().getJwtToken()}`,
          'id-Token': session.getIdToken().getJwtToken(),
        };

        const response = await API.get('api', '/assets/count', { headers });
        if (response?.count != null) {
          setAssetCount(response.count);
        }
      } catch (error) {
        console.error('Error fetching asset count:', error);
      }
    };
    fetchAssetCount();
  }, []);

  // Switch to "Tags" if toggle is changed
  const handleToggleChange = (option) => {
    if (option === 'Tags') {
      // Add a small delay to allow toggle animation to finish
      setTimeout(() => {
        navigate('/tags');
      }, 250);
    }
  };

  const renderSkeletonItem = () => (
    <div className="tag-list-item">
      <div className="tag-info">
        <Skeleton width={100} height={15} />
        <Skeleton width={150} height={15} />
      </div>
      <div className="tag-id">
        <Skeleton width={30} height={15} />
      </div>
    </div>
  );

  const renderAssetItem = (asset) => (
    <div
      key={asset.id}
      onClick={() => navigate(`/assets/${asset.id}`)}
      className="tag-list-item"
    >
      <div className="tag-info">
        <div>{asset.name || asset.assetId || 'Asset Name'}</div>
        <p>{asset.description || 'Asset Address'}</p>
      </div>
      <div className="tag-id">{asset.tagsCount || 0}</div>
    </div>
  );

  return (
    <div className="settings-container">
      {/* Title + Count */}
      <div className="title">
        <h2>Assets</h2>
        <div className="count-container">
          <div className="count">
            {loading ? <Skeleton width={30} /> : assetCount}
          </div>
          <div className="count-color"></div>
        </div>
      </div>

      {/* Toggle Switch */}
      <ToggleSwitch
        options={['Tags', 'Assets']}
        activeOption="Assets"
        onChange={handleToggleChange}
      />

      {/* Search Input */}
      <div className="search-container">
        <input
          className="search-input"
          type="text"
          value={searchTerm}
          placeholder="Search all assets"
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      <div className="filter-row" style={{
        display: 'flex',
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        alignItems: 'center',
        margin: '10px 0',
        width: '100%'
      }}>
        {/* Filter Row - Filter Buttons */}
        <div className="filter-buttons">

          <button
            className={`filter-button ${activeFilter === 'withTags' ? 'active' : ''}`}
            onClick={() => setActiveFilter(activeFilter === 'withTags' ? null : 'withTags')}
          >
            Tagged
          </button>

          <button
            className={`filter-button ${activeFilter === 'noTags' ? 'active' : ''}`}
            onClick={() => setActiveFilter(activeFilter === 'noTags' ? null : 'noTags')}
          >
            Untagged
          </button>
        </div>

        {/* Create New Asset */}
      </div>

      {/* Asset List */}
      <div className="tag-list">
        {loading ? (
          // Show 5 skeleton items
          Array.from({ length: 5 }).map((_, index) => (
            <div key={index}>{renderSkeletonItem()}</div>
          ))
        ) : assets.length > 0 ? (
          assets.map(renderAssetItem)
        ) : (
          <div className="no-results">No assets found</div>
        )}
      </div>
    </div>
  );
};