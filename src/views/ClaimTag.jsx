import React, { useState, useEffect, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Auth, API } from "aws-amplify";
import { useParams } from 'react-router-dom';
import '../styles/ClaimTag.css';
import { Thinkertag } from '../components/Thinkertag';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import { animate } from 'motion';

const ClaimTag = () => {
  const { tagId } = useParams();
  const [tagStyle, setTagStyle] = useState("skeleton");
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const [scale, setScale] = useState(0.7); // State variable to control the scale

  const radius = 156 * scale;
  const circumference = 2 * Math.PI * radius;
  const textLength = circumference;

  const navigate = useNavigate();



  const arrowRef = useRef(null);
  useEffect(() => {
    if (!arrowRef.current) return;
    
    const wiggleEffect = () => {
      if (!arrowRef.current) return;
      const currentTransform = getComputedStyle(arrowRef.current).transform;
      return animate(
        arrowRef.current,
        { transform: [
          currentTransform,
          `translateX(4px)`, 
          `translateX(-4px)`, 
          `translateX(3px)`,
          `translateX(-3px)`,
          `translateX(2px)`,
          `translateX(-2px)`,
          `translateX(1px)`,
          `translateX(-1px)`,
          `translateX(0px)`
        ] },
        { 
          duration: 1.5,
          easing: 'easeInOut'
        }
      ).stop;
    };
    
    const initialTimeout = setTimeout(() => {
      const stopInitial = wiggleEffect();
      const interval = setInterval(wiggleEffect, 4000);
      
      return () => {
        stopInitial?.();
        clearInterval(interval);
      };
    }, 1500);
  
    return () => clearTimeout(initialTimeout);
  }, []);

  useEffect(() => {

    const checkAuthStatus = async () => {
      try {
        const user = await Auth.currentAuthenticatedUser();
        setIsLoggedIn(true);
      } catch (error) {
        setIsLoggedIn(false);
      }
    };

    const fetchTagStyle = async () => {
      setIsLoading(true);
      try {
        const response = await API.get('public-api', `/tags/${tagId}/claim`);
        if (response && response.style) {
          console.log('Tag style fetched:', response.style);
          setTagStyle(response.style || "gold");
        } else {
          console.warn('Failed to fetch tag style, defaulting to gold');
          setTagStyle("gold");
        }
      } catch (error) {
        console.error('Error fetching tag style:', error);
        console.warn('Defaulting to gold due to error');
        setTagStyle("gold");
      } finally {
        setIsLoading(false);
      }
    };

    fetchTagStyle();
    checkAuthStatus();
  }, [tagId]);

  const ClaimTag = async () => {

    if (!isLoggedIn) {
      navigate(`/claim/${tagId}/setup`);
    } else {
      const session = await Auth.currentSession();
      try {
        const response = await API.post('api', `/tags/${tagId}/claim`, {
          headers: {
            Authorization: `Bearer ${session.getAccessToken().getJwtToken()}`,
            'id-Token': session.getIdToken().getJwtToken()
          }
        })

        if (response) {
          console.log(response);
          navigate(`/tags/${tagId}`);
        } else {
          console.log('Failed to claim tag');
        }
      } catch (error) {
        console.error('Error claiming tag:', error);
      }

    }

  };


  useEffect(() => {
    const qrCodeGroup = document.querySelector('.claim-tag-qr-code');
    const numberOfParticles = 10;

    // Create a particleContainer div
    const particleContainer = document.createElement('div');
    particleContainer.classList.add('particle-container');

    const colors = ['#6058E8', '#2BD07B', '#F14C4D', '#F14CA2'];
    const centerX = window.innerWidth / 2;
    const centerY = 170;
    const radius = 120;

    for (let i = 0; i < numberOfParticles; i++) {
      const particle = document.createElement('div');

      // Decide the style of the particle
      const styles = ['particle', 'particle-bordered', 'particle-cross'];
      const randomStyle = styles[Math.floor(Math.random() * styles.length)];
      particle.classList.add(randomStyle);

      // Determine the size and color of the particle
      const size = Math.random() * 5 + 9; // Between 5px and 15px
      const color = colors[Math.floor(Math.random() * colors.length)];

      // Calculate particle position in a circular pattern
      const angle = (2 * Math.PI / numberOfParticles) * i;
      const left = centerX + radius * Math.cos(angle) - size / 2;
      const top = centerY + radius * Math.sin(angle) - size / 2;

      // Apply the styles
      particle.style.width = `${size}px`;
      particle.style.height = `${size}px`;

      // Apply color based on the style
      if (randomStyle === 'particle') {
        particle.style.backgroundColor = color;
      } else if (randomStyle === 'particle-bordered') {
        particle.style.borderColor = color;
      } else if (randomStyle === 'particle-cross') {
        particle.style.color = color;
      }

      particle.style.left = `${left}px`;
      particle.style.top = `${top}px`;

      // Append the particle to the particleContainer
      particleContainer.appendChild(particle);
    }

    // Append the particleContainer to the QR code group
    qrCodeGroup.appendChild(particleContainer);

    particleContainer.childNodes.forEach(particle => {
      const initialLeft = parseFloat(particle.style.left);
      const initialTop = parseFloat(particle.style.top);

      // Store initial position
      particle.setAttribute('data-initial-left', initialLeft);
      particle.setAttribute('data-initial-top', initialTop);

      // Calculate direction
      const directionX = (initialLeft - centerX) / centerX;
      const directionY = (initialTop - centerY) / centerY;

      // Set CSS variables for direction
      particle.style.setProperty('--direction-x', `${directionX}`);
      particle.style.setProperty('--direction-y', `${directionY}`);
    });



    //Hide logo
    const isometricNavbar = document.querySelector('.isometric-navbar');
    const logoContainer = document.querySelector('.logo-container');
    if (logoContainer) {
      logoContainer.style.display = 'none';
    }
    if (isometricNavbar) {
      isometricNavbar.style.display = 'none';
    }

    // Cleanup function (optional but recommended)
    return () => {
      // Remove particles if necessary or any other cleanup task
      particleContainer.remove();
    };

  }, []);


  return (

    <div>
      <div className='settings-header'>
        <Link to='/tags' className='close-window-button'>
          <img src="/close.svg" alt="Close" />
        </Link>
      </div>
      
      <div className='settings-panel-claim'>

        <div className='rotating-text-div'>
          <svg width={400 * scale} height={400 * scale} xmlns="http://www.w3.org/2000/svg">
            <path id="circlePath"
              d={`M ${200 * scale}, ${200 * scale}
          m -${radius}, 0
          a ${radius},${radius} 0 1,1 ${2 * radius},0
          a ${radius},${radius} 0 1,1 -${2 * radius},0`}
              fill="none" />
            <text fill='#E1DBDB' fontSize={9} fontWeight={600}>
              <textPath href="#circlePath" startOffset="0" textLength={textLength} lengthAdjust="spacing">
                THINKERTAGS X THINKERTAGS X THINKERTAGS X &nbsp;
              </textPath>
            </text>
          </svg>
        </div>
        <div className='round-backdrop-container'>
          <div className='round-backdrop-clone'></div>



          <div className='claim-tag-qr-code'>
            {isLoading ? (
              <div style={{ margin: '10px' }}>
                <Skeleton width={142} height={142} borderRadius={10} />
              </div>
            ) : (
              <Thinkertag
                value={tagStyle}
                tagId={tagId}
                url={`https://api.thinkertags.com/${tagId}`}
                
              />
            )}
          </div>

          <div className='round-backdrop-clone'></div>
        </div>

        <div>
          <h1 className='settings-header-titles'>You scanned a new tag!</h1>
        </div>



        <div className='claim-message'>
          {isLoggedIn
            ? "You are already logged in. Ready to set up."
            : "Ready to set up."}
        </div>

        <button className='settings-panel-body-bottom-claim-button' onClick={ClaimTag}>
          <div className='claim-button-label'>
            <div className='claim-button-label-text'>{isLoggedIn
              ? "Set Up"
              : "Try Now"}</div>
            <div className="arrow-forward-svg" ref={arrowRef}></div>
          </div>
        </button>
      </div>

    </div>
  );
};

export { ClaimTag };


