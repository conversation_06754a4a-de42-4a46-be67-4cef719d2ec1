import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App";
import "./index.css";
import "./styles/fonts.css"; // Import custom fonts
import {Amplify} from "aws-amplify";
import { CookiesProvider } from 'react-cookie';
import { SteppingModeProvider } from './components/SteppingModeContext';

const endpoint = import.meta.env.VITE_API_ENDPOINT;
//console.debug('API Endpoint:', endpoint);
const public_endpoint = import.meta.env.VITE_PUBLIC_API_ENDPOINT;
//console.debug('Public API Endpoint:', public_endpoint);

Amplify.configure({
  Auth: {
    region: 'eu-central-1',
    userPoolId: 'eu-central-1_C8GLqXRKT',
    userPoolWebClientId: 'emv4fthi0nj1g2q9d24qieu23',
  },
  API: {
    endpoints: [
      {
        name: "api",
        endpoint: endpoint,
        region: "eu-central-1",
      },
      {
        name: "public-api",
        endpoint: public_endpoint,
        region: "eu-central-1",
      }
    ],
  },
});

ReactDOM.createRoot(document.getElementById("root")).render(
  <React.StrictMode>
    <CookiesProvider>
      <SteppingModeProvider>
        <App />
      </SteppingModeProvider>
    </CookiesProvider>
  </React.StrictMode>
);