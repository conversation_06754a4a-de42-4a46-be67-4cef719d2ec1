/**
 * <PERSON><PERSON><PERSON> to generate a sitemap.xml file for Thinkertags
 *
 * This script:
 * 1. Scans the /public/blog directory for blog posts
 * 2. Adds all public routes from the App.jsx file
 * 3. Generates a sitemap.xml file in the public directory
 *
 * Usage: node scripts/generate-sitemap.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { promisify } from 'util';

const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const writeFile = promisify(fs.writeFile);

// Get the directory name in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Base URL for the site
const BASE_URL = 'https://thinkertags.com';

// Public routes to include (these should match the routes in App.jsx)
const PUBLIC_ROUTES = [
  { path: '/', changefreq: 'daily', priority: '1.0' },
  { path: '/login', changefreq: 'monthly', priority: '0.5' },
  { path: '/f/ea/operations-scale/introduction', changefreq: 'monthly', priority: '0.7' },
];

/**
 * Recursively scans a directory for files with a specific extension
 * @param {string} dir - The directory to scan
 * @param {string} ext - The file extension to look for
 * @returns {Promise<string[]>} - Array of file paths
 */
async function findFiles(dir, ext) {
  const subdirs = await readdir(dir);
  const files = await Promise.all(subdirs.map(async (subdir) => {
    const res = path.resolve(dir, subdir);
    return (await stat(res)).isDirectory() ? findFiles(res, ext) : res;
  }));
  return files.flat().filter(file => file.endsWith(ext));
}

/**
 * Extracts the slug from a blog post file path
 * @param {string} filePath - The file path
 * @returns {string} - The slug
 */
function extractSlug(filePath) {
  // Handle both old and new blog post structures
  const filename = path.basename(filePath, '.md');
  const dirname = path.basename(path.dirname(filePath));

  // If the directory name is 'blog', it's using the old structure
  if (dirname === 'blog') {
    return filename;
  }

  // Otherwise, it's using the new structure (dirname should be the slug)
  return dirname;
}

/**
 * Checks if a blog post should be excluded from the sitemap
 * @param {string} slug - The blog post slug
 * @returns {boolean} - True if the post should be excluded
 */
function shouldExcludeBlogPost(slug) {
  // List of sample or test posts to exclude
  const excludedSlugs = [
    'sample-post',
    'top-3-timeless-principles',
    'the-future-of-asset-tracking-with-thinkertags',
    'the-conundrums-of-better-quality' // Exclude from blog scan as we add it manually
  ];

  return excludedSlugs.includes(slug);
}

/**
 * Generates the sitemap XML content
 * @param {Array} urls - Array of URL objects with path, changefreq, and priority
 * @returns {string} - The sitemap XML content
 */
function generateSitemapXml(urls) {
  let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
  xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';

  urls.forEach(url => {
    xml += '  <url>\n';
    xml += `    <loc>${url.loc}</loc>\n`;
    if (url.changefreq) {
      xml += `    <changefreq>${url.changefreq}</changefreq>\n`;
    }
    if (url.priority) {
      xml += `    <priority>${url.priority}</priority>\n`;
    }
    xml += '  </url>\n';
  });

  xml += '</urlset>';
  return xml;
}

/**
 * Main function to generate the sitemap
 */
async function generateSitemap() {
  try {
    // Get project root directory (2 levels up from scripts directory)
    const projectRoot = path.resolve(__dirname, '..');

    // Get all blog post files
    const blogDir = path.join(projectRoot, 'public', 'blog');
    const blogFiles = await findFiles(blogDir, '.md');

    // Extract slugs and create URL objects for blog posts
    const blogUrls = blogFiles
      .map(file => extractSlug(file))
      .filter(slug => !shouldExcludeBlogPost(slug))
      .map(slug => ({
        loc: `${BASE_URL}/research-and-insights/${slug}`,
        changefreq: 'monthly',
        priority: '0.8'
      }));

    // Create URL objects for public routes
    const publicUrls = PUBLIC_ROUTES.map(route => ({
      loc: `${BASE_URL}${route.path}`,
      changefreq: route.changefreq,
      priority: route.priority
    }));

    // Add specific research and insights article
    const specificArticles = [
      {
        loc: `${BASE_URL}/research-and-insights/the-conundrums-of-better-quality`,
        changefreq: 'monthly',
        priority: '0.8'
      }
    ];

    // Combine all URLs
    const allUrls = [...publicUrls, ...blogUrls, ...specificArticles];

    // Generate the sitemap XML
    const sitemapXml = generateSitemapXml(allUrls);

    // Write the sitemap to the public directory
    const sitemapPath = path.join(projectRoot, 'public', 'sitemap.xml');
    await writeFile(sitemapPath, sitemapXml);

    console.log(`Sitemap generated with ${allUrls.length} URLs at ${sitemapPath}`);
  } catch (error) {
    console.error('Error generating sitemap:', error);
  }
}

// Run the script
generateSitemap();
