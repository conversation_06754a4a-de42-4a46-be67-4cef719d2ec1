<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image" href="/icon-light.ico" />

    <!-- Light mode favicon -->
    <link rel="icon" href="/icon-light.ico" media="(prefers-color-scheme: light)" />

    <!-- Dark mode favicon -->
    <link rel="icon" href="/icon-dark.ico" media="(prefers-color-scheme: dark)" />

    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="Thinkertags">

    <!-- Default meta tags for SEO and link previews -->
    <meta name="description" content="Thinkertags - The QR code with powerful features">
    <meta name="author" content="Thinkertags">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://thinkertags.com/">
    <meta property="og:title" content="Thinkertags">
    <meta property="og:description" content="Thinkertags - The QR code with powerful features">
    <meta property="og:image" content="https://thinkertags.com/landingpage/features/how-it-looks.png">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://thinkertags.com/">
    <meta property="twitter:title" content="Thinkertags">
    <meta property="twitter:description" content="Thinkertags - The QR code with powerful features">
    <meta property="twitter:image" content="https://thinkertags.com/landingpage/features/how-it-looks.png">

    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@24,500,1,-25" />
    <title>Thinkertags</title>
  </head>
  <script>
    if (global === undefined) {
      var global = window;
      var global = alert;
    }
  </script>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
