AWSTemplateFormatVersion: '2010-09-09'
Resources:
  MySSLCertificate:
    Type: 'AWS::CertificateManager::Certificate'
    Properties:
      DomainName: 'thinkertags.com'
      SubjectAlternativeNames:
        - 'thinkertag.com'
        - 'thinkertags.fr'
        - 'thinkertags.de'
        - 'thinkertags.es'
        - 'thinkertags.co.uk'
      ValidationMethod: 'DNS'
      DomainValidationOptions:
        - DomainName: 'thinkertags.com'
          HostedZoneId: 'Z031034829BBKB8OEMGP8'
        - DomainName: 'thinkertag.com'
          HostedZoneId: 'Z0778787SHNSH6ECR8NM'
        - DomainName: 'thinkertags.fr'
          HostedZoneId: 'Z081683134LRVJQUU53Y8'
        - DomainName: 'thinkertags.de'
          HostedZoneId: 'Z08035121B5PGX1XE3NFU'
        - DomainName: 'thinkertags.es'
          HostedZoneId: 'Z0206211213U3VMGAQP9A'
        - DomainName: 'thinkertags.co.uk'
          HostedZoneId: 'Z0500581322DOO13FAKZK'                             
      
Outputs:
  ConsoleAcmCertArn:
    Description: The ARN of the created ACM certificate
    Value: !Ref MySSLCertificate
    Export:
      Name: ConsoleAcmCertArn